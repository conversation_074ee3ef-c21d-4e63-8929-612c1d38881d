# Voice Integration - Connection Issues & Fix Plan

## 🔴 Current Status: Connection Failing

The voice integration is showing **"Connection Lost" error** with max reconnection attempts being hit.

## 🔍 Root Cause Analysis

### Issue 1: Wrong WebSocket Endpoint
**Current (Wrong):**
```
wss://puzjricwpsjusjlgrwen.functions.supabase.co/realtime-chat
```

**Should be (Correct):**
```
wss://api.openai.com/v1/realtime
```

The code is trying to connect to a Supabase edge function that:
- Doesn't exist or isn't configured
- Doesn't have proper authentication
- Can't proxy OpenAI Realtime API correctly

### Issue 2: Missing Authentication
The WebSocket connection is missing:
- OpenAI API key in Authorization header
- No Bearer token for authentication
- No environment variable configuration

### Issue 3: WebRTC Audio Not Fully Integrated
- `RealtimeAudio.ts` has comprehensive optimization implemented
- But it's not being properly utilized in VoiceAgent
- Network monitoring isn't affecting actual bitrate

### Issue 4: Poor Error Messages
- Generic "Connection Lost" error with no debugging info
- No indication of what the actual problem is
- No actionable steps for user recovery

## ✅ Fix Plan Created

A comprehensive PRP-based fix plan has been created with 8 tasks:

### Phase 1: Critical Fixes (Must do first)
1. ✏️ **Fix WebSocket endpoint** → Connect to OpenAI Realtime API directly
2. ✏️ **Add API authentication** → Implement Bearer token with API key
3. ✏️ **Fix WebRTC integration** → Properly use audio optimization

### Phase 2: Enhanced Features
4. ✏️ **Better error messages** → Specific, actionable error feedback
5. ✏️ **Network detection** → Auto-reconnect when connection restored

### Phase 3: Testing & Docs
6. ✏️ **Add tests** → Unit tests for connection retry logic
7. ✏️ **Update docs** → Setup guide and troubleshooting
8. ✏️ **End-to-end testing** → Test with real OpenAI API

## 📋 Archon Tasks Created

**Project:** Voice Integration Connection Fix
**Total Tasks:** 8
**Status:** All pending

### Critical Path Tasks (Do These First)
1. `CRITICAL: Fix WebSocket endpoint to use OpenAI Realtime API` (Task Order: 100)
2. `CRITICAL: Implement OpenAI API authentication` (Task Order: 99)
3. `Fix WebRTC audio integration with network optimization` (Task Order: 95)

### Important Tasks
4. `Improve error messages and connection feedback` (Task Order: 90)
5. `Add network status detection and automatic reconnection` (Task Order: 88)

### Support Tasks
6. `Create connection and error recovery tests` (Task Order: 85)
7. `Update documentation with setup and troubleshooting guide` (Task Order: 80)
8. `Test voice integration end-to-end with real OpenAI API` (Task Order: 75)

## 🚀 Quick Start to Fix

### Step 1: Get OpenAI API Key
1. Go to https://platform.openai.com/account/api-keys
2. Create new API key
3. Copy the key

### Step 2: Create Environment File
Create `.env` file in project root:
```env
VITE_OPENAI_API_KEY=sk-your-key-here
VITE_OPENAI_REALTIME_ENDPOINT=wss://api.openai.com/v1/realtime
```

### Step 3: Update WebSocket Connection
In `src/components/VoiceAgent.tsx`, change the `connectWebSocket` function:

**Current (Line 303):**
```typescript
const wsUrl = `wss://puzjricwpsjusjlgrwen.functions.supabase.co/realtime-chat`;
wsRef.current = new WebSocket(wsUrl);
```

**Change to:**
```typescript
const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
const wsUrl = import.meta.env.VITE_OPENAI_REALTIME_ENDPOINT || 'wss://api.openai.com/v1/realtime';

wsRef.current = new WebSocket(wsUrl);
wsRef.current.onopen = () => {
  // Send authentication
  const setupMessage = {
    type: 'session.update',
    session: {
      model: 'gpt-4-realtime-preview',
      instructions: 'You are a voice assistant for managing seafood inventory...',
      input_audio_format: 'pcm16',
      output_audio_format: 'pcm16',
      voice: 'alloy',
      turn_detection: { type: 'server_vad' },
      temperature: 1,
      max_response_output_tokens: 4096,
      tools: [
        // ... existing tool definitions ...
      ]
    }
  };

  if (wsRef.current?.readyState === WebSocket.OPEN) {
    wsRef.current.send(JSON.stringify(setupMessage));
  }

  // Continue with existing onopen logic...
};
```

### Step 4: Test Connection
1. Create `.env` with your API key
2. Run `npm run dev`
3. Click "Start Voice Chat"
4. Should connect without "Connection Lost" error
5. Speak a voice command to test

## 📊 Success Metrics

After fixes are complete:

✅ WebSocket connects successfully (no "Connection Lost")
✅ Audio captures properly (WebRTC working)
✅ Voice commands are recognized (OpenAI API responding)
✅ Tool execution works (inventory operations execute)
✅ Graceful reconnection on network failure
✅ Clear error messages for troubleshooting
✅ >90% test coverage of connection code

## 📚 Documentation

- **VOICE_FIX_PLAN.md** - Full implementation plan with architecture
- **VOICE_COMPLETION_SUMMARY.md** - Previous completion status
- **docs/VOICE_INTEGRATION.md** - User guide
- **docs/VOICE_API_REFERENCE.md** - API documentation
- **docs/VOICE_TESTING_GUIDE.md** - Testing procedures

## 🎯 Next Actions

1. **Read VOICE_FIX_PLAN.md** - Understand full scope
2. **Check Archon project** - See all 8 tasks in task management system
3. **Start Task 1** - Fix WebSocket endpoint
4. **Update .env configuration** - Add OpenAI API key
5. **Test connection** - Verify WebSocket connects to OpenAI API
6. **Iterate through remaining tasks** - Complete phases 1, 2, 3

## ⏱️ Estimated Time

- Phase 1 (Critical fixes): 30 minutes
- Phase 2 (Features): 1 hour
- Phase 3 (Testing & Docs): 1.5 hours
- **Total: 2.5-3 hours**

## 🔗 Related Files

- `src/components/VoiceAgent.tsx` - Main voice component
- `src/utils/RealtimeAudio.ts` - WebRTC audio handling
- `src/lib/voice-error-handler.ts` - Error classification
- `src/lib/voice-audit-logger.ts` - Audit logging
- `.env.example` - Configuration template (to create)

---

**Created:** October 24, 2025
**Status:** Planning Phase
**Archon Project ID:** 0dc3bec8-c768-4671-83b8-764e9d0b782e
**Priority:** High (Blocking Feature)
