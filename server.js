import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

/**
 * Ephemeral Key Endpoint
 * Generates short-lived OpenAI API keys for WebRTC connections
 */
app.post('/api/voice/ephemeral-key', async (req, res) => {
  try {
    const apiKey = process.env.OPENAI_API_KEY;

    if (!apiKey) {
      console.error('❌ OPENAI_API_KEY not set in environment');
      return res.status(500).json({
        error: 'OpenAI API key not configured',
        hint: 'Set OPENAI_API_KEY in your .env file'
      });
    }

    console.log('🔑 Generating ephemeral key...');

    // Generate ephemeral key from OpenAI
    const response = await fetch('https://api.openai.com/v1/realtime/client_secrets', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session: {
          type: 'realtime',
          model: 'gpt-realtime'
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ OpenAI API error:', errorText);
      return res.status(response.status).json({
        error: `OpenAI API error: ${response.statusText}`,
        details: errorText
      });
    }

    const data = await response.json();

    console.log('✅ Ephemeral key generated', {
      value: data.value.substring(0, 10) + '...',
      expiresAt: data.expires_at ? new Date(data.expires_at * 1000).toISOString() : 'Unknown',
      expiresIn: data.expires_at ? `${Math.round((data.expires_at * 1000 - Date.now()) / 1000)}s` : 'Unknown'
    });

    res.json(data);
  } catch (error) {
    console.error('❌ Error generating ephemeral key:', error);
    res.status(500).json({
      error: error.message || 'Failed to generate ephemeral key'
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    hasOpenAiKey: !!process.env.OPENAI_API_KEY
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📡 Ephemeral key endpoint: http://localhost:${PORT}/api/voice/ephemeral-key`);
  console.log(`🔑 OpenAI API Key: ${process.env.OPENAI_API_KEY ? '✅ Set' : '❌ Not Set'}`);
});
