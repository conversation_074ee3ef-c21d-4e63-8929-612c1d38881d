# Voice Integration WebRTC Migration - Feature Specification

## FEATURE

Migrate voice agent from WebSocket to WebRTC transport for OpenAI Realtime API to achieve lower latency, better audio quality, and simpler architecture. This migration addresses the current issue where the voice agent connects but doesn't respond to spoken prompts, while also implementing OpenAI's recommended browser architecture.

**Goals:**
1. Fix voice agent response issue (agent currently doesn't reply to speech)
2. Reduce latency by 50-100ms through direct peer-to-peer connection
3. Simplify architecture by removing server-side WebSocket bridge
4. Implement OpenAI's recommended approach for browser-based applications
5. Maintain all existing features (tool execution, network monitoring, error handling)

---

## EXAMPLES

### WebSocket Implementation (Current - To Replace)
- **Current connection**: `src/components/VoiceAgent.tsx` (lines 538-717)
  - `connectWebSocket()` function with manual WebSocket setup
  - Authentication via query parameter with API key
  - Manual audio encoding/decoding via `RealtimeAudio.ts`
  - Message handling in `handleMessage()` (lines 405-495)

### Audio Processing (Keep and Adapt)
- **Audio optimization**: `src/utils/RealtimeAudio.ts`
  - AudioRecorder class with jitter buffer (lines 24-330)
  - Network condition monitoring (lines 251-262)
  - Audio encoding: `encodeAudioForAPI()` (lines 332-349)
  - Audio playback: `playAudioData()` (lines 460-465)
  - Echo cancellation, noise suppression (lines 52-73)

### Tool Execution (Keep Unchanged)
- **Tool handling**: `src/components/VoiceAgent.tsx` (lines 269-403)
  - `handleToolCall()` async function
  - Integration with `VoiceToolExecutor`
  - Tool feedback UI via `VoiceToolFeedback` component

### Network Monitoring (Keep and Integrate)
- **Connection quality**: `src/components/VoiceAgent.tsx` (lines 210-267)
  - `monitorNetworkConditions()` function
  - Latency tracking with exponential moving average
  - Packet loss estimation
  - Bandwidth adaptation

### Error Handling (Keep and Enhance)
- **Error classification**: `src/components/VoiceAgent.tsx` (lines 62-175)
  - `ConnectionErrorType` enum
  - `getErrorMessage()` with user-friendly suggestions
  - Exponential backoff retry logic

### UI Components (Keep Unchanged)
- **Voice UI**: `src/components/VoiceAgent.tsx` (lines 920-1048)
  - Floating button interface
  - Connection quality indicator
  - Tool feedback display via `VoiceToolFeedback`
  - Status indicators (listening, speaking, processing)

---

## DOCUMENTATION

### OpenAI Realtime API
- **WebRTC Overview**: https://platform.openai.com/docs/guides/realtime#webrtc-connection
  - Official recommendation: "WebRTC connection: Ideal for browser and client-side interactions"
  - Session creation endpoint: `/v1/realtime/sessions`
  - Ephemeral key generation for browser safety

- **API Reference**: https://platform.openai.com/docs/api-reference/realtime
  - Session configuration
  - Audio format specifications (PCM16, 24kHz)
  - Event types and message format

- **Model Information**: https://platform.openai.com/docs/models/gpt-4o-realtime
  - Current model: `gpt-4o-realtime-preview-2024-10-01`
  - Recommended model: `gpt-4o-realtime-preview-2024-12-17` (latest)
  - Audio capabilities and limitations

### WebRTC Documentation
- **MDN WebRTC API**: https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API
  - RTCPeerConnection fundamentals
  - Creating offers and answers (SDP negotiation)
  - ICE candidate handling

- **MDN RTCPeerConnection**: https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection
  - Connection lifecycle
  - Event handlers (ontrack, onicecandidate, etc.)
  - Media stream management

- **MDN RTCDataChannel**: https://developer.mozilla.org/en-US/docs/Web/API/RTCDataChannel
  - Creating data channels for events
  - Message sending/receiving
  - Error handling

- **getUserMedia**: https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia
  - Audio constraints (already implemented correctly)
  - Echo cancellation, noise suppression

### React Patterns
- **useRef for WebRTC**: https://react.dev/reference/react/useRef
  - Storing RTCPeerConnection instance
  - Managing cleanup in useEffect

- **useEffect cleanup**: https://react.dev/reference/react/useEffect#cleaning-up
  - Closing peer connections
  - Stopping media streams

### Existing Project Documentation
- **WebRTC Migration Strategy**: `WEBRTC_MIGRATION_STRATEGY.md`
  - Full architectural comparison
  - Performance expectations (50-100ms improvement)
  - Migration risks and mitigation

- **Troubleshooting Plan**: `VOICE_WEBRTC_TROUBLESHOOTING_PLAN.md`
  - Phase-by-phase implementation guide
  - Debugging steps for current WebSocket
  - Code examples for WebRTC client

- **Voice Setup Guide**: `docs/VOICE_SETUP.md`
  - Environment configuration
  - API key management
  - Browser requirements

---

## OTHER CONSIDERATIONS

### Critical Requirements

**1. Ephemeral Key Service (Security)**
- Never expose permanent OpenAI API key in browser
- Create backend endpoint or Supabase Edge Function
- Generate ephemeral keys via OpenAI API: `POST /v1/realtime/sessions`
- Ephemeral keys are short-lived (~60 seconds TTL)
- Keys only work for Realtime API (not general OpenAI access)
- **File to create**: `src/lib/openai-ephemeral-keys.ts`
- **Backend endpoint**: Supabase function or Express endpoint

**2. WebRTC Client Implementation**
- Create reusable WebRTC client class
- Handle RTCPeerConnection lifecycle
- Manage data channels for OpenAI events
- Handle media streams for audio
- Implement SDP offer/answer negotiation
- Handle ICE candidates for NAT traversal
- **File to create**: `src/lib/realtime-webrtc-client.ts`

**3. Audio Stream Management**
- Use native MediaStream for audio (replace manual encoding)
- Keep echo cancellation, noise suppression, auto gain control
- Maintain jitter buffer for network optimization
- Adapt to network conditions (bandwidth, latency, packet loss)
- Handle bidirectional audio (microphone input + speaker output)

**4. Preserve Existing Features**
- Tool execution must work identically (inventory, CCP monitoring, etc.)
- Network monitoring must continue to function
- Error handling and retry logic must be maintained
- Connection quality indicators must work
- Audit logging must capture all events
- Tool feedback UI must display correctly

**5. Message Handling Migration**
- OpenAI events will arrive via RTCDataChannel (not WebSocket messages)
- Keep same event types: `response.audio.delta`, `response.audio_transcript.delta`, etc.
- Maintain tool call buffer for streaming function arguments
- Preserve session state tracking (message count, tool calls, errors)

**6. Browser Compatibility**
- Target: Chrome, Firefox, Safari, Edge
- WebRTC is standard in all modern browsers
- Test on both desktop and mobile
- Provide fallback error messages for unsupported browsers

**7. Testing Strategy**
- Unit tests: WebRTC connection, ephemeral key generation
- Integration tests: End-to-end voice conversation
- Performance tests: Measure latency before/after
- Network tests: Throttle to 3G, 4G, test reconnection
- Regression tests: Verify all existing features work

### Implementation Order

**Phase 1: Debug Current WebSocket (Optional but Recommended)**
1. Add enhanced logging to identify why agent doesn't respond
2. Test with latest model: `gpt-4o-realtime-preview-2024-12-17`
3. Verify audio playback queue is triggering
4. Test text responses (bypass audio to isolate issue)
5. Document findings for future reference

**Phase 2: Setup WebRTC Infrastructure**
1. Create ephemeral key service (backend)
2. Create WebRTC client class
3. Implement SDP negotiation
4. Set up data channels for events
5. Handle media streams for audio

**Phase 3: Refactor VoiceAgent Component**
1. Replace `wsRef` with `webrtcClientRef`
2. Replace `connectWebSocket()` with `connectWebRTC()`
3. Update session initialization
4. Remove manual audio streaming (WebRTC handles it)
5. Keep all existing UI and event handlers

**Phase 4: Testing & Validation**
1. Test connection establishment
2. Test audio bidirectional flow
3. Test tool execution with voice commands
4. Measure latency improvements
5. Test on multiple browsers
6. Run regression tests

**Phase 5: Documentation & Deployment**
1. Update VOICE_INTEGRATION.md with WebRTC architecture
2. Update VOICE_SETUP.md with ephemeral key setup
3. Update VOICE_TROUBLESHOOTING.md with WebRTC errors
4. Create migration completion document
5. Update inline code documentation

### Validation Commands

**Level 1: Syntax and Types**
```bash
npm run lint           # ESLint checks
npm run build:dev      # TypeScript compilation
```

**Level 2: Development Testing**
```bash
npm run dev            # Start dev server
# Test voice connection in browser
# Speak to agent and verify responses
# Test tool execution ("add 100kg salmon")
# Check browser console for errors
```

**Level 3: Network Testing**
```bash
# Use Chrome DevTools:
# 1. Open DevTools → Network tab
# 2. Enable "Network throttling"
# 3. Test on: Fast 3G, Slow 3G, Offline
# 4. Verify reconnection works
```

**Level 4: Browser Compatibility**
```bash
# Test on:
# - Chrome (primary development browser)
# - Firefox
# - Safari
# - Edge
# - Mobile browsers (if applicable)
```

### Success Criteria

**Must Have:**
- [ ] Voice agent responds to spoken prompts
- [ ] Audio flows bidirectionally (user → OpenAI → user)
- [ ] Tool execution works (addInventoryEvent, recordCCP, etc.)
- [ ] No regressions in existing features
- [ ] WebRTC connection establishes reliably
- [ ] Ephemeral keys generate successfully
- [ ] Error handling works (API key, network, microphone)
- [ ] All tests pass (lint, build, manual)

**Performance:**
- [ ] Latency reduced by at least 50ms (measure before/after)
- [ ] Audio quality is good or better than WebSocket
- [ ] No audio gaps or stuttering
- [ ] Reconnection works after network interruption

**Code Quality:**
- [ ] No TypeScript errors
- [ ] No ESLint warnings
- [ ] Code follows existing patterns
- [ ] Comprehensive error handling
- [ ] JSDoc documentation for new classes/functions

**Documentation:**
- [ ] VOICE_INTEGRATION.md updated with WebRTC
- [ ] VOICE_SETUP.md includes ephemeral key setup
- [ ] VOICE_TROUBLESHOOTING.md covers WebRTC errors
- [ ] Inline code comments explain WebRTC flow
- [ ] Migration completion document created

### Environment Variables

**Current (.env):**
```bash
VITE_OPENAI_API_KEY="sk-your-api-key-here"
VITE_OPENAI_REALTIME_ENDPOINT="wss://api.openai.com/v1/realtime"
```

**After Migration (may change):**
```bash
# Frontend (if needed for dev testing)
VITE_OPENAI_API_KEY="sk-your-api-key-here"  # May be removable after ephemeral keys

# Backend (Supabase or Express)
OPENAI_API_KEY="sk-your-api-key-here"  # For ephemeral key generation
```

### Known Gotchas

1. **Ephemeral Key TTL**: Keys expire quickly (~60s), handle expiration gracefully
2. **ICE Candidates**: May need STUN server for NAT traversal (usually default works)
3. **Audio Format**: Must match OpenAI's expected format (PCM16, 24kHz, mono)
4. **Data Channel Order**: Must establish data channel before sending events
5. **Browser Permissions**: WebRTC requires microphone permissions (same as WebSocket)
6. **SDP Negotiation**: Offer/answer must complete before media flows
7. **Connection State**: Monitor RTCPeerConnection state (connecting, connected, failed)
8. **Media Stream Cleanup**: Must stop tracks when disconnecting to release microphone

### Rollback Plan

If WebRTC migration fails:
1. Revert to previous commit (WebSocket implementation)
2. Keep debugging logs added in Phase 1
3. Fix WebSocket issue based on Phase 1 findings
4. Consider WebRTC migration for future iteration

### References

- **Archon Project**: Safe-catch-flow (a7206f24-59d5-4873-8a6a-01717ce3c95f)
- **Archon Tasks**: 5 tasks created (orders 100-80)
- **Current Model**: gpt-4o-realtime-preview-2024-10-01 (VoiceAgent.tsx:568)
- **Sample Rate**: 24kHz (VoiceAgent.tsx:500, 575)
- **Audio Format**: PCM16 (VoiceAgent.tsx:603)
