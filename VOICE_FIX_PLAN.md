# Voice Integration Fix Plan (PRP Framework)

## Problem Statement

The voice integration is throwing "Connection Lost" errors. Root causes identified:

1. **WebSocket endpoint mismatch**: Code tries to connect to `wss://puzjricwpsjusjlgrwen.functions.supabase.co/realtime-chat`
   - This appears to be a Supabase edge function that may not exist
   - No authentication being sent
   - No API key configuration

2. **Missing OpenAI Realtime API integration**:
   - Should connect directly to `wss://api.openai.com/v1/realtime`
   - Requires OpenAI API key in headers
   - Currently missing proper authentication

3. **WebRTC audio optimization not being utilized**:
   - `RealtimeAudio.ts` has comprehensive optimization but it's not being properly integrated
   - Network monitoring initialized but not affecting bitrate

4. **No proper error recovery for network failures**:
   - Max 5 reconnection attempts may be insufficient
   - No exponential backoff details shown to user
   - "Connection Lost" message lacks actionable guidance

## Solution Architecture

### Component 1: Fix WebSocket Endpoint & Authentication
- Replace Supabase edge function URL with OpenAI Realtime API endpoint
- Add proper Authorization header with OpenAI API key
- Implement session token exchange if needed

### Component 2: Enhance WebRTC Audio Integration
- Properly integrate `AudioRecorder` with network monitoring
- Apply dynamic bitrate adjustment based on network conditions
- Improve VAD (Voice Activity Detection) integration
- Add jitter buffer management

### Component 3: Improve Error Handling & Recovery
- Implement exponential backoff with better UX feedback
- Add network status monitoring (online/offline detection)
- Create graceful degradation for poor connections
- Provide actionable error messages

### Component 4: Add Environment Configuration
- Create `.env` setup for OpenAI API key
- Allow configurable endpoint selection
- Support multiple deployment environments

### Component 5: Testing & Validation
- Unit tests for connection retry logic
- Integration tests with mock WebSocket
- E2E tests with real OpenAI API (optional)
- Network failure scenario testing

## Implementation Tasks

### Phase 1: Critical Fixes (Must do first)
1. **[CRITICAL] Fix WebSocket endpoint** - Connect to OpenAI Realtime API
2. **[CRITICAL] Add API key authentication** - Implement proper auth headers
3. **[CRITICAL] Add environment configuration** - Create .env setup guide

### Phase 2: Enhanced Features
4. **Improve WebRTC integration** - Full audio optimization utilization
5. **Better error messages** - User-friendly connection failure feedback
6. **Network monitoring** - Real-time network quality indication

### Phase 3: Testing & Documentation
7. **Add unit tests** - Connection, retry logic, error handling
8. **Update documentation** - Setup guide, troubleshooting, environment config
9. **Create setup script** - Automate .env configuration

## Success Criteria

✅ Voice connection successfully establishes (no "Connection Lost" error)
✅ Audio streams properly to OpenAI API
✅ Voice commands are recognized and processed
✅ Tool execution works with proper feedback
✅ Graceful reconnection on network failure
✅ Clear error messages for debugging
✅ Comprehensive test coverage (>90%)
✅ Users can set up voice with simple `.env` configuration

## Estimated Timeline

- Phase 1: 30 minutes
- Phase 2: 1 hour
- Phase 3: 1.5 hours
- **Total: ~2.5-3 hours**

## Key Files to Modify

### Core Implementation
- `src/components/VoiceAgent.tsx` - WebSocket connection logic
- `src/utils/RealtimeAudio.ts` - Audio integration improvements
- `src/lib/voice-error-handler.ts` - Better error messaging
- `.env.example` - API key configuration template

### Testing
- `src/test/voice-connection.test.ts` (NEW) - Connection tests
- `src/test/voice-error-recovery.test.ts` (NEW) - Retry logic tests
- `docs/VOICE_SETUP.md` (UPDATE) - Environment configuration guide

### Configuration
- `.env.example` - Example configuration
- `vitest.config.ts` (UPDATE) - Mock WebSocket setup for tests

## Risk Assessment

**High Risk:**
- OpenAI API key exposure in frontend code
- **Mitigation**: Use Supabase edge function as proxy (or alternative proxy)

**Medium Risk:**
- Breaking existing voice command workflows
- **Mitigation**: Thorough testing before deployment

**Low Risk:**
- Backward compatibility issues
- **Mitigation**: No breaking API changes planned

## Dependencies

- OpenAI API account with Realtime API access
- Environment variable configuration
- Modern browser with WebRTC support
- Stable internet connection

## Rollback Plan

If issues occur:
1. Revert to previous commit
2. Keep old Supabase edge function URL as fallback
3. Document lessons learned

---

## Next Steps

1. Create Archon project for tracking
2. Create detailed tasks for each phase
3. Start Phase 1 (Critical fixes)
4. Test fixes in development environment
5. Update documentation
6. Deploy to production
