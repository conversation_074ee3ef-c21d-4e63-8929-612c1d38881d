# WebRTC Audio Integration Implementation Plan

**Parent Task:** Fix WebRTC audio integration with network optimization
**Project:** Voice Integration Connection Fix
**Subtasks Created:** 3

## Overview

This plan details the implementation of proper WebRTC audio optimization integration with VoiceAgent, ensuring network-aware bitrate adjustment and comprehensive audio quality optimization.

## Subtasks

### Subtask 1: Add network condition monitoring to VoiceAgent (Order: 98)
**File:** `src/components/VoiceAgent.tsx`

**What to implement:**
1. Create a new `useEffect` hook that monitors connection quality
2. Track latency from WebSocket round-trip times
3. Calculate packet loss from missed acknowledgments
4. Estimate bandwidth from successful message rates
5. Call `recorderRef.current?.updateNetworkConditions()` with monitored metrics
6. Run monitoring every 2-5 seconds while connected

**Key code patterns:**
```typescript
// In connectWebSocket function, track each message round-trip
const messageStartTime = Date.now();
wsRef.current.send(JSON.stringify({ type: 'ping', id: Date.now() }));

// In onmessage handler, calculate latency
if (data.type === 'pong') {
  const latency = Date.now() - data.id;
  // Pass to AudioRecorder
  recorderRef.current?.updateNetworkConditions({ latency });
}
```

**Success Criteria:**
- ✅ Network conditions are updated at least every 5 seconds
- ✅ AudioRecorder receives valid network condition updates
- ✅ Console logs show adaptive bitrate adjustments
- ✅ No errors in network monitoring loop

---

### Subtask 2: Enable echo cancellation and noise suppression (Order: 97)
**File:** `src/utils/RealtimeAudio.ts` (verify implementation)

**What to verify:**
1. Check that `getUserMedia` constraints are correctly set:
   - `echoCancellation: true`
   - `noiseSuppression: true`
   - `autoGainControl: true`
2. Add console debug logging when constraints are applied
3. Log the actual audio track settings returned by browser
4. Test in browser DevTools to confirm audio processing is active

**Debug approach:**
```typescript
// After getUserMedia succeeds
const audioTracks = this.stream!.getAudioTracks();
const settings = audioTracks[0].getSettings();
console.log('Audio track settings:', settings);
// Check for echoCancellation, noiseSuppression, autoGainControl
```

**Success Criteria:**
- ✅ Audio constraints are properly applied in getUserMedia call
- ✅ Browser confirms constraints are enabled (check DevTools)
- ✅ Console logs show audio track settings
- ✅ Audio quality noticeably improves (subjective test)

---

### Subtask 3: Integrate jitter buffer management (Order: 96)
**File:** `src/utils/RealtimeAudio.ts` and `src/components/VoiceAgent.tsx`

**What to implement:**
1. Verify jitter buffer is working correctly:
   - Target: 40ms buffer as defined in `calculateOptimalSendInterval()`
   - Actual: Check `audioBuffer` array accumulation
2. Test different network conditions:
   - High latency (>200ms): Buffer should increase interval
   - Packet loss (>5%): Buffer should increase delay between sends
3. Add unit tests for jitter buffer behavior
4. Verify OpenAI API receives well-formed audio chunks

**Test scenarios:**
```typescript
// Test 1: High latency
updateNetworkConditions({ latency: 250, packetLoss: 0 })
// Expected: calculateOptimalSendInterval() returns ~50ms

// Test 2: High packet loss
updateNetworkConditions({ latency: 50, packetLoss: 0.1 })
// Expected: calculateOptimalSendInterval() returns ~60ms

// Test 3: Both conditions
updateNetworkConditions({ latency: 200, packetLoss: 0.08 })
// Expected: calculateOptimalSendInterval() returns ~60-100ms
```

**Success Criteria:**
- ✅ Jitter buffer maintains 40ms target under normal conditions
- ✅ Buffer increases intelligently under poor network
- ✅ Unit tests cover all network scenarios
- ✅ OpenAI API receives properly formatted audio
- ✅ No audio gaps or stuttering in playback

---

## Implementation Order

1. **Start with Subtask 2** (easiest verification)
   - Just verify the existing implementation works
   - Takes 10-15 minutes

2. **Move to Subtask 1** (core feature)
   - Add network monitoring to VoiceAgent
   - Integrate with AudioRecorder
   - Takes 20-30 minutes

3. **Complete with Subtask 3** (testing)
   - Add unit tests for jitter buffer
   - Verify audio quality under various conditions
   - Takes 20-30 minutes

**Total Estimated Time:** 50-75 minutes (Phase 3 of overall fix)

---

## Key Files Reference

- **VoiceAgent.tsx** (lines 301-421): connectWebSocket function
- **VoiceAgent.tsx** (lines 426-464): startRecording function
- **RealtimeAudio.ts** (lines 45-85): AudioRecorder.start() with constraints
- **RealtimeAudio.ts** (lines 90-112): processAudioWithOptimization
- **RealtimeAudio.ts** (lines 133-150): calculateOptimalSendInterval
- **RealtimeAudio.ts** (lines 155-165): updateNetworkConditions
- **RealtimeAudio.ts** (lines 170-187): calculateAdaptiveBitrate

---

## Testing Approach

### Unit Tests
Create tests for:
1. Network condition updates and bitrate calculation
2. Jitter buffer timing under different network conditions
3. Audio constraint validation

### Integration Tests
1. VoiceAgent properly calls updateNetworkConditions
2. AudioRecorder receives and processes network updates
3. Audio data reaches OpenAI API without gaps

### Manual Testing
1. Enable Network DevTools throttling (fast 3G, slow 3G, offline)
2. Listen for audio quality changes
3. Check console logs for adaptive bitrate adjustments

---

## Rollback Plan

If issues occur:
1. Revert network monitoring code in VoiceAgent
2. Keep audio constraints as-is (they're non-breaking)
3. Remove any new tests if they fail
4. Return to previous commit if critical

---

## Next: Parent Task Completion

Once all 3 subtasks are complete:
1. Mark Task 3 as "review"
2. Update VOICE_FIX_PLAN.md with completion status
3. Move to Phase 2 tasks: Error messages, network status detection
