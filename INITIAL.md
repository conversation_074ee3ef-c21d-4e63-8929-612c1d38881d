# Feature Request Template

Use this template to define requirements for new features. The more context you provide, the better the implementation.

## FEATURE

[Clear, concise description of what you want to build]

**Example:**
```
Add a new form for recording supplier deliveries with temperature checks and lot tracking
```

---

## EXAMPLES

Point to existing code patterns in the codebase that should be followed.

**Pattern Categories:**
- **Form components** - Similar forms to follow
- **Database methods** - Service methods for data operations
- **UI components** - shadcn/ui components to use
- **Validation patterns** - Zod schemas and React Hook Form usage
- **Sync patterns** - Supabase synchronization logic

**Example:**
```
- Form pattern: src/components/HaccpEventForm.tsx
- Database methods: src/lib/sqlite-service.ts (insertProduct, insertTemperatureReading, getProducts)
- UI components: src/components/ui/form.tsx, button.tsx, input.tsx, select.tsx
- Validation: React Hook Form + Zod schema (see HaccpEventForm.tsx)
- Sync pattern: src/lib/sync-service.ts (syncToSupabase method)
```

---

## DOCUMENTATION

External documentation URLs that will be helpful for implementation.

**Common Libraries:**
- React Hook Form: https://react-hook-form.com/docs
- Zod Validation: https://zod.dev
- shadcn/ui Components: https://ui.shadcn.com/docs/components
- SQLite: https://sql.js.org
- React Query: https://tanstack.com/query/latest/docs/framework/react/overview

**Example:**
```
- React Hook Form useForm: https://react-hook-form.com/docs/useform
- Zod schema validation: https://zod.dev/?id=primitives
- shadcn/ui Form component: https://ui.shadcn.com/docs/components/form
- shadcn/ui Select component: https://ui.shadcn.com/docs/components/select
```

---

## OTHER CONSIDERATIONS

Special requirements, business rules, edge cases, or patterns to follow.

**Common Considerations:**
- Validation rules (min/max values, required fields, formats)
- Error handling requirements
- Success/error feedback (toasts)
- Data relationships (foreign keys, lookups)
- Staging workflow integration
- User experience requirements
- Performance considerations
- Accessibility needs

**Example:**
```
- Temperature validation: Must be between 32°F and 212°F
- Lot number validation: Format XXX-YYYY-NNNN (required)
- Track relationships: supplier_id, product_id (use Select dropdowns)
- Add to staging workflow for Supabase sync
- Show success toast after submission: "Delivery recorded successfully"
- Show error toast on failure: "Failed to record delivery"
- Reset form after successful submission
- Auto-populate date/time to current timestamp
- Required fields: supplier, product, quantity, temperature, lot_number
- Optional fields: notes, received_by
```

---

## Tips for Better PRPs

1. **Be Specific**: Instead of "add a form", say "add a supplier delivery form with temperature monitoring"

2. **Reference Existing Code**: Point to similar implementations already in the codebase

3. **Include Business Rules**: Define validation rules, required fields, formats

4. **Specify User Experience**: Describe expected behavior, feedback, error handling

5. **Link Documentation**: Include specific doc sections, not just homepages

6. **Define Success**: What does "done" look like? How will you test it?

---

## Example Complete INITIAL.md

```markdown
## FEATURE
Add a supplier delivery tracking form that records incoming shipments with temperature monitoring, lot tracking, and product verification for HACCP compliance.

## EXAMPLES
- Form structure: src/components/HaccpEventForm.tsx
- Database insert pattern: src/lib/sqlite-service.ts (insertProduct, insertTemperatureReading methods)
- Database schema pattern: public/sqlite-schema.sql (products_staged, temperature_readings_staged tables)
- UI components: src/components/ui/form.tsx, button.tsx, input.tsx, select.tsx
- Select dropdown pattern: HaccpEventForm.tsx (product/supplier selection)
- Validation: React Hook Form with Zod (see HaccpEventForm formSchema)
- Toast notifications: HaccpEventForm.tsx (success/error handling)
- Sync to Supabase: src/lib/sync-service.ts

## DOCUMENTATION
- React Hook Form API: https://react-hook-form.com/docs/useform
- Zod validation: https://zod.dev/?id=primitives
- Zod number validation: https://zod.dev/?id=numbers
- shadcn/ui Form: https://ui.shadcn.com/docs/components/form
- shadcn/ui Select: https://ui.shadcn.com/docs/components/select
- shadcn/ui Date Picker: https://ui.shadcn.com/docs/components/date-picker

## OTHER CONSIDERATIONS
- Required fields: supplier_id, product_id, quantity, temperature, lot_number, received_date
- Optional fields: notes, received_by, delivery_time
- Temperature validation: 32-212°F range with decimal support
- Lot number format: XXX-YYYY-NNNN (letters, year, sequence number)
- Quantity validation: Positive integers only, minimum 1
- Use Select components for supplier and product (load from SQLite)
- Auto-populate received_date to current date
- Show success toast: "Delivery recorded successfully - [Supplier] [Product] x[Qty]"
- Show error toast: "Failed to record delivery - [error message]"
- Reset form after successful submission
- Add to staging workflow with synced_to_supabase and sync_confirmed fields
- Create new SQLite table: supplier_deliveries_staged
- Include in sync-service.ts for Supabase synchronization
- Maintain consistent styling with existing forms
```

---

## Next Steps

After creating your INITIAL.md:

1. **Generate PRP**: Run `/generate-prp INITIAL.md` in Claude Code
   - This analyzes your codebase and creates a comprehensive implementation plan

2. **Review PRP**: Check `PRPs/[feature-name].md` for completeness
   - Verify all patterns are captured
   - Ensure validation commands are correct
   - Confirm success criteria match your needs

3. **Execute PRP**: Run `/execute-prp PRPs/[feature-name].md` in Claude Code
   - This implements the feature following the plan
   - Runs validation at each step
   - Verifies success criteria

4. **Test**: Manually test the feature
   - Run `npm run dev`
   - Test in browser
   - Verify data persistence
   - Check Supabase sync (if applicable)
