# Example Feature Request: Supplier Delivery Tracking

This is a complete example of an INITIAL.md file that demonstrates best practices for creating feature requests using the PRP framework.

---

## FEATURE

Add a comprehensive supplier delivery tracking system that records incoming seafood shipments with temperature monitoring, lot number tracking, and product verification for HACCP compliance. The system should integrate with existing products and support the staging workflow for offline data collection with Supabase synchronization.

**Core Functionality:**
- Record supplier deliveries with product details
- Track receiving temperature (critical for food safety)
- Capture lot numbers for traceability
- Link to existing products and suppliers
- Support offline entry with sync to Supabase
- Provide delivery history and reporting

---

## EXAMPLES

### Form Patterns
- **Main form structure**: `src/components/HaccpEventForm.tsx`
  - React Hook Form setup with zodResolver
  - Multiple FormField components
  - Select dropdowns for entity selection
  - Number input handling with type conversion
  - Success/error toast notifications
  - Form reset after successful submission

### Database Operations
- **Insert methods**: `src/lib/sqlite-service.ts`
  - `insertProduct()` - Shows parameterized query pattern
  - `insertTemperatureReading()` - Temperature data structure
  - `insertInventory()` - Quantity tracking pattern
  - Error handling with try/catch
  - Statement preparation and cleanup

- **Get methods**: `src/lib/sqlite-service.ts`
  - `getProducts()` - Shows result mapping pattern
  - Converting SQLite array results to typed objects
  - Ordering and filtering

### Schema Patterns
- **Table structures**: `public/sqlite-schema.sql`
  - `products_staged` - Product entity pattern
  - `temperature_readings_staged` - Temperature tracking
  - `inventory_staged` - Quantity tracking with timestamps
  - Staging workflow fields: `synced_to_supabase`, `sync_confirmed`
  - Foreign key relationships
  - Default timestamp handling

### UI Components
- **Form components**: `src/components/ui/form.tsx`
- **Input components**: `src/components/ui/input.tsx`
- **Select dropdowns**: `src/components/ui/select.tsx`
- **Button variants**: `src/components/ui/button.tsx`
- **Date picker** (if needed): `src/components/ui/calendar.tsx`

### Sync Pattern
- **Supabase sync**: `src/lib/sync-service.ts`
  - Filter unsynced records
  - Batch insert to Supabase
  - Update sync status flags
  - Error handling during sync

---

## DOCUMENTATION

### React Hook Form
- **useForm hook**: https://react-hook-form.com/docs/useform
  - Form initialization and configuration
  - Default values setup
  - Resolver integration

- **FormField component**: https://react-hook-form.com/docs/usecontroller
  - Field registration pattern
  - Render prop usage
  - Field validation

### Zod Validation
- **Schema definition**: https://zod.dev/?id=primitives
  - String, number, date validators
  - Min/max constraints
  - Custom error messages

- **Number validation**: https://zod.dev/?id=numbers
  - Positive numbers, min/max values
  - Integer vs decimal handling

- **String validation**: https://zod.dev/?id=strings
  - Min length, max length
  - Pattern matching (regex)
  - Transforms (trim, etc.)

### shadcn/ui Components
- **Form component**: https://ui.shadcn.com/docs/components/form
  - Form structure and accessibility
  - Error message handling
  - Label associations

- **Select component**: https://ui.shadcn.com/docs/components/select
  - Dropdown implementation
  - Option rendering
  - Value handling

- **Input component**: https://ui.shadcn.com/docs/components/input
  - Text and number inputs
  - Controlled component pattern

### SQLite & sql.js
- **Parameterized queries**: https://sql.js.org/#/?id=using-prepared-statements
  - Preventing SQL injection
  - Statement preparation
  - Parameter binding

---

## OTHER CONSIDERATIONS

### Data Model Requirements

**Required Fields:**
- `supplier_id` (INTEGER) - Foreign key to suppliers table
- `product_id` (INTEGER) - Foreign key to products table
- `quantity` (INTEGER) - Amount received (positive integer, min: 1)
- `unit` (TEXT) - Unit of measure (lbs, kg, cases, etc.)
- `temperature` (REAL) - Receiving temperature in Fahrenheit
- `lot_number` (TEXT) - Manufacturer lot/batch number for traceability
- `received_date` (TEXT) - Date of delivery (ISO 8601 format)
- `created_by` (TEXT) - User who recorded the delivery

**Optional Fields:**
- `notes` (TEXT) - Additional delivery notes
- `received_by` (TEXT) - Person who physically received delivery
- `delivery_time` (TEXT) - Time of delivery (HH:MM format)
- `condition_notes` (TEXT) - Product condition upon receipt
- `rejected` (INTEGER) - Boolean flag (0/1) if delivery rejected

**Staging Fields (automatic):**
- `synced_to_supabase` (INTEGER) - Default 0
- `sync_confirmed` (INTEGER) - Default 0
- `created_at` (TEXT) - DEFAULT CURRENT_TIMESTAMP

### Validation Rules

**Temperature:**
- Range: 32°F to 212°F (freezing to boiling)
- Decimal precision: 1 decimal place (e.g., 38.5°F)
- Warning if outside safe zone (varies by product type)

**Lot Number:**
- Format: Alphanumeric, dashes allowed
- Min length: 3 characters
- Max length: 50 characters
- Example: "LOT-2025-0123"

**Quantity:**
- Must be positive integer (min: 1)
- Reasonable max: 10,000 (configurable)

**Date:**
- Cannot be in the future
- Default to current date
- Format: YYYY-MM-DD

**Supplier & Product:**
- Must select from existing records (dropdown)
- Load options from SQLite products_staged and suppliers table

### User Experience Requirements

**Form Behavior:**
- Auto-populate received_date to current date
- Focus on first field on load
- Show loading state during submission
- Disable submit button while submitting
- Clear form after successful submission
- Keep dropdowns loaded (don't refetch on reset)

**Feedback:**
- Success toast: "Delivery recorded: [Supplier Name] - [Product Name] ([Quantity] [Unit])"
- Error toast: "Failed to record delivery: [error message]"
- Validation errors inline with each field

**Temperature Warning:**
- If temperature > 41°F for refrigerated items, show warning icon
- If temperature > 0°F for frozen items, show warning icon
- Don't block submission, just visual indicator

### Database Integration

**New Table: `supplier_deliveries_staged`**
```sql
CREATE TABLE IF NOT EXISTS supplier_deliveries_staged (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  supplier_id INTEGER NOT NULL,
  product_id INTEGER NOT NULL,
  quantity INTEGER NOT NULL,
  unit TEXT NOT NULL,
  temperature REAL NOT NULL,
  lot_number TEXT NOT NULL,
  received_date TEXT NOT NULL,
  notes TEXT,
  received_by TEXT,
  delivery_time TEXT,
  condition_notes TEXT,
  rejected INTEGER DEFAULT 0,
  created_by TEXT NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  synced_to_supabase INTEGER DEFAULT 0,
  sync_confirmed INTEGER DEFAULT 0
);
```

**Service Methods Needed:**
- `insertSupplierDelivery(data: SupplierDeliveryInsert): Promise<void>`
- `getSupplierDeliveries(): Promise<SupplierDelivery[]>`
- `getSupplierDeliveryById(id: number): Promise<SupplierDelivery | null>`
- `updateSupplierDelivery(id: number, data: Partial<SupplierDeliveryInsert>): Promise<void>`

### Sync Integration

**Add to `sync-service.ts`:**
- Sync supplier_deliveries_staged to Supabase `supplier_deliveries` table
- Follow existing pattern from products, inventory sync
- Update `synced_to_supabase` flag after successful sync
- Include in confirmation queue

### UI/UX Details

**Layout:**
- Two-column grid on desktop
- Single column on mobile
- Group related fields (product info, temperature/lot, notes)

**Field Order:**
1. Supplier (Select dropdown)
2. Product (Select dropdown)
3. Quantity (Number input)
4. Unit (Select: lbs, kg, cases, ea)
5. Temperature (Number input with °F label)
6. Lot Number (Text input)
7. Received Date (Date picker, default today)
8. Delivery Time (Time input, optional)
9. Received By (Text input, optional)
10. Condition Notes (Textarea, optional)
11. Notes (Textarea, optional)

**Styling:**
- Consistent with existing forms
- Use `space-y-4` for vertical spacing
- Use `grid gap-4 md:grid-cols-2` for two-column layout
- Temperature input with suffix "°F"
- Submit button at bottom, full width on mobile

### Testing Checklist

**Form Validation:**
- [ ] Required fields show error when empty
- [ ] Temperature validates range (32-212°F)
- [ ] Quantity must be positive integer
- [ ] Lot number meets format requirements
- [ ] Date cannot be in future
- [ ] Supplier/Product selections required

**Database Operations:**
- [ ] Insert creates record in supplier_deliveries_staged
- [ ] Record includes all required fields
- [ ] Timestamps auto-populate correctly
- [ ] Staging flags default to 0

**User Feedback:**
- [ ] Success toast shows on successful submission
- [ ] Error toast shows on failure
- [ ] Form resets after success
- [ ] Loading state shows during submission
- [ ] Submit button disabled while submitting

**Sync Workflow:**
- [ ] Record appears in staging dashboard
- [ ] Can confirm and sync to Supabase
- [ ] Sync flags update correctly
- [ ] Data matches in Supabase after sync

---

## Success Criteria

### Functional
- ✅ Form accepts all required and optional fields
- ✅ Validation rules enforced before submission
- ✅ Data persists to SQLite correctly
- ✅ Records sync to Supabase successfully
- ✅ Form resets and is ready for next entry
- ✅ User receives appropriate feedback (success/error)

### Technical
- ✅ No TypeScript errors (`npm run build:dev`)
- ✅ No ESLint warnings (`npm run lint`)
- ✅ All interfaces properly typed
- ✅ Parameterized queries prevent SQL injection
- ✅ Error handling on all async operations

### User Experience
- ✅ Form is intuitive and easy to use
- ✅ Validation errors are clear and helpful
- ✅ Loading states prevent duplicate submissions
- ✅ Mobile responsive and accessible
- ✅ Consistent with existing application design

---

## Implementation Notes

**Phase 1: Database & Service Layer**
1. Add table schema to `sqlite-schema.sql`
2. Add interfaces to `sqlite-service.ts`
3. Implement insert/get methods
4. Test database operations in console

**Phase 2: Form Component**
1. Create `SupplierDeliveryForm.tsx`
2. Define Zod schema
3. Setup React Hook Form
4. Implement FormFields
5. Add submit handler with error handling

**Phase 3: Integration**
1. Create page component (if standalone)
2. Add route to App.tsx
3. Update sync-service.ts
4. Test end-to-end workflow

**Phase 4: Validation & Polish**
1. Run linter and fix warnings
2. Test all validation rules
3. Test on mobile devices
4. Verify Supabase sync
5. User acceptance testing

---

## Questions to Resolve

Before generating PRP:
- Should we create a separate suppliers table or assume suppliers already exist?
- Do we need a delivery history view, or just the form?
- Should rejected deliveries have different workflow/notifications?
- What temperature ranges trigger warnings (product-type specific)?

**Assumptions:**
- Suppliers table already exists with supplier_name field
- Products table already exists (products_staged)
- No separate delivery history view initially (can be added later)
- Temperature warnings for all items: >41°F = warning

---

This example demonstrates:
- ✅ Clear, detailed feature description
- ✅ Specific code examples with explanations
- ✅ Comprehensive documentation links
- ✅ Detailed validation and business rules
- ✅ User experience specifications
- ✅ Database schema and integration details
- ✅ Success criteria and testing checklist
- ✅ Implementation roadmap
- ✅ Open questions and assumptions

When you create your own INITIAL.md, aim for this level of detail!
