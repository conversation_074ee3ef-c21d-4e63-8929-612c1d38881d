import { supabase } from '@/integrations/supabase/client';
import { sqliteService, SyncStatus, ConfirmationQueueItem } from './sqlite-service';
import {
  StagedProduct,
  StagedInventory,
  StagedCCPMonitoring,
  StagedTemperatureReading,
  StagedQualityTest,
  StagedAllergenTest,
  StagedHACCPEvent
} from './sqlite-service';

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  failedCount: number;
  errors: Array<{
    table: string;
    recordId: string;
    error: string;
  }>;
}

export interface SyncOptions {
  confirmBeforeSync?: boolean;
  batchSize?: number;
  tables?: string[];
  dryRun?: boolean;
}

class SyncService {
  private isRunning = false;
  private abortController: AbortController | null = null;

  async initialize(): Promise<void> {
    await sqliteService.initialize();
  }

  async startSync(options: SyncOptions = {}): Promise<SyncResult> {
    if (this.isRunning) {
      throw new Error('Sync is already running');
    }

    this.isRunning = true;
    this.abortController = new AbortController();

    try {
      const result: SyncResult = {
        success: true,
        syncedCount: 0,
        failedCount: 0,
        errors: []
      };

      const tables = options.tables || [
        'staged_products',
        'staged_inventory',
        'staged_ccp_monitoring',
        'staged_temperature_readings',
        'staged_quality_tests',
        'staged_allergen_tests',
        'staged_haccp_events'
      ];

      for (const tableName of tables) {
        if (this.abortController?.signal.aborted) {
          break;
        }

        try {
          const syncResult = await this.syncTable(tableName, options);
          result.syncedCount += syncResult.syncedCount;
          result.failedCount += syncResult.failedCount;
          result.errors.push(...syncResult.errors);

          if (!syncResult.success) {
            result.success = false;
          }
        } catch (error) {
          result.success = false;
          result.errors.push({
            table: tableName,
            recordId: 'unknown',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return result;
    } finally {
      this.isRunning = false;
      this.abortController = null;
    }
  }

  async stopSync(): Promise<void> {
    if (this.abortController) {
      this.abortController.abort();
    }
  }

  private async syncTable(tableName: string, options: SyncOptions): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      syncedCount: 0,
      failedCount: 0,
      errors: []
    };

    // Get unsynced records
    const records = await sqliteService.getUnsyncedRecords(tableName);

    if (records.length === 0) {
      return result;
    }

    // Process records in batches
    const batchSize = options.batchSize || 10;
    for (let i = 0; i < records.length; i += batchSize) {
      if (this.abortController?.signal.aborted) {
        break;
      }

      const batch = records.slice(i, i + batchSize);

      for (const record of batch) {
        try {
          // Check if confirmation is required
          if (options.confirmBeforeSync) {
            const needsConfirmation = await this.requiresConfirmation(tableName, record);
            if (needsConfirmation) {
              await this.addToConfirmationQueue(tableName, record);
              continue;
            }
          }

          // Perform dry run check
          if (options.dryRun) {
            console.log(`Would sync ${tableName} record:`, record.id);
            continue;
          }

          // Sync the record
          const synced = await this.syncRecord(tableName, record);

          if (synced) {
            // Mark as synced in SQLite
            await sqliteService.markRecordsAsSynced(tableName, [record.id as string]);
            result.syncedCount++;
          } else {
            result.failedCount++;
          }
        } catch (error) {
          result.success = false;
          result.failedCount++;
          result.errors.push({
            table: tableName,
            recordId: record.id as string,
            error: error instanceof Error ? error.message : 'Unknown error'
          });

          // Update sync status
          await sqliteService.addSyncStatus({
            table_name: tableName,
            record_id: record.id as string,
            operation: 'insert',
            status: 'failed',
            error_message: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    return result;
  }

  private async syncRecord(tableName: string, record: Record<string, unknown>): Promise<boolean> {
    try {
      const recordId = record.id as string;

      switch (tableName) {
        case 'staged_products':
          return await this.syncProduct(record as unknown as StagedProduct);

        case 'staged_inventory':
          return await this.syncInventory(record as unknown as StagedInventory);

        case 'staged_ccp_monitoring':
          return await this.syncCCPMonitoring(record as unknown as StagedCCPMonitoring);

        case 'staged_temperature_readings':
          return await this.syncTemperatureReading(record as unknown as StagedTemperatureReading);

        case 'staged_quality_tests':
          return await this.syncQualityTest(record as unknown as StagedQualityTest);

        case 'staged_allergen_tests':
          return await this.syncAllergenTest(record as unknown as StagedAllergenTest);

        case 'staged_haccp_events':
          return await this.syncHACCPEvent(record as unknown as StagedHACCPEvent);

        default:
          throw new Error(`Unknown table: ${tableName}`);
      }
    } catch (error) {
      console.error(`Failed to sync record in ${tableName}:`, error);
      return false;
    }
  }

  private async syncProduct(product: StagedProduct): Promise<boolean> {
    // Map SQLite staged_products to Supabase Products table (case-sensitive)
    const supabaseProduct = {
      id: product.id,
      name: product.name,
      category: product.category || null,
      sub_category: product.subcategory || null,
      origin_country: product.origin_country || null,
      supplier_id: product.supplier_id || null,
      price: product.unit_price || null,
      min_stock: product.min_stock ?? 0,
      stock: product.current_stock || null,
      storage_temp_min: product.storage_temp_min || null,
      storage_temp_max: product.storage_temp_max || null,
      handling_instructions: product.handling_instructions || null,
      created_at: product.created_at || new Date().toISOString(),
      updated_at: product.updated_at || new Date().toISOString(),
    };

    const { error } = await supabase
      .from('products')
      .upsert(supabaseProduct);

    if (error) {
      console.error('Error syncing product:', error);
      throw error;
    }

    return true;
  }

  private async syncInventory(inventory: StagedInventory): Promise<boolean> {
    // Map to Supabase batch_tracking table
    const supabaseInventory = {
      id: inventory.id,
      product_id: inventory.product_id,
      batch_number: inventory.batch_number,
      quantity: inventory.quantity,
      unit_cost: 0, // From staged_inventory, this field may need to be added
      expiry_date: inventory.expiry_date || null,
      received_date: inventory.received_date,
      vendor_id: inventory.vendor_id || null,
      quality_check_status: inventory.quality_status || null,
      notes: inventory.notes || null,
      created_at: inventory.created_at || new Date().toISOString(),
      updated_at: inventory.updated_at || new Date().toISOString(),
    };

    const { error } = await supabase
      .from('batch_tracking')
      .upsert(supabaseInventory);

    if (error) {
      console.error('Error syncing inventory:', error);
      throw error;
    }

    return true;
  }

  private async syncCCPMonitoring(ccp: StagedCCPMonitoring): Promise<boolean> {
    // First, we need to find or create the corresponding CCP record
    const { data: ccpRecord } = await supabase
      .from('critical_control_points')
      .select('id')
      .eq('ccp_name', ccp.ccp_name)
      .single();

    const ccpId = ccpRecord?.id || crypto.randomUUID();

    const supabaseCCP = {
      id: ccp.id,
      ccp_id: ccpId,
      product_id: ccp.product_id,
      batch_number: ccp.batch_number,
      measurement_value: ccp.measurement_value,
      measurement_unit: ccp.measurement_unit,
      critical_limit_min: ccp.critical_limit_min,
      critical_limit_max: ccp.critical_limit_max,
      is_within_limits: ccp.is_within_limits,
      monitoring_datetime: ccp.monitoring_datetime,
      monitored_by: ccp.monitored_by,
      equipment_used: ccp.equipment_used,
      observations: ccp.observations,
      corrective_action_needed: ccp.corrective_action_needed,
    };

    const { error } = await supabase
      .from('ccp_monitoring_logs')
      .upsert(supabaseCCP);

    if (error) {
      throw error;
    }

    return true;
  }

  private async syncTemperatureReading(reading: StagedTemperatureReading): Promise<boolean> {
    const supabaseReading = {
      id: reading.id,
      sensor_id: reading.sensor_id,
      storage_area_id: reading.storage_area_id,
      temp_celsius: reading.temp_celsius,
      temp_fahrenheit: reading.temp_fahrenheit,
      humidity: reading.humidity,
      recorded_at: reading.recorded_at,
      within_safe_range: reading.within_safe_range,
      user_id: reading.created_by || 'system', // Required field
      notes: reading.notes,
    };

    const { error } = await supabase
      .from('temperature_readings')
      .upsert(supabaseReading);

    if (error) {
      throw error;
    }

    return true;
  }

  private async syncQualityTest(test: StagedQualityTest): Promise<boolean> {
    // Map quality tests to appropriate Supabase tables based on test type
    const supabaseTest = {
      id: test.id,
      product_id: test.product_id,
      batch_number: test.batch_number,
      test_type: test.test_type,
      test_date: test.test_date,
      laboratory: test.laboratory,
      result: test.result,
      quantitative_result: test.quantitative_result,
      detection_limit: test.detection_limit,
      compliant: test.compliant,
      certificate_number: test.certificate_number,
      test_method: test.test_method,
    };

    // Use a generic table or create specific logic based on test type
    // For now, we'll store in a JSON format that can be processed later
    const { error } = await supabase
      .from('products')
      .update({
        // Store quality test data in metadata or a related table
        // This is a simplified approach - you might want to create a dedicated quality_tests table
      })
      .eq('id', test.product_id);

    if (error) {
      console.warn('Quality test sync skipped - consider creating a dedicated quality_tests table');
    }

    return true;
  }

  private async syncAllergenTest(test: StagedAllergenTest): Promise<boolean> {
    const supabaseTest = {
      id: test.id,
      product_id: test.product_id,
      lot_id: test.lot_id,
      allergen_type: test.allergen_type,
      test_date: test.test_date,
      laboratory: test.laboratory,
      test_method: test.test_method,
      result: test.result,
      quantitative_result: test.quantitative_result,
      detection_limit: test.detection_limit,
      compliant: test.compliant,
      certificate_number: test.certificate_number,
    };

    const { error } = await supabase
      .from('allergen_testing')
      .upsert(supabaseTest);

    if (error) {
      throw error;
    }

    return true;
  }

  private async syncHACCPEvent(event: StagedHACCPEvent): Promise<boolean> {
    // Map to appropriate Supabase table for HACCP events
    // Since there's no specific haccp_events table, we'll store in calendar_events or similar
    const supabaseEvent = {
      id: event.id,
      title: `${event.event_type}: ${event.description}`,
      description: `Severity: ${event.severity}\nReported by: ${event.reported_by}\nImmediate Action: ${event.immediate_action || 'N/A'}\nRoot Cause: ${event.root_cause || 'N/A'}\nCorrective Action: ${event.corrective_action || 'N/A'}`,
      event_type: 'haccp_event',
      start_at: event.event_datetime,
      source: 'staging_system',
      metadata: {
        original_event_type: event.event_type,
        product_id: event.product_id,
        batch_number: event.batch_number,
        severity: event.severity,
        reported_by: event.reported_by,
        preventive_measures: event.preventive_measures,
        status: event.status,
      }
    };

    const { error } = await supabase
      .from('calendar_events')
      .upsert(supabaseEvent);

    if (error) {
      console.warn('HACCP event sync skipped - stored in calendar_events');
    }

    return true;
  }

  private async requiresConfirmation(tableName: string, record: Record<string, unknown>): Promise<boolean> {
    // Define rules for when confirmation is required
    const confirmationRules = {
      staged_products: (record: Record<string, unknown>) => {
        // Require confirmation for high-value products or new suppliers
        return (record.unit_price as number) > 1000 || !(record.supplier_id);
      },
      staged_inventory: (record: Record<string, unknown>) => {
        // Require confirmation for large quantities or expired items
        return (record.quantity as number) > 1000 || (record.expiry_date && new Date(record.expiry_date as string) < new Date());
      },
      staged_ccp_monitoring: (record: Record<string, unknown>) => {
        // Always require confirmation for CCP monitoring
        return true;
      },
      staged_haccp_events: (record: Record<string, unknown>) => {
        // Require confirmation for critical or major events
        return (record.severity as string) === 'critical' || (record.severity as string) === 'major';
      }
    };

    const rule = confirmationRules[tableName as keyof typeof confirmationRules];
    return rule ? rule(record) : false;
  }

  private async addToConfirmationQueue(tableName: string, record: Record<string, unknown>): Promise<void> {
    await sqliteService.addToConfirmationQueue({
      record_type: tableName,
      record_id: record.id as string,
      data: record,
      requires_confirmation: true
    });
  }

  async getSyncStatus(): Promise<{
    isRunning: boolean;
    pendingRecords: number;
    pendingConfirmations: number;
    lastSyncResult?: SyncResult;
  }> {
    const stats = await sqliteService.getStats();

    return {
      isRunning: this.isRunning,
      pendingRecords: stats.pending_sync,
      pendingConfirmations: stats.pending_confirmations
    };
  }

  async getPendingConfirmations(): Promise<ConfirmationQueueItem[]> {
    return await sqliteService.getPendingConfirmations();
  }

  async confirmAndSync(queueItemId: number, confirmedBy: string): Promise<boolean> {
    try {
      // Mark as confirmed
      await sqliteService.confirmQueueItem(queueItemId, confirmedBy);

      // Get the confirmed item
      const confirmedItems = await sqliteService.getPendingConfirmations();
      const confirmedItem = confirmedItems.find(item => item.id === queueItemId);

      if (!confirmedItem) {
        throw new Error('Confirmed item not found');
      }

      // Sync the confirmed record
      const synced = await this.syncRecord(confirmedItem.record_type, confirmedItem.data);

      if (synced) {
        // Mark as synced in SQLite
        await sqliteService.markRecordsAsSynced(
          confirmedItem.record_type,
          [confirmedItem.record_id]
        );
      }

      return synced;
    } catch (error) {
      console.error('Failed to confirm and sync:', error);
      return false;
    }
  }

  async getSyncHistory(): Promise<SyncStatus[]> {
    // This would typically query a sync history table
    // For now, return pending sync items
    return await sqliteService.getPendingSyncItems();
  }
}

// Export singleton instance
export const syncService = new SyncService();