/**
 * Comprehensive error handling for voice agent operations
 * Provides error classification, recovery strategies, and user-friendly messaging
 */

// Error classification for better handling and recovery
export enum VoiceErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  DATABASE = 'DATABASE',
  AUDIO = 'AUDIO',
  TIMEOUT = 'TIMEOUT',
  PERMISSION = 'PERMISSION',
  UNKNOWN = 'UNKNOWN',
}

// Structured error representation
export class VoiceAgentError extends Error {
  constructor(
    public type: VoiceErrorType,
    public message: string,
    public details?: Record<string, unknown>,
    public isRetryable: boolean = false,
    public suggestedAction?: string
  ) {
    super(message);
    this.name = 'VoiceAgentError';
  }
}

/**
 * Classify and wrap errors with context
 */
export function classifyError(
  error: unknown,
  context: string = 'Unknown operation'
): VoiceAgentError {
  if (error instanceof VoiceAgentError) {
    return error;
  }

  const errorMessage = error instanceof Error ? error.message : String(error);

  // Network-related errors
  if (
    errorMessage.includes('WebSocket') ||
    errorMessage.includes('Failed to fetch') ||
    errorMessage.includes('Network request failed') ||
    error instanceof TypeError
  ) {
    return new VoiceAgentError(
      VoiceErrorType.NETWORK,
      'Network connection failed',
      { originalError: errorMessage, context },
      true,
      'Check your internet connection and try again'
    );
  }

  // Audio/Permission errors
  if (
    errorMessage.includes('NotAllowedError') ||
    errorMessage.includes('microphone') ||
    errorMessage.includes('getUserMedia')
  ) {
    return new VoiceAgentError(
      VoiceErrorType.PERMISSION,
      'Microphone permission denied',
      { originalError: errorMessage, context },
      false,
      'Grant microphone permission in browser settings and reload'
    );
  }

  // Timeout errors
  if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
    return new VoiceAgentError(
      VoiceErrorType.TIMEOUT,
      'Operation took too long',
      { originalError: errorMessage, context },
      true,
      'Try again or check your internet connection'
    );
  }

  // Validation errors
  if (errorMessage.includes('Validation') || errorMessage.includes('required')) {
    return new VoiceAgentError(
      VoiceErrorType.VALIDATION,
      `Invalid input: ${errorMessage}`,
      { originalError: errorMessage, context },
      false,
      'Please check the input values and try again'
    );
  }

  // Database errors
  if (errorMessage.includes('database') || errorMessage.includes('sql')) {
    return new VoiceAgentError(
      VoiceErrorType.DATABASE,
      'Database operation failed',
      { originalError: errorMessage, context },
      true,
      'Try again or restart the application'
    );
  }

  // Audio processing errors
  if (
    errorMessage.includes('audio') ||
    errorMessage.includes('encoding') ||
    errorMessage.includes('codec')
  ) {
    return new VoiceAgentError(
      VoiceErrorType.AUDIO,
      'Audio processing failed',
      { originalError: errorMessage, context },
      true,
      'Check your audio settings and try again'
    );
  }

  // Unknown error
  return new VoiceAgentError(
    VoiceErrorType.UNKNOWN,
    `Unexpected error: ${errorMessage}`,
    { originalError: errorMessage, context },
    false,
    'Try restarting the voice agent'
  );
}

/**
 * Format error for user-friendly display
 */
export function formatErrorForUser(error: VoiceAgentError | unknown): {
  title: string;
  message: string;
  action?: string;
} {
  if (error instanceof VoiceAgentError) {
    return {
      title: getErrorTitle(error.type),
      message: error.message,
      action: error.suggestedAction,
    };
  }

  const classified = classifyError(error);
  return {
    title: getErrorTitle(classified.type),
    message: classified.message,
    action: classified.suggestedAction,
  };
}

/**
 * Get human-readable title for error type
 */
function getErrorTitle(type: VoiceErrorType): string {
  switch (type) {
    case VoiceErrorType.NETWORK:
      return 'Connection Issue';
    case VoiceErrorType.VALIDATION:
      return 'Invalid Input';
    case VoiceErrorType.DATABASE:
      return 'Database Error';
    case VoiceErrorType.AUDIO:
      return 'Audio Error';
    case VoiceErrorType.TIMEOUT:
      return 'Operation Timeout';
    case VoiceErrorType.PERMISSION:
      return 'Permission Denied';
    default:
      return 'Error';
  }
}

/**
 * Determine if an error is retryable
 */
export function isRetryable(error: VoiceAgentError | unknown): boolean {
  if (error instanceof VoiceAgentError) {
    return error.isRetryable;
  }
  const classified = classifyError(error);
  return classified.isRetryable;
}

/**
 * Build detailed error context for logging
 */
export function buildErrorContext(
  error: unknown,
  operation: string,
  additionalContext?: Record<string, unknown>
) {
  const classified =
    error instanceof VoiceAgentError ? error : classifyError(error, operation);

  return {
    timestamp: new Date().toISOString(),
    operation,
    errorType: classified.type,
    message: classified.message,
    details: classified.details,
    isRetryable: classified.isRetryable,
    ...additionalContext,
  };
}

/**
 * Log error with full context
 */
export function logError(
  error: unknown,
  operation: string,
  additionalContext?: Record<string, unknown>
) {
  const errorContext = buildErrorContext(error, operation, additionalContext);
  console.error('[VoiceAgent Error]', errorContext);

  // Could be extended to send to error tracking service (e.g., Sentry)
  // sendToErrorTracking(errorContext);
}
