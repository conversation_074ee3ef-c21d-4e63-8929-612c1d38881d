/**
 * Comprehensive audit logging system for voice agent operations
 * Tracks all user actions, tool executions, and system events for compliance and debugging
 */

export enum AuditEventType {
  // Session events
  SESSION_STARTED = 'SESSION_STARTED',
  SESSION_ENDED = 'SESSION_ENDED',

  // Connection events
  CONNECTION_ESTABLISHED = 'CONNECTION_ESTABLISHED',
  CONNECTION_LOST = 'CONNECTION_LOST',
  CONNECTION_RETRY = 'CONNECTION_RETRY',

  // Tool execution events
  TOOL_REQUESTED = 'TOOL_REQUESTED',
  TOOL_EXECUTED = 'TOOL_EXECUTED',
  TOOL_FAILED = 'TOOL_FAILED',
  TOOL_TIMEOUT = 'TOOL_TIMEOUT',

  // Data modification events
  INVENTORY_CREATED = 'INVENTORY_CREATED',
  INVENTORY_UPDATED = 'INVENTORY_UPDATED',
  LOCATION_CHANGED = 'LOCATION_CHANGED',
  CCP_RECORDED = 'CCP_RECORDED',
  HACCP_RETRIEVED = 'HACCP_RETRIEVED',

  // Error events
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUDIO_ERROR = 'AUDIO_ERROR',

  // User events
  VOICE_INPUT_STARTED = 'VOICE_INPUT_STARTED',
  VOICE_INPUT_ENDED = 'VOICE_INPUT_ENDED',
  TEXT_MESSAGE_SENT = 'TEXT_MESSAGE_SENT',

  // Permission events
  PERMISSION_REQUESTED = 'PERMISSION_REQUESTED',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
}

export enum AuditSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL',
}

export interface AuditLogEntry {
  timestamp: string;
  sessionId: string;
  eventType: AuditEventType;
  severity: AuditSeverity;
  userId?: string;
  description: string;
  details?: Record<string, unknown>;
  // Compliance fields
  affectedResources?: string[]; // e.g., product IDs, batch numbers
  complianceRelevant?: boolean; // Flag for HACCP/food safety compliance
}

/**
 * In-memory audit log storage
 * In production, this should be persisted to a database
 */
class AuditLogger {
  private logs: AuditLogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory
  private sessionId: string = '';

  /**
   * Initialize audit logger with session ID
   */
  initSession(sessionId: string, userId?: string) {
    this.sessionId = sessionId;
    this.log(
      AuditEventType.SESSION_STARTED,
      AuditSeverity.INFO,
      'Voice session started',
      { sessionId, userId }
    );
  }

  /**
   * Log an audit event
   */
  log(
    eventType: AuditEventType,
    severity: AuditSeverity = AuditSeverity.INFO,
    description: string,
    details?: Record<string, unknown>,
    affectedResources?: string[],
    complianceRelevant: boolean = false
  ): void {
    const entry: AuditLogEntry = {
      timestamp: new Date().toISOString(),
      sessionId: this.sessionId,
      eventType,
      severity,
      description,
      details,
      affectedResources,
      complianceRelevant,
    };

    this.logs.push(entry);

    // Maintain max log size
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development' || severity === AuditSeverity.ERROR) {
      this.printLog(entry);
    }
  }

  /**
   * Log a tool execution
   */
  logToolExecution(
    toolName: string,
    args: Record<string, unknown>,
    success: boolean,
    result?: Record<string, unknown>,
    error?: string
  ): void {
    const affectedResources = this.extractResourceIds(toolName, args);

    if (success) {
      this.log(
        AuditEventType.TOOL_EXECUTED,
        AuditSeverity.INFO,
        `Tool executed: ${toolName}`,
        {
          toolName,
          args: this.sanitizeArgs(args),
          result,
        },
        affectedResources,
        this.isComplianceRelevant(toolName)
      );
    } else {
      this.log(
        AuditEventType.TOOL_FAILED,
        AuditSeverity.ERROR,
        `Tool failed: ${toolName}`,
        {
          toolName,
          args: this.sanitizeArgs(args),
          error,
        },
        affectedResources,
        this.isComplianceRelevant(toolName)
      );
    }
  }

  /**
   * Log data modifications
   */
  logDataModification(
    eventType: AuditEventType,
    resourceType: string,
    resourceId: string,
    changes?: Record<string, unknown>
  ): void {
    this.log(
      eventType,
      AuditSeverity.INFO,
      `${resourceType} modified: ${resourceId}`,
      {
        resourceType,
        resourceId,
        changes,
      },
      [resourceId],
      true // Data modifications are always compliance relevant
    );
  }

  /**
   * Log errors
   */
  logError(
    error: Error | string,
    context: string,
    details?: Record<string, unknown>
  ): void {
    const message = error instanceof Error ? error.message : String(error);
    this.log(
      AuditEventType.DATABASE_ERROR,
      AuditSeverity.ERROR,
      `Error in ${context}: ${message}`,
      {
        context,
        error: message,
        ...details,
      }
    );
  }

  /**
   * Get all logs
   */
  getLogs(
    filter?: {
      eventType?: AuditEventType;
      severity?: AuditSeverity;
      complianceOnly?: boolean;
      since?: Date;
    }
  ): AuditLogEntry[] {
    let filtered = [...this.logs];

    if (filter?.eventType) {
      filtered = filtered.filter(log => log.eventType === filter.eventType);
    }

    if (filter?.severity) {
      filtered = filtered.filter(log => log.severity === filter.severity);
    }

    if (filter?.complianceOnly) {
      filtered = filtered.filter(log => log.complianceRelevant);
    }

    if (filter?.since) {
      const sinceTime = filter.since.getTime();
      filtered = filtered.filter(log => new Date(log.timestamp).getTime() >= sinceTime);
    }

    return filtered;
  }

  /**
   * Get compliance report (HACCP-relevant events)
   */
  getComplianceReport(sessionId?: string): AuditLogEntry[] {
    const logs = this.getLogs({ complianceOnly: true });
    if (sessionId) {
      return logs.filter(log => log.sessionId === sessionId);
    }
    return logs;
  }

  /**
   * Export audit log as JSON for compliance documentation
   */
  exportAsJSON(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * Clear logs (use with caution)
   */
  clearLogs(beforeDate?: Date): void {
    if (beforeDate) {
      const cutoffTime = beforeDate.getTime();
      this.logs = this.logs.filter(log => new Date(log.timestamp).getTime() >= cutoffTime);
    } else {
      this.logs = [];
    }
  }

  /**
   * Print log entry to console
   */
  private printLog(entry: AuditLogEntry): void {
    const icon = this.getSeverityIcon(entry.severity);
    console.log(
      `${icon} [${entry.eventType}] ${entry.description}`,
      entry.details || ''
    );
  }

  /**
   * Get severity icon for console output
   */
  private getSeverityIcon(severity: AuditSeverity): string {
    switch (severity) {
      case AuditSeverity.INFO:
        return 'ℹ️ ';
      case AuditSeverity.WARNING:
        return '⚠️ ';
      case AuditSeverity.ERROR:
        return '❌';
      case AuditSeverity.CRITICAL:
        return '🚨';
      default:
        return '•';
    }
  }

  /**
   * Extract resource IDs from tool arguments
   */
  private extractResourceIds(toolName: string, args: Record<string, unknown>): string[] {
    const ids: string[] = [];

    if (args.product_id) ids.push(String(args.product_id));
    if (args.batch_number) ids.push(String(args.batch_number));
    if (args.inventory_id) ids.push(String(args.inventory_id));
    if (args.ccp_id) ids.push(String(args.ccp_id));

    return ids;
  }

  /**
   * Sanitize arguments for logging (remove sensitive data)
   */
  private sanitizeArgs(args: Record<string, unknown>): Record<string, unknown> {
    const sanitized = { ...args };
    // Remove any sensitive fields if needed
    return sanitized;
  }

  /**
   * Determine if a tool is compliance-relevant
   */
  private isComplianceRelevant(toolName: string): boolean {
    // Tools that affect food safety compliance
    const complianceTools = [
      'record_ccp_monitoring',
      'get_haccp_events',
      'add_inventory_event',
      'update_product_location',
    ];
    return complianceTools.includes(toolName);
  }
}

// Export singleton instance
export const auditLogger = new AuditLogger();
