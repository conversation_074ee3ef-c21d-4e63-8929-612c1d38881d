import { z } from 'zod';

/**
 * Comprehensive validation schemas for all voice tool arguments
 * Ensures type safety and data integrity before database operations
 */

// Common field validations
export const commonValidations = {
  productId: z.string().min(1, 'Product ID is required').max(255),
  batchNumber: z.string().min(1, 'Batch number is required').max(255),
  quantity: z.number().positive('Quantity must be positive'),
  unit: z
    .string()
    .optional()
    .refine((val) => !val || ['kg', 'lb', 'units', 'pieces', 'boxes'].includes(val), {
      message: 'Unit must be one of: kg, lb, units, pieces, boxes'
    }),
  location: z.string().max(255).optional(),
  notes: z.string().max(500).optional(),
};

// Validation schema for add_inventory_event
export const addInventoryEventSchema = z.object({
  product_id: commonValidations.productId,
  batch_number: commonValidations.batchNumber,
  quantity: commonValidations.quantity,
  unit: commonValidations.unit,
  location: commonValidations.location,
  notes: commonValidations.notes,
});

export type AddInventoryEventArgs = z.infer<typeof addInventoryEventSchema>;

// Validation schema for update_product_location
export const updateProductLocationSchema = z.object({
  product_id: commonValidations.productId,
  batch_number: commonValidations.batchNumber,
  new_location: z.string().min(1, 'New location is required').max(255),
});

export type UpdateProductLocationArgs = z.infer<typeof updateProductLocationSchema>;

// Validation schema for check_product_status
export const checkProductStatusSchema = z.object({
  product_id: commonValidations.productId,
});

export type CheckProductStatusArgs = z.infer<typeof checkProductStatusSchema>;

// Validation schema for record_ccp_monitoring
export const recordCCPMonitoringSchema = z.object({
  ccp_name: z.string().min(1, 'CCP name is required').max(255),
  measurement_value: z
    .number()
    .min(-100, 'Measurement too low')
    .max(1000, 'Measurement too high'),
  measurement_unit: z
    .string()
    .min(1)
    .refine(
      (val) => ['°C', '°F', 'pH', '%', 'ppm', 'minutes', 'hours'].includes(val),
      { message: 'Invalid measurement unit' }
    ),
  monitored_by: z.string().min(1, 'Monitored by is required').max(255),
  notes: z.string().max(500).optional(),
});

export type RecordCCPMonitoringArgs = z.infer<typeof recordCCPMonitoringSchema>;

// Validation schema for get_haccp_events (most flexible)
export const getHACCPEventsSchema = z.object({
  product_id: commonValidations.productId.optional(),
  event_type: z.string().max(100).optional(),
  limit: z.number().int().positive().max(1000).optional(),
});

export type GetHACCPEventsArgs = z.infer<typeof getHACCPEventsSchema>;

/**
 * Validate tool arguments against schema
 * Returns validated args or throws detailed error
 */
export function validateToolArguments(toolName: string, args: Record<string, unknown>) {
  try {
    switch (toolName) {
      case 'add_inventory_event':
        return addInventoryEventSchema.parse(args);

      case 'update_product_location':
        return updateProductLocationSchema.parse(args);

      case 'check_product_status':
        return checkProductStatusSchema.parse(args);

      case 'record_ccp_monitoring':
        return recordCCPMonitoringSchema.parse(args);

      case 'get_haccp_events':
        return getHACCPEventsSchema.parse(args);

      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Format Zod errors into user-friendly messages
      const fieldErrors = error.errors
        .map((err) => `${err.path.join('.')}: ${err.message}`)
        .join('; ');
      throw new Error(`Validation failed: ${fieldErrors}`);
    }
    throw error;
  }
}

/**
 * Format validation error for user-friendly display
 */
export function formatValidationError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return 'Validation failed: Invalid input';
}
