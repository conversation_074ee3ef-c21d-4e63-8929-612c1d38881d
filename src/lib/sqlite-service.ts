import initSqlJs from 'sql.js';

export interface StagedProduct {
  id: string;
  name: string;
  category?: string;
  subcategory?: string;
  origin_country?: string;
  supplier_id?: string;
  unit_price?: number;
  min_stock?: number;
  current_stock?: number;
  storage_temp_min?: number;
  storage_temp_max?: number;
  handling_instructions?: string;
  created_at?: string;
  updated_at?: string;
  synced_to_supabase?: boolean;
  sync_confirmed?: boolean;
  created_by?: string;
}

export interface StagedInventory {
  id: string;
  product_id: string;
  batch_number: string;
  quantity: number;
  unit?: string;
  expiry_date?: string;
  received_date: string;
  vendor_id?: string;
  quality_status?: string;
  temperature?: number;
  location?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  synced_to_supabase?: boolean;
  sync_confirmed?: boolean;
  created_by?: string;
}

export interface StagedCCPMonitoring {
  id: string;
  ccp_name: string;
  product_id?: string;
  batch_number?: string;
  measurement_value: number;
  measurement_unit: string;
  critical_limit_min?: number;
  critical_limit_max?: number;
  is_within_limits?: boolean;
  monitoring_datetime: string;
  monitored_by: string;
  equipment_used?: string;
  observations?: string;
  corrective_action_needed?: boolean;
  created_at?: string;
  synced_to_supabase?: boolean;
  sync_confirmed?: boolean;
  created_by?: string;
}

export interface StagedTemperatureReading {
  id: string;
  sensor_id: string;
  storage_area_id?: string;
  temp_celsius: number;
  temp_fahrenheit: number;
  humidity?: number;
  recorded_at: string;
  within_safe_range: boolean;
  violation_type?: string;
  notes?: string;
  created_at?: string;
  synced_to_supabase?: boolean;
  sync_confirmed?: boolean;
  created_by?: string;
}

export interface StagedQualityTest {
  id: string;
  product_id: string;
  batch_number?: string;
  test_type: string;
  test_date: string;
  laboratory: string;
  result: string;
  quantitative_result?: number;
  detection_limit?: number;
  compliant: boolean;
  certificate_number?: string;
  test_method?: string;
  created_at?: string;
  synced_to_supabase?: boolean;
  sync_confirmed?: boolean;
  created_by?: string;
}

export interface StagedAllergenTest {
  id: string;
  product_id: string;
  lot_id: string;
  allergen_type: string;
  test_date: string;
  laboratory: string;
  test_method: string;
  result: string;
  quantitative_result?: number;
  detection_limit?: number;
  compliant: boolean;
  certificate_number?: string;
  created_at?: string;
  synced_to_supabase?: boolean;
  sync_confirmed?: boolean;
  created_by?: string;
}

export interface StagedHACCPEvent {
  id: string;
  event_type: string;
  product_id?: string;
  batch_number?: string;
  event_datetime: string;
  description: string;
  severity: string;
  reported_by: string;
  immediate_action?: string;
  root_cause?: string;
  corrective_action?: string;
  preventive_measures?: string;
  status?: string;
  created_at?: string;
  synced_to_supabase?: boolean;
  sync_confirmed?: boolean;
  created_by?: string;
}

export interface SyncStatus {
  id?: number;
  table_name: string;
  record_id: string;
  operation: 'insert' | 'update' | 'delete';
  status: 'pending' | 'syncing' | 'completed' | 'failed';
  error_message?: string;
  created_at?: string;
  updated_at?: string;
  retry_count?: number;
}

export interface ConfirmationQueueItem {
  id?: number;
  record_type: string;
  record_id: string;
  data: Record<string, unknown>;
  requires_confirmation?: boolean;
  confirmed?: boolean;
  confirmed_by?: string;
  confirmed_at?: string;
  created_at?: string;
}

class SQLiteService {
  private db: import('sql.js').Database | null = null;
  private SQL: import('sql.js').SqlJsStatic | null = null;

  constructor() {
    // sql.js works entirely in memory for browser compatibility
  }

  async initialize(): Promise<void> {
    try {
      // Initialize sql.js
      const SQL = await initSqlJs({
        // You can load a wasm file from a CDN or include it in your project
        locateFile: file => `https://sql.js.org/dist/${file}`
      });

      this.SQL = SQL;

      // Create database in memory
      this.db = new SQL.Database();

      // Load and execute schema
      const schemaResponse = await fetch('/sqlite-schema.sql');
      const schema = await schemaResponse.text();
      this.db.exec(schema);

      console.log('SQLite database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize SQLite database:', error);
      throw error;
    }
  }

  private ensureDb() {
    if (!this.db || !this.SQL) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return { db: this.db, SQL: this.SQL };
  }

  // Product operations
  async createProduct(product: Omit<StagedProduct, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    const { db } = this.ensureDb();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO staged_products (
        id, name, category, subcategory, origin_country, supplier_id,
        unit_price, min_stock, current_stock, storage_temp_min, storage_temp_max,
        handling_instructions, created_at, updated_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      id, 
      product.name, 
      product.category ?? null, 
      product.subcategory ?? null,
      product.origin_country ?? null, 
      product.supplier_id ?? null, 
      product.unit_price ?? null,
      product.min_stock ?? null, 
      product.current_stock ?? null, 
      product.storage_temp_min ?? null,
      product.storage_temp_max ?? null, 
      product.handling_instructions ?? null, 
      now, 
      now, 
      product.created_by ?? null
    ]);

    return id;
  }

  async getProduct(id: string): Promise<StagedProduct | null> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_products WHERE id = ?', [id]);
    if (results.length > 0 && results[0].values.length > 0) {
      const row = results[0].values[0];
      const columns = results[0].columns;
      const product: any = {};
      columns.forEach((col: string, index: number) => {
        product[col] = row[index];
      });
      return product as StagedProduct;
    }
    return null;
  }

  async getAllProducts(): Promise<StagedProduct[]> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_products ORDER BY created_at DESC');
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const product: any = {};
        columns.forEach((col: string, index: number) => {
          product[col] = row[index];
        });
        return product as StagedProduct;
      });
    }
    return [];
  }

  async updateProduct(id: string, updates: Partial<StagedProduct>): Promise<void> {
    const { db } = this.ensureDb();
    const now = new Date().toISOString();

    const fields = Object.keys(updates).filter(key => key !== 'id');
    const values = fields.map(key => {
      const value = updates[key as keyof StagedProduct];
      // Convert boolean values to numbers for SQLite
      if (typeof value === 'boolean') {
        return value ? 1 : 0;
      }
      return value;
    });
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    const stmt = db.prepare(`UPDATE staged_products SET ${setClause}, updated_at = ? WHERE id = ?`);
    stmt.run([...values, now, id]);
  }

  // Inventory operations
  async createInventory(inventory: Omit<StagedInventory, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    const { db } = this.ensureDb();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO staged_inventory (
        id, product_id, batch_number, quantity, unit, expiry_date, received_date,
        vendor_id, quality_status, temperature, location, notes, created_at, updated_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      id, 
      inventory.product_id, 
      inventory.batch_number, 
      inventory.quantity,
      inventory.unit ?? null, 
      inventory.expiry_date ?? null, 
      inventory.received_date,
      inventory.vendor_id ?? null, 
      inventory.quality_status ?? null, 
      inventory.temperature ?? null,
      inventory.location ?? null, 
      inventory.notes ?? null, 
      now, 
      now, 
      inventory.created_by ?? null
    ]);

    return id;
  }

  async getInventory(id: string): Promise<StagedInventory | null> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_inventory WHERE id = ?', [id]);
    if (results.length > 0 && results[0].values.length > 0) {
      const row = results[0].values[0];
      const columns = results[0].columns;
      const inventory: any = {};
      columns.forEach((col: string, index: number) => {
        inventory[col] = row[index];
      });
      return inventory as StagedInventory;
    }
    return null;
  }

  async getInventoryByBatch(batchNumber: string): Promise<StagedInventory[]> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_inventory WHERE batch_number = ?', [batchNumber]);
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const inventory: any = {};
        columns.forEach((col: string, index: number) => {
          inventory[col] = row[index];
        });
        return inventory as StagedInventory;
      });
    }
    return [];
  }

  // CCP Monitoring operations
  async createCCPMonitoring(ccp: Omit<StagedCCPMonitoring, 'id' | 'created_at'>): Promise<string> {
    const { db } = this.ensureDb();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO staged_ccp_monitoring (
        id, ccp_name, product_id, batch_number, measurement_value, measurement_unit,
        critical_limit_min, critical_limit_max, is_within_limits, monitoring_datetime,
        monitored_by, equipment_used, observations, corrective_action_needed, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      id, 
      ccp.ccp_name, 
      ccp.product_id ?? null, 
      ccp.batch_number ?? null, 
      ccp.measurement_value,
      ccp.measurement_unit, 
      ccp.critical_limit_min ?? null, 
      ccp.critical_limit_max ?? null,
      ccp.is_within_limits ? 1 : 0, 
      ccp.monitoring_datetime, 
      ccp.monitored_by,
      ccp.equipment_used ?? null, 
      ccp.observations ?? null, 
      ccp.corrective_action_needed ? 1 : 0, 
      now, 
      ccp.created_by ?? null
    ]);

    return id;
  }

  async getCCPMonitoring(id: string): Promise<StagedCCPMonitoring | null> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_ccp_monitoring WHERE id = ?', [id]);
    if (results.length > 0 && results[0].values.length > 0) {
      const row = results[0].values[0];
      const columns = results[0].columns;
      const ccp: any = {};
      columns.forEach((col: string, index: number) => {
        ccp[col] = row[index];
      });
      return ccp as StagedCCPMonitoring;
    }
    return null;
  }

  // Temperature readings operations
  async createTemperatureReading(reading: Omit<StagedTemperatureReading, 'id' | 'created_at'>): Promise<string> {
    const { db } = this.ensureDb();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO staged_temperature_readings (
        id, sensor_id, storage_area_id, temp_celsius, temp_fahrenheit, humidity,
        recorded_at, within_safe_range, violation_type, notes, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      id, 
      reading.sensor_id, 
      reading.storage_area_id ?? null, 
      reading.temp_celsius,
      reading.temp_fahrenheit, 
      reading.humidity ?? null, 
      reading.recorded_at,
      reading.within_safe_range ? 1 : 0, 
      reading.violation_type ?? null, 
      reading.notes ?? null, 
      now, 
      reading.created_by ?? null
    ]);

    return id;
  }

  // Quality test operations
  async createQualityTest(test: Omit<StagedQualityTest, 'id' | 'created_at'>): Promise<string> {
    const { db } = this.ensureDb();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO staged_quality_tests (
        id, product_id, batch_number, test_type, test_date, laboratory, result,
        quantitative_result, detection_limit, compliant, certificate_number,
        test_method, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      id, 
      test.product_id, 
      test.batch_number ?? null, 
      test.test_type, 
      test.test_date,
      test.laboratory ?? null, 
      test.result, 
      test.quantitative_result ?? null, 
      test.detection_limit ?? null,
      test.compliant ? 1 : 0, 
      test.certificate_number ?? null, 
      test.test_method ?? null, 
      now, 
      test.created_by ?? null
    ]);

    return id;
  }

  // Allergen test operations
  async createAllergenTest(test: Omit<StagedAllergenTest, 'id' | 'created_at'>): Promise<string> {
    const { db } = this.ensureDb();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO staged_allergen_tests (
        id, product_id, lot_id, allergen_type, test_date, laboratory, test_method,
        result, quantitative_result, detection_limit, compliant, certificate_number,
        created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      id, 
      test.product_id, 
      test.lot_id, 
      test.allergen_type, 
      test.test_date,
      test.laboratory, 
      test.test_method, 
      test.result, 
      test.quantitative_result ?? null,
      test.detection_limit ?? null, 
      test.compliant ? 1 : 0, 
      test.certificate_number ?? null, 
      now, 
      test.created_by ?? null
    ]);

    return id;
  }

  // HACCP event operations
  async createHACCPEvent(event: Omit<StagedHACCPEvent, 'id' | 'created_at'>): Promise<string> {
    const { db } = this.ensureDb();
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO staged_haccp_events (
        id, event_type, product_id, batch_number, event_datetime, description,
        severity, reported_by, immediate_action, root_cause, corrective_action,
        preventive_measures, status, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      id, 
      event.event_type, 
      event.product_id ?? null, 
      event.batch_number ?? null, 
      event.event_datetime,
      event.description, 
      event.severity, 
      event.reported_by, 
      event.immediate_action ?? null,
      event.root_cause ?? null, 
      event.corrective_action ?? null, 
      event.preventive_measures ?? null,
      event.status || 'open', 
      now, 
      event.created_by ?? null
    ]);

    return id;
  }

  // Sync status operations
  async addSyncStatus(syncStatus: Omit<SyncStatus, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const { db } = this.ensureDb();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO sync_status (table_name, record_id, operation, status, error_message, created_at, updated_at, retry_count)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run([
      syncStatus.table_name, 
      syncStatus.record_id, 
      syncStatus.operation,
      syncStatus.status, 
      syncStatus.error_message ?? null, 
      now, 
      now, 
      syncStatus.retry_count || 0
    ]);

    // Get the last inserted row ID using a separate query
    const result = db.exec('SELECT last_insert_rowid() as id');
    return result[0].values[0][0] as number;
  }

  async getPendingSyncItems(): Promise<SyncStatus[]> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM sync_status WHERE status = ? ORDER BY created_at ASC', ['pending']);
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const item: any = {};
        columns.forEach((col: string, index: number) => {
          item[col] = row[index];
        });
        return item as SyncStatus;
      });
    }
    return [];
  }

  async updateSyncStatus(id: number, status: SyncStatus['status'], errorMessage?: string): Promise<void> {
    const { db } = this.ensureDb();
    const now = new Date().toISOString();

    const stmt = db.prepare('UPDATE sync_status SET status = ?, error_message = ?, updated_at = ? WHERE id = ?');
    stmt.run([status, errorMessage, now, id]);
  }

  // Confirmation queue operations
  async addToConfirmationQueue(item: Omit<ConfirmationQueueItem, 'id' | 'created_at'>): Promise<number> {
    const { db } = this.ensureDb();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO confirmation_queue (record_type, record_id, data, requires_confirmation, created_at)
      VALUES (?, ?, ?, ?, ?)
    `);

    stmt.run([
      item.record_type, 
      item.record_id, 
      JSON.stringify(item.data),
      item.requires_confirmation ? 1 : 0, 
      now
    ]);

    // Get the last inserted row ID using a separate query
    const result = db.exec('SELECT last_insert_rowid() as id');
    return result[0].values[0][0] as number;
  }

  async getPendingConfirmations(): Promise<ConfirmationQueueItem[]> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM confirmation_queue WHERE confirmed = FALSE ORDER BY created_at ASC');
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const item: any = {};
        columns.forEach((col: string, index: number) => {
          item[col] = row[index];
        });
        return {
          id: item.id,
          record_type: item.record_type,
          record_id: item.record_id,
          data: JSON.parse(item.data),
          requires_confirmation: Boolean(item.requires_confirmation),
          confirmed: Boolean(item.confirmed),
          confirmed_by: item.confirmed_by,
          confirmed_at: item.confirmed_at,
          created_at: item.created_at
        } as ConfirmationQueueItem;
      });
    }
    return [];
  }

  async confirmQueueItem(id: number, confirmedBy: string): Promise<void> {
    const { db } = this.ensureDb();
    const now = new Date().toISOString();

    const stmt = db.prepare('UPDATE confirmation_queue SET confirmed = TRUE, confirmed_by = ?, confirmed_at = ? WHERE id = ?');
    stmt.run([confirmedBy, now, id]);
  }

  // Get unsynced records for a specific table
  async getUnsyncedRecords(tableName: string): Promise<Record<string, unknown>[]> {
    const { db } = this.ensureDb();
    const results = db.exec(`SELECT * FROM ${tableName} WHERE synced_to_supabase = FALSE`);
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const record: any = {};
        columns.forEach((col: string, index: number) => {
          record[col] = row[index];
        });
        return record;
      });
    }
    return [];
  }

  // Mark records as synced
  async markRecordsAsSynced(tableName: string, recordIds: string[]): Promise<void> {
    const { db } = this.ensureDb();
    const placeholders = recordIds.map(() => '?').join(',');
    const stmt = db.prepare(`UPDATE ${tableName} SET synced_to_supabase = TRUE WHERE id IN (${placeholders})`);
    stmt.run(recordIds);
  }

  // Get database statistics
  async getStats(): Promise<{
    products: number;
    inventory: number;
    ccp_monitoring: number;
    temperature_readings: number;
    quality_tests: number;
    allergen_tests: number;
    haccp_events: number;
    pending_sync: number;
    pending_confirmations: number;
  }> {
    const { db } = this.ensureDb();

    const productsResult = db.exec('SELECT COUNT(*) as count FROM staged_products');
    const inventoryResult = db.exec('SELECT COUNT(*) as count FROM staged_inventory');
    const ccpResult = db.exec('SELECT COUNT(*) as count FROM staged_ccp_monitoring');
    const tempResult = db.exec('SELECT COUNT(*) as count FROM staged_temperature_readings');
    const qualityResult = db.exec('SELECT COUNT(*) as count FROM staged_quality_tests');
    const allergenResult = db.exec('SELECT COUNT(*) as count FROM staged_allergen_tests');
    const haccpResult = db.exec('SELECT COUNT(*) as count FROM staged_haccp_events');
    const syncResult = db.exec('SELECT COUNT(*) as count FROM sync_status WHERE status = ?', ['pending']);
    const confirmResult = db.exec('SELECT COUNT(*) as count FROM confirmation_queue WHERE confirmed = FALSE');

    return {
      products: productsResult[0]?.values[0]?.[0] as number || 0,
      inventory: inventoryResult[0]?.values[0]?.[0] as number || 0,
      ccp_monitoring: ccpResult[0]?.values[0]?.[0] as number || 0,
      temperature_readings: tempResult[0]?.values[0]?.[0] as number || 0,
      quality_tests: qualityResult[0]?.values[0]?.[0] as number || 0,
      allergen_tests: allergenResult[0]?.values[0]?.[0] as number || 0,
      haccp_events: haccpResult[0]?.values[0]?.[0] as number || 0,
      pending_sync: syncResult[0]?.values[0]?.[0] as number || 0,
      pending_confirmations: confirmResult[0]?.values[0]?.[0] as number || 0,
    };
  }

  // Get all inventory records
  async getAllInventory(): Promise<StagedInventory[]> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_inventory ORDER BY created_at DESC');
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const inventory: any = {};
        columns.forEach((col: string, index: number) => {
          inventory[col] = row[index];
        });
        return inventory as StagedInventory;
      });
    }
    return [];
  }

  // Update inventory record
  async updateInventory(id: string, updates: Partial<StagedInventory>): Promise<void> {
    const { db } = this.ensureDb();
    const now = new Date().toISOString();

    const fields = Object.keys(updates).filter(key => key !== 'id');
    const values = fields.map(key => updates[key as keyof StagedInventory]);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    const stmt = db.prepare(`UPDATE staged_inventory SET ${setClause}, updated_at = ? WHERE id = ?`);
    stmt.run([...values, now, id]);
  }

  // Get all HACCP events
  async getAllHACCPEvents(): Promise<StagedHACCPEvent[]> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_haccp_events ORDER BY event_datetime DESC');
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const event: any = {};
        columns.forEach((col: string, index: number) => {
          event[col] = row[index];
        });
        return event as StagedHACCPEvent;
      });
    }
    return [];
  }

  // Get CCP monitoring records
  async getAllCCPMonitoring(): Promise<StagedCCPMonitoring[]> {
    const { db } = this.ensureDb();
    const results = db.exec('SELECT * FROM staged_ccp_monitoring ORDER BY monitoring_datetime DESC');
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const ccp: any = {};
        columns.forEach((col: string, index: number) => {
          ccp[col] = row[index];
        });
        return ccp as StagedCCPMonitoring;
      });
    }
    return [];
  }

  /**
   * Retrieves inventory records created by the voice agent.
   * Useful for monitoring voice assistant activity and debugging.
   * 
   * @param limit - Maximum number of records to return (default: 20)
   * @returns Array of StagedInventory records with created_by='voice-agent'
   * @example
   * const recent = await sqliteService.getVoiceInventoryRecords(10);
   * console.log(`Found ${recent.length} voice-created records`);
   */
  async getVoiceInventoryRecords(limit: number = 20): Promise<StagedInventory[]> {
    const { db } = this.ensureDb();
    
    const results = db.exec(
      `SELECT * FROM staged_inventory 
       WHERE created_by = 'voice-agent' 
       ORDER BY created_at DESC 
       LIMIT ?`,
      [limit]
    );
    
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const inventory: any = {};
        columns.forEach((col: string, index: number) => {
          inventory[col] = row[index];
        });
        return inventory as StagedInventory;
      });
    }
    return [];
  }

  /**
   * Retrieves CCP monitoring records created by the voice agent.
   * Useful for monitoring voice assistant activity and debugging.
   * 
   * @param limit - Maximum number of records to return (default: 20)
   * @returns Array of StagedCCPMonitoring records with created_by='voice-agent'
   * @example
   * const recent = await sqliteService.getVoiceCCPRecords(10);
   * console.log(`Found ${recent.length} voice-created CCP records`);
   */
  async getVoiceCCPRecords(limit: number = 20): Promise<StagedCCPMonitoring[]> {
    const { db } = this.ensureDb();
    
    const results = db.exec(
      `SELECT * FROM staged_ccp_monitoring 
       WHERE created_by = 'voice-agent' 
       ORDER BY created_at DESC 
       LIMIT ?`,
      [limit]
    );
    
    if (results.length > 0) {
      const columns = results[0].columns;
      return results[0].values.map((row: any[]) => {
        const ccp: any = {};
        columns.forEach((col: string, index: number) => {
          ccp[col] = row[index];
        });
        return ccp as StagedCCPMonitoring;
      });
    }
    return [];
  }

  // Close database connection
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

// Export singleton instance
export const sqliteService = new SQLiteService();