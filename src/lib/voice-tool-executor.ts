import { sqliteService, StagedInventory, StagedCCPMonitoring } from './sqlite-service';
import { validateToolArguments, formatValidationError } from './voice-validation';
import {
  classifyError,
  formatErrorForUser,
  logError,
  VoiceErrorType,
  VoiceAgentError
} from './voice-error-handler';

export interface ToolCall {
  name: string;
  arguments: Record<string, unknown>;
  call_id?: string;
}

export interface ToolResult {
  success: boolean;
  data?: unknown;
  message: string;
  error?: string;
  errorType?: VoiceErrorType;
}

export class VoiceToolExecutor {
  /**
   * Execute a tool call with validation and comprehensive error handling
   */
  static async executeTool(tool: ToolCall): Promise<ToolResult> {
    const { name, arguments: args } = tool;

    try {
      // Validate database is ready
      if (!sqliteService.isReady()) {
        logError(
          new VoiceAgentError(
            VoiceErrorType.DATABASE,
            'Database not initialized',
            { toolName: name }
          ),
          `Tool execution: ${name}`,
          { stage: 'initialization' }
        );

        return {
          success: false,
          message: 'Database not ready',
          error: 'Please wait for the application to initialize',
          errorType: VoiceErrorType.DATABASE
        };
      }

      // Execute tool with a timeout
      const result = await Promise.race([
        this.executeToolInternal(name, args),
        new Promise<ToolResult>((_, reject) =>
          setTimeout(
            () => reject(new VoiceAgentError(
              VoiceErrorType.TIMEOUT,
              'Tool execution timeout',
              { toolName: name, timeout: 30000 }
            )),
            30000 // 30 second timeout
          )
        )
      ]);

      return result;
    } catch (error) {
      const classified = classifyError(error, `Tool execution: ${name}`);
      logError(error, `Tool execution: ${name}`, { toolCall: tool });

      return {
        success: false,
        message: classified.message,
        error: classified.message,
        errorType: classified.type
      };
    }
  }

  /**
   * Internal tool execution with argument validation
   */
  private static async executeToolInternal(
    name: string,
    args: Record<string, unknown>
  ): Promise<ToolResult> {
    try {
      // Validate arguments based on tool type
      const validatedArgs = validateToolArguments(name, args);

      switch (name) {
        case 'add_inventory_event':
          return await this.addInventoryEvent(validatedArgs);

        case 'update_product_location':
          return await this.updateProductLocation(validatedArgs);

        case 'check_product_status':
          return await this.checkProductStatus(validatedArgs);

        case 'get_haccp_events':
          return await this.getHACCPEvents(validatedArgs);

        case 'record_ccp_monitoring':
          return await this.recordCCPMonitoring(validatedArgs);

        default:
          return {
            success: false,
            message: `Unknown tool: ${name}`,
            error: `Tool ${name} is not supported`,
            errorType: VoiceErrorType.VALIDATION
          };
      }
    } catch (error) {
      // Validation error
      if (error instanceof Error && error.message.includes('Validation')) {
        return {
          success: false,
          message: error.message,
          error: error.message,
          errorType: VoiceErrorType.VALIDATION
        };
      }

      // Re-throw for outer error handler
      throw error;
    }
  }

  /**
   * Add a new inventory event/stock transaction
   */
  private static async addInventoryEvent(args: Record<string, unknown>): Promise<ToolResult> {
    try {
      const {
        product_id,
        batch_number,
        quantity,
        unit = 'kg',
        location,
        notes
      } = args;

      const inventoryData: Omit<StagedInventory, 'id' | 'created_at' | 'updated_at'> = {
        product_id: product_id as string,
        batch_number: batch_number as string,
        quantity: quantity as number,
        unit: (unit as string) || undefined,
        received_date: new Date().toISOString().split('T')[0],
        location: (location as string) || undefined,
        notes: (notes as string) || undefined,
        created_by: 'voice-agent'
      };

      const inventoryId = await sqliteService.createInventory(inventoryData);

      // Log the creation for visibility
      console.log('✅ Voice Agent: Inventory Record Created');
      console.table({
        'Record ID': inventoryId,
        'Product ID': product_id,
        'Batch Number': batch_number,
        'Quantity': `${quantity} ${unit}`,
        'Location': location || 'Not specified',
        'Created By': 'voice-agent',
        'Timestamp': new Date().toISOString()
      });

      return {
        success: true,
        data: { inventory_id: inventoryId },
        message: `Added ${quantity} ${unit} of product to inventory (batch: ${batch_number})`
      };
    } catch (error) {
      const classified = classifyError(error, 'addInventoryEvent');
      logError(error, 'addInventoryEvent', { args });

      return {
        success: false,
        message: classified.message,
        error: classified.message,
        errorType: classified.type
      };
    }
  }

  /**
   * Update the storage location of a product batch
   */
  private static async updateProductLocation(args: Record<string, unknown>): Promise<ToolResult> {
    try {
      const { product_id, batch_number, new_location } = args;

      // Get existing inventory records for this batch
      const inventoryRecords = await sqliteService.getInventoryByBatch(batch_number as string);
      const record = inventoryRecords.find(r => r.product_id === product_id);

      if (!record) {
        return {
          success: false,
          message: 'Inventory record not found',
          error: `No inventory found for product ${product_id} batch ${batch_number}`,
          errorType: VoiceErrorType.DATABASE
        };
      }

      // Update location
      await sqliteService.updateInventory(record.id, {
        location: new_location as string
      });

      const message = `Moved ${batch_number} to ${new_location}`;

      return {
        success: true,
        data: { inventory_id: record.id },
        message
      };
    } catch (error) {
      const classified = classifyError(error, 'updateProductLocation');
      logError(error, 'updateProductLocation', { args });

      return {
        success: false,
        message: classified.message,
        error: classified.message,
        errorType: classified.type
      };
    }
  }

  /**
   * Check the current status and inventory of a product
   */
  private static async checkProductStatus(args: Record<string, unknown>): Promise<ToolResult> {
    try {
      const { product_id } = args;

      const product = await sqliteService.getProduct(product_id as string);

      if (!product) {
        return {
          success: false,
          message: `Product not found`,
          error: `Product ${product_id} does not exist`,
          errorType: VoiceErrorType.DATABASE
        };
      }

      const allInventory = await sqliteService.getAllInventory();
      const batches = allInventory.filter(b => b.product_id === product_id);

      const statusInfo = {
        product_name: product.name,
        product_id: product.id,
        category: product.category,
        current_stock: product.current_stock,
        min_stock: product.min_stock,
        batches: batches.map(b => ({
          batch_number: b.batch_number,
          quantity: b.quantity,
          unit: b.unit,
          location: b.location,
          expiry_date: b.expiry_date,
          quality_status: b.quality_status
        }))
      };

      const message = `Product: ${product.name} - Current Stock: ${product.current_stock}, Minimum: ${product.min_stock}. Found ${batches.length} batch(es).`;

      return {
        success: true,
        data: statusInfo,
        message
      };
    } catch (error) {
      const classified = classifyError(error, 'checkProductStatus');
      logError(error, 'checkProductStatus', { args });

      return {
        success: false,
        message: classified.message,
        error: classified.message,
        errorType: classified.type
      };
    }
  }

  /**
   * Retrieve HACCP event records for compliance monitoring
   */
  private static async getHACCPEvents(args: Record<string, unknown>): Promise<ToolResult> {
    try {
      const { product_id, limit = 10 } = args;

      const events = await sqliteService.getAllHACCPEvents();

      // Filter by product_id if provided
      const filtered = product_id ? events.filter(e => e.product_id === product_id) : events;

      // Sort by most recent first and limit
      const results = filtered
        .sort((a, b) => new Date(b.event_datetime).getTime() - new Date(a.event_datetime).getTime())
        .slice(0, (limit as number) || 10);

      const message = `Found ${results.length} HACCP event(s)${product_id ? ` for product ${product_id}` : ''}`;

      return {
        success: true,
        data: results.map(e => ({
          event_type: e.event_type,
          product_id: e.product_id,
          severity: e.severity,
          description: e.description,
          event_datetime: e.event_datetime,
          status: e.status
        })),
        message
      };
    } catch (error) {
      const classified = classifyError(error, 'getHACCPEvents');
      logError(error, 'getHACCPEvents', { args });

      return {
        success: false,
        message: classified.message,
        error: classified.message,
        errorType: classified.type
      };
    }
  }

  /**
   * Record a Critical Control Point (CCP) monitoring measurement
   */
  private static async recordCCPMonitoring(args: Record<string, unknown>): Promise<ToolResult> {
    try {
      const {
        ccp_name,
        measurement_value,
        measurement_unit,
        monitored_by,
        notes
      } = args;

      // Determine if measurement is within limits (basic validation)
      const isWithinLimits = true; // Simplified for now

      const ccpData: Omit<StagedCCPMonitoring, 'id' | 'created_at'> = {
        ccp_name: ccp_name as string,
        measurement_value: measurement_value as number,
        measurement_unit: measurement_unit as string,
        critical_limit_min: undefined,
        critical_limit_max: undefined,
        is_within_limits: isWithinLimits,
        monitoring_datetime: new Date().toISOString(),
        monitored_by: monitored_by as string,
        observations: (notes as string) || undefined,
        product_id: undefined,
        corrective_action_needed: !isWithinLimits,
        synced_to_supabase: false,
        sync_confirmed: false,
        created_by: 'voice-agent'
      };

      const ccpId = await sqliteService.createCCPMonitoring(ccpData);

      // Log the creation for visibility
      console.log('✅ Voice Agent: CCP Monitoring Record Created');
      console.table({
        'Record ID': ccpId,
        'CCP Name': ccp_name,
        'Measurement': `${measurement_value} ${measurement_unit}`,
        'Within Limits': isWithinLimits ? 'Yes' : 'No',
        'Monitored By': monitored_by,
        'Created By': 'voice-agent',
        'Timestamp': new Date().toISOString()
      });

      const message = `Recorded CCP monitoring: ${ccp_name} = ${measurement_value} ${measurement_unit}${!isWithinLimits ? ' - Corrective action may be needed' : ' ✓ Recorded'}`;

      return {
        success: true,
        data: { ccp_id: ccpId, is_within_limits: isWithinLimits },
        message
      };
    } catch (error) {
      const classified = classifyError(error, 'recordCCPMonitoring');
      logError(error, 'recordCCPMonitoring', { args });

      return {
        success: false,
        message: classified.message,
        error: classified.message,
        errorType: classified.type
      };
    }
  }

  /**
   * Format tool result for voice feedback
   */
  static formatResultForVoice(result: ToolResult): string {
    if (result.success) {
      return result.message;
    } else {
      return `Error: ${result.message}. ${result.error || ''}`.trim();
    }
  }
}

export default VoiceToolExecutor;
