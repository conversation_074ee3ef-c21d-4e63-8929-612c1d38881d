/**
 * WebRTC Client for OpenAI Realtime API
 *
 * Handles peer connection, data channels, and media streams for
 * low-latency bidirectional voice communication with OpenAI.
 *
 * Key components:
 * - RTCPeerConnection: WebRTC peer connection for audio streaming
 * - RTCDataChannel: Data channel for sending/receiving events
 * - MediaStream: Microphone input and speaker output
 *
 * @see https://platform.openai.com/docs/guides/realtime#webrtc-connection
 */

export interface WebRTCClientConfig {
  ephemeralKey: string;
  model: string;
  voice: string;
  onMessage: (message: RealtimeEvent) => void;
  onAudioData?: (audioData: Uint8Array) => void;
  onError: (error: Error) => void;
  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
}

/**
 * OpenAI Realtime API event types
 */
export interface RealtimeEvent {
  type: string;
  [key: string]: unknown;
}

/**
 * Session configuration for OpenAI
 */
export interface SessionConfig {
  modalities: string[];          // ['text', 'audio']
  instructions: string;          // System prompt
  voice: string;                 // Voice ID
  input_audio_format: 'pcm16';   // Required format
  output_audio_format: 'pcm16';  // Required format
  turn_detection: {              // Voice Activity Detection
    type: 'server_vad';
    threshold?: number;
    prefix_padding_ms?: number;
    silence_duration_ms?: number;
  };
  temperature: number;           // 0-1
  max_response_output_tokens: number | string;
}

export class RealtimeWebRTCClient {
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private audioStream: MediaStream | null = null;
  private sessionUpdateAckTimeout: number | null = null;
  private dataChannelReadyPromise: Promise<void> | null = null;
  private config: WebRTCClientConfig;

  constructor(config: WebRTCClientConfig) {
    this.config = config;
  }

  /**
   * Establish WebRTC connection to OpenAI Realtime API
   */
  async connect(): Promise<void> {
    try {
      console.log('🔌 [WebRTC] Starting connection...');

      // 1. Create peer connection with STUN server
      this.peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      // Monitor connection state
      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection!.connectionState;
        console.log(`🔌 [WebRTC] Connection state: ${state}`);
        this.config.onConnectionStateChange?.(state);

        if (state === 'failed' || state === 'closed') {
          this.config.onError(new Error(`Connection ${state}`));
        }
      };

      // 2. Set up data channel for events
      this.dataChannel = this.peerConnection.createDataChannel('oai-events', {
        ordered: true
      });
      this.setupDataChannel();

      // 3. Get user media (microphone) with optimized constraints
      console.log('🎤 [WebRTC] Requesting microphone access...');
      try {
        this.audioStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: { ideal: 24000 },
            channelCount: { ideal: 1 },
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            // Add these for better compatibility
            latency: { ideal: 0.01 },
            volume: { ideal: 1.0 }
          }
        });
      } catch (error) {
        console.warn('⚠️ [WebRTC] Failed with ideal constraints, trying basic constraints...', error);
        // Fallback to basic constraints
        this.audioStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });
      }

      console.log('🎤 [WebRTC] Microphone access granted');

      // Log audio stream details for debugging
      const audioTrack = this.audioStream.getAudioTracks()[0];
      if (audioTrack) {
        const settings = audioTrack.getSettings();
        console.log('🎤 [Audio Track] Settings:', {
          sampleRate: settings.sampleRate,
          channelCount: settings.channelCount,
          echoCancellation: settings.echoCancellation,
          noiseSuppression: settings.noiseSuppression,
          autoGainControl: settings.autoGainControl,
          deviceId: settings.deviceId
        });
      }

      // 4. Add audio tracks to peer connection
      this.audioStream.getTracks().forEach(track => {
        console.log('➕ [WebRTC] Adding local audio track');
        this.peerConnection!.addTrack(track, this.audioStream!);
      });

      // 5. Handle incoming audio tracks from OpenAI
      this.peerConnection.ontrack = (event) => {
        console.log('📡 [WebRTC] Received remote audio track');

        // Play received audio through speakers
        const audioElement = new Audio();
        audioElement.srcObject = event.streams[0];
        audioElement.play().catch(err => {
          console.error('❌ [WebRTC] Failed to play audio:', err);
        });
      };

      // 6. Create SDP offer
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);
      console.log('📤 [WebRTC] Created and set local description (offer)');

      // 7. Send offer to OpenAI and get answer
      const answer = await this.sendOfferToOpenAI(offer);
      await this.peerConnection.setRemoteDescription(answer);
      console.log('📥 [WebRTC] Set remote description (answer)');

      // 8. Wait for connection to establish
      await this.waitForConnection();

      // 9. Wait for data channel readiness
      await this.waitForDataChannelReady();

      // 10. Send session configuration via data channel
      this.sendSessionConfig();

      console.log('✅ [WebRTC] Connection established successfully');
    } catch (error) {
      console.error('❌ [WebRTC] Connection failed:', error);
      this.config.onError(error as Error);
      throw error;
    }
  }

  /**
   * Set up data channel event handlers
   */
  private setupDataChannel(): void {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      console.log('✅ [DataChannel] Opened - ready to send/receive events');
    };

    this.dataChannel.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        // Log message type for debugging
        console.log(`📩 [DataChannel] Received: ${message.type}`);

        if (message.type === 'session.updated') {
          console.log('✅ [Session] OpenAI accepted session config', {
            modalities: message.session?.modalities,
            voice: message.session?.voice,
            toolCount: message.session?.tools?.length
          });
          if (this.sessionUpdateAckTimeout) {
            clearTimeout(this.sessionUpdateAckTimeout);
            this.sessionUpdateAckTimeout = null;
          }
        }

        if (message.type === 'error' || message.type === 'session.error') {
          console.error('❌ [Session] Error from OpenAI', message);
        }

        if (typeof message.type === 'string' && message.type.endsWith('.failed')) {
          console.warn('⚠️ [Realtime] Event failed', {
            type: message.type,
            reason: message.error || message.response?.error || message.item?.error,
            raw: message
          });
        }

        // Forward to message handler
        this.config.onMessage(message);

        // If audio data included, forward to audio handler
        if (message.type === 'response.audio.delta' && message.delta) {
          const audioData = this.base64ToUint8Array(message.delta);
          this.config.onAudioData?.(audioData);
        }
      } catch (error) {
        console.error('❌ [DataChannel] Failed to parse message:', error);
      }
    };

    this.dataChannel.onerror = (error) => {
      console.error('❌ [DataChannel] Error:', error);
      this.config.onError(new Error('Data channel error'));
    };

    this.dataChannel.onclose = () => {
      console.log('🔌 [DataChannel] Closed');
    };
  }

  /**
   * Wait until the data channel is open before sending messages
   */
  private waitForDataChannelReady(timeoutMs = 5000): Promise<void> {
    if (!this.dataChannel) {
      return Promise.reject(new Error('Data channel not initialized'));
    }

    if (this.dataChannel.readyState === 'open') {
      return Promise.resolve();
    }

    if (!this.dataChannelReadyPromise) {
      this.dataChannelReadyPromise = new Promise((resolve, reject) => {
        const timeout = window.setTimeout(() => {
          reject(new Error('Data channel failed to open in time'));
        }, timeoutMs);

        const handleOpen = () => {
          clearTimeout(timeout);
          this.dataChannel?.removeEventListener('open', handleOpen);
          resolve();
        };

        this.dataChannel.addEventListener('open', handleOpen);
      });
    }

    return this.dataChannelReadyPromise;
  }

  /**
   * Send SDP offer to OpenAI and receive answer
   */
  private async sendOfferToOpenAI(
    offer: RTCSessionDescriptionInit
  ): Promise<RTCSessionDescriptionInit> {
    console.log('📤 [WebRTC] Sending SDP offer to OpenAI...');

    const response = await fetch('https://api.openai.com/v1/realtime', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.ephemeralKey}`,
        'Content-Type': 'application/sdp'
      },
      body: offer.sdp
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [WebRTC] SDP exchange failed:', errorText);
      throw new Error(`OpenAI SDP exchange failed: ${response.statusText}`);
    }

    const answerSdp = await response.text();
    console.log('📥 [WebRTC] Received SDP answer from OpenAI');

    return {
      type: 'answer',
      sdp: answerSdp
    };
  }

  /**
   * Wait for peer connection to reach 'connected' state
   */
  private async waitForConnection(timeoutMs = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, timeoutMs);

      const checkState = () => {
        const state = this.peerConnection?.connectionState;
        if (state === 'connected') {
          clearTimeout(timeout);
          resolve();
        } else if (state === 'failed' || state === 'closed') {
          clearTimeout(timeout);
          reject(new Error(`Connection ${state}`));
        }
      };

      this.peerConnection!.addEventListener('connectionstatechange', checkState);
      checkState(); // Check immediately in case already connected
    });
  }

  /**
   * Send session configuration to OpenAI
   */
  private sendSessionConfig(): void {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      console.warn('[WebRTC] Data channel not ready, cannot send session config');
      return;
    }

    const sessionConfig = {
      type: 'session.update',
      session: {
        modalities: ['text', 'audio'],
        instructions: 'You are a helpful voice assistant for managing seafood inventory and food safety compliance. Help users with inventory operations, temperature monitoring, quality testing, and HACCP documentation. Keep responses concise and action-oriented.',
        voice: this.config.voice,
        input_audio_format: 'pcm16',
        output_audio_format: 'pcm16',
        turn_detection: {
          type: 'server_vad',
          threshold: 0.5,
          prefix_padding_ms: 300,
          silence_duration_ms: 1000
        },
        temperature: 0.8,
        max_response_output_tokens: 'inf',
        tools: [
          {
            type: 'function',
            name: 'add_inventory_event',
            description: 'Add a new inventory event or stock transaction. Use this when a user wants to record a new inventory addition, receipt, or stock adjustment.',
            parameters: {
              type: 'object',
              properties: {
                product_id: { type: 'string', description: 'The unique identifier of the product' },
                batch_number: { type: 'string', description: 'The batch or lot number for the inventory' },
                quantity: { type: 'number', description: 'The quantity being added' },
                unit: { type: 'string', description: 'The unit of measurement (e.g., kg, lbs, boxes)', enum: ['kg', 'lbs', 'boxes', 'units', 'liters', 'gallons'] },
                location: { type: 'string', description: 'Storage location for the inventory' },
                notes: { type: 'string', description: 'Additional notes about the inventory event' }
              },
              required: ['product_id', 'batch_number', 'quantity', 'unit']
            }
          },
          {
            type: 'function',
            name: 'update_product_location',
            description: 'Move or update the storage location of a product batch.',
            parameters: {
              type: 'object',
              properties: {
                product_id: { type: 'string', description: 'The unique identifier of the product' },
                batch_number: { type: 'string', description: 'The batch or lot number' },
                new_location: { type: 'string', description: 'The new storage location' },
                reason: { type: 'string', description: 'Reason for the location change', enum: ['rotation', 'restocking', 'quarantine', 'consolidation', 'audit', 'other'] },
                notes: { type: 'string', description: 'Additional notes about the move' }
              },
              required: ['product_id', 'batch_number', 'new_location']
            }
          },
          {
            type: 'function',
            name: 'check_product_status',
            description: 'Query the current status and inventory details of a product.',
            parameters: {
              type: 'object',
              properties: {
                product_id: { type: 'string', description: 'The unique identifier of the product to check' },
                batch_number: { type: 'string', description: 'Optional: specific batch number to check' }
              },
              required: ['product_id']
            }
          },
          {
            type: 'function',
            name: 'get_haccp_events',
            description: 'Retrieve HACCP event records for compliance monitoring and audit trails.',
            parameters: {
              type: 'object',
              properties: {
                product_id: { type: 'string', description: 'Optional: filter by product ID' },
                severity: { type: 'string', description: 'Filter by event severity level', enum: ['critical', 'major', 'minor', 'informational'] },
                limit: { type: 'integer', description: 'Maximum number of records to return', default: 10, minimum: 1, maximum: 100 }
              }
            }
          },
          {
            type: 'function',
            name: 'record_ccp_monitoring',
            description: 'Record a Critical Control Point (CCP) monitoring measurement for HACCP compliance.',
            parameters: {
              type: 'object',
              properties: {
                ccp_name: { type: 'string', description: 'Name of the critical control point being monitored' },
                measurement_value: { type: 'number', description: 'The measured value' },
                measurement_unit: { type: 'string', description: 'Unit of the measurement (e.g., °C, pH, minutes)' },
                critical_limit_min: { type: 'number', description: 'Minimum acceptable value for this CCP' },
                critical_limit_max: { type: 'number', description: 'Maximum acceptable value for this CCP' },
                product_id: { type: 'string', description: 'Optional: product ID if monitoring is product-specific' },
                monitored_by: { type: 'string', description: 'Name or ID of the person performing the monitoring' },
                observations: { type: 'string', description: 'Additional observations or notes' }
              },
              required: ['ccp_name', 'measurement_value', 'measurement_unit', 'monitored_by']
            }
          }
        ]
      }
    };

    console.log('📤 [WebRTC] Sending session configuration:', {
      modalities: sessionConfig.session.modalities,
      voice: sessionConfig.session.voice,
      toolCount: sessionConfig.session.tools.length,
      formats: {
        input: sessionConfig.session.input_audio_format,
        output: sessionConfig.session.output_audio_format
      }
    });

    this.dataChannel.send(JSON.stringify(sessionConfig));

    if (this.sessionUpdateAckTimeout) {
      clearTimeout(this.sessionUpdateAckTimeout);
    }
    this.sessionUpdateAckTimeout = window.setTimeout(() => {
      console.warn('⚠️ [Session] No session.updated acknowledgment received within 3s', {
        dataChannelState: this.dataChannel?.readyState,
        peerConnectionState: this.peerConnection?.connectionState
      });
    }, 3000);

    // Send initial response.create to start listening
    setTimeout(() => {
      this.sendMessage({
        type: 'response.create',
        response: {
          modalities: ['text', 'audio'],
          instructions: 'Please respond with audio.'
        }
      });
      console.log('📤 [WebRTC] Sent initial response.create with audio modality');
    }, 500);
  }

  /**
   * Send message/event via data channel
   */
  sendMessage(message: RealtimeEvent): void {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      throw new Error('Data channel not ready');
    }
    this.dataChannel.send(JSON.stringify(message));
    console.log(`📤 [DataChannel] Sent: ${message.type}`);
  }

  /**
   * Get the current connection state
   */
  getConnectionState(): RTCPeerConnectionState | 'not-connected' {
    return this.peerConnection?.connectionState || 'not-connected';
  }

  /**
   * Convert base64 audio to Uint8Array
   */
  private base64ToUint8Array(base64: string): Uint8Array {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * Disconnect and cleanup all resources
   */
  disconnect(): void {
    console.log('🔌 [WebRTC] Disconnecting...');

    // Close data channel
    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }
    this.dataChannelReadyPromise = null;

    // Close peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Stop media streams
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => {
        track.stop();
        console.log('🛑 [WebRTC] Stopped audio track');
      });
      this.audioStream = null;
    }

    console.log('✅ [WebRTC] Disconnected successfully');
  }
}
