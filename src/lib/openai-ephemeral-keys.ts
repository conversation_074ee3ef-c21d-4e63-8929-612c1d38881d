/**
 * OpenAI Ephemeral Key Service
 *
 * Generates short-lived API keys for browser-safe WebRTC connections.
 * Ephemeral keys expire after ~60 seconds and are safe for client-side use.
 */

export interface EphemeralKeyResponse {
  client_secret: {
    value: string;               // Actual ephemeral key
    expires_at: number;          // Unix timestamp
  };
}

/**
 * Get ephemeral API key for WebRTC connection
 * Ephemeral keys are short-lived (~60s) and safe for browser use
 *
 * @returns {Promise<string>} Ephemeral key value
 * @throws {Error} If key generation fails
 */
export async function getEphemeralKey(): Promise<string> {
  try {
    console.log('🔑 Requesting ephemeral key from backend...')

    // For local development, use local Express endpoint
    const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001'

    const response = await fetch(
      `${backendUrl}/api/voice/ephemeral-key`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Failed to get ephemeral key:', errorText)
      throw new Error(`Failed to get ephemeral key: ${response.statusText}`)
    }

    const data: EphemeralKeyResponse = await response.json()

    if (!data.client_secret?.value) {
      throw new Error('Invalid ephemeral key response')
    }

    console.log('✅ Ephemeral key obtained', {
      expiresAt: new Date(data.client_secret.expires_at * 1000).toISOString(),
      expiresIn: `${Math.round((data.client_secret.expires_at * 1000 - Date.now()) / 1000)}s`
    })

    return data.client_secret.value
  } catch (error) {
    console.error('❌ Failed to get ephemeral key:', error)
    throw error
  }
}
