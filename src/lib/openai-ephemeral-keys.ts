/**
 * OpenAI Ephemeral Key Service
 *
 * Generates short-lived API keys for browser-safe WebRTC connections.
 * Ephemeral keys expire after ~60 seconds and are safe for client-side use.
 */

export interface EphemeralKeyResponse {
  value: string;                 // Actual ephemeral key (starts with ek_)
  expires_at?: number;           // Unix timestamp (optional)
}

/**
 * Get ephemeral API key for WebRTC connection
 * Ephemeral keys are short-lived (~60s) and safe for browser use
 *
 * @returns {Promise<string>} Ephemeral key value
 * @throws {Error} If key generation fails
 */
export async function getEphemeralKey(): Promise<string> {
  try {
    console.log('🔑 Requesting ephemeral key from backend...')

    // For local development, use local Express endpoint
    const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001'

    const response = await fetch(
      `${backendUrl}/api/voice/ephemeral-key`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Failed to get ephemeral key:', errorText)
      throw new Error(`Failed to get ephemeral key: ${response.statusText}`)
    }

    const data: EphemeralKeyResponse = await response.json()

    if (!data.value) {
      throw new Error('Invalid ephemeral key response')
    }

    console.log('✅ Ephemeral key obtained', {
      value: data.value.substring(0, 10) + '...',
      expiresAt: data.expires_at ? new Date(data.expires_at * 1000).toISOString() : 'Unknown',
      expiresIn: data.expires_at ? `${Math.round((data.expires_at * 1000 - Date.now()) / 1000)}s` : 'Unknown'
    })

    return data.value
  } catch (error) {
    console.error('❌ Failed to get ephemeral key:', error)
    throw error
  }
}
