import { useState, useEffect, useCallback } from 'react';
import { sqliteService } from '@/lib/sqlite-service';
import { syncService } from '@/lib/sync-service';
import {
  StagedProduct,
  StagedInventory,
  StagedCCPMonitoring,
  StagedTemperatureReading,
  StagedQualityTest,
  StagedAllergenTest,
  StagedHACCPEvent,
  SyncStatus,
  ConfirmationQueueItem
} from '@/lib/sqlite-service';
import { SyncResult, SyncOptions } from '@/lib/sync-service';

export interface StagingStats {
  products: number;
  inventory: number;
  ccp_monitoring: number;
  temperature_readings: number;
  quality_tests: number;
  allergen_tests: number;
  haccp_events: number;
  pending_sync: number;
  pending_confirmations: number;
}

export interface UseSQLiteStagingReturn {
  // Initialization
  isInitialized: boolean;
  initializeError: string | null;

  // Data operations
  createProduct: (product: Omit<StagedProduct, 'id' | 'created_at' | 'updated_at'>) => Promise<string>;
  createInventory: (inventory: Omit<StagedInventory, 'id' | 'created_at' | 'updated_at'>) => Promise<string>;
  createCCPMonitoring: (ccp: Omit<StagedCCPMonitoring, 'id' | 'created_at'>) => Promise<string>;
  createTemperatureReading: (reading: Omit<StagedTemperatureReading, 'id' | 'created_at'>) => Promise<string>;
  createQualityTest: (test: Omit<StagedQualityTest, 'id' | 'created_at'>) => Promise<string>;
  createAllergenTest: (test: Omit<StagedAllergenTest, 'id' | 'created_at'>) => Promise<string>;
  createHACCPEvent: (event: Omit<StagedHACCPEvent, 'id' | 'created_at'>) => Promise<string>;

  // Data retrieval
  getAllProducts: () => Promise<StagedProduct[]>;
  getAllInventory: () => Promise<StagedInventory[]>;
  getStats: () => Promise<StagingStats>;

  // Sync operations
  startSync: (options?: SyncOptions) => Promise<SyncResult>;
  stopSync: () => Promise<void>;
  isSyncRunning: boolean;
  getSyncStatus: () => Promise<{
    isRunning: boolean;
    pendingRecords: number;
    pendingConfirmations: number;
  }>;

  // Confirmation workflow
  getPendingConfirmations: () => Promise<ConfirmationQueueItem[]>;
  confirmAndSync: (queueItemId: number, confirmedBy: string) => Promise<boolean>;

  // Sync history
  getSyncHistory: () => Promise<SyncStatus[]>;
}

export function useSQLiteStaging(): UseSQLiteStagingReturn {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initializeError, setInitializeError] = useState<string | null>(null);
  const [isSyncRunning, setIsSyncRunning] = useState(false);

  // Initialize SQLite database
  useEffect(() => {
    const initialize = async () => {
      try {
        await syncService.initialize();
        setIsInitialized(true);
        setInitializeError(null);
      } catch (error) {
        console.error('Failed to initialize SQLite staging:', error);
        setInitializeError(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    initialize();
  }, []);

  // Product operations
  const createProduct = useCallback(async (product: Omit<StagedProduct, 'id' | 'created_at' | 'updated_at'>): Promise<string> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    const id = await sqliteService.createProduct(product);

    // Add to confirmation queue if required
    await sqliteService.addToConfirmationQueue({
      record_type: 'staged_products',
      record_id: id,
      data: { id, ...product },
      requires_confirmation: true // Products usually need confirmation
    });

    return id;
  }, [isInitialized]);

  const createInventory = useCallback(async (inventory: Omit<StagedInventory, 'id' | 'created_at' | 'updated_at'>): Promise<string> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    const id = await sqliteService.createInventory(inventory);

    // Add to confirmation queue for high-value inventory
    const requiresConfirmation = (inventory.quantity > 1000) || (inventory.quality_status === 'failed');
    await sqliteService.addToConfirmationQueue({
      record_type: 'staged_inventory',
      record_id: id,
      data: { id, ...inventory },
      requires_confirmation: requiresConfirmation
    });

    return id;
  }, [isInitialized]);

  const createCCPMonitoring = useCallback(async (ccp: Omit<StagedCCPMonitoring, 'id' | 'created_at'>): Promise<string> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    const id = await sqliteService.createCCPMonitoring(ccp);

    // CCP monitoring always requires confirmation for safety
    await sqliteService.addToConfirmationQueue({
      record_type: 'staged_ccp_monitoring',
      record_id: id,
      data: { id, ...ccp },
      requires_confirmation: true
    });

    return id;
  }, [isInitialized]);

  const createTemperatureReading = useCallback(async (reading: Omit<StagedTemperatureReading, 'id' | 'created_at'>): Promise<string> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    const id = await sqliteService.createTemperatureReading(reading);

    // Temperature readings usually don't need confirmation unless out of range
    const requiresConfirmation = !reading.within_safe_range;
    if (requiresConfirmation) {
      await sqliteService.addToConfirmationQueue({
        record_type: 'staged_temperature_readings',
        record_id: id,
        data: { id, ...reading },
        requires_confirmation: true
      });
    }

    return id;
  }, [isInitialized]);

  const createQualityTest = useCallback(async (test: Omit<StagedQualityTest, 'id' | 'created_at'>): Promise<string> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    const id = await sqliteService.createQualityTest(test);

    // Quality tests require confirmation if non-compliant
    await sqliteService.addToConfirmationQueue({
      record_type: 'staged_quality_tests',
      record_id: id,
      data: { id, ...test },
      requires_confirmation: !test.compliant
    });

    return id;
  }, [isInitialized]);

  const createAllergenTest = useCallback(async (test: Omit<StagedAllergenTest, 'id' | 'created_at'>): Promise<string> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    const id = await sqliteService.createAllergenTest(test);

    // Allergen tests always require confirmation for safety
    await sqliteService.addToConfirmationQueue({
      record_type: 'staged_allergen_tests',
      record_id: id,
      data: { id, ...test },
      requires_confirmation: true
    });

    return id;
  }, [isInitialized]);

  const createHACCPEvent = useCallback(async (event: Omit<StagedHACCPEvent, 'id' | 'created_at'>): Promise<string> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    const id = await sqliteService.createHACCPEvent(event);

    // Critical HACCP events require confirmation
    const isCritical = event.severity === 'critical' || event.severity === 'major';
    await sqliteService.addToConfirmationQueue({
      record_type: 'staged_haccp_events',
      record_id: id,
      data: { id, ...event },
      requires_confirmation: isCritical
    });

    return id;
  }, [isInitialized]);

  // Data retrieval
  const getAllProducts = useCallback(async (): Promise<StagedProduct[]> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }
    return await sqliteService.getAllProducts();
  }, [isInitialized]);

  const getAllInventory = useCallback(async (): Promise<StagedInventory[]> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }
    // This would need to be implemented in the service
    return [];
  }, [isInitialized]);

  const getStats = useCallback(async (): Promise<StagingStats> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }
    return await sqliteService.getStats();
  }, [isInitialized]);

  // Sync operations
  const startSync = useCallback(async (options: SyncOptions = {}): Promise<SyncResult> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }

    setIsSyncRunning(true);
    try {
      const result = await syncService.startSync(options);
      return result;
    } finally {
      setIsSyncRunning(false);
    }
  }, [isInitialized]);

  const stopSync = useCallback(async (): Promise<void> => {
    await syncService.stopSync();
    setIsSyncRunning(false);
  }, []);

  const getSyncStatus = useCallback(async () => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }
    return await syncService.getSyncStatus();
  }, [isInitialized]);

  // Confirmation workflow
  const getPendingConfirmations = useCallback(async (): Promise<ConfirmationQueueItem[]> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }
    return await syncService.getPendingConfirmations();
  }, [isInitialized]);

  const confirmAndSync = useCallback(async (queueItemId: number, confirmedBy: string): Promise<boolean> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }
    return await syncService.confirmAndSync(queueItemId, confirmedBy);
  }, [isInitialized]);

  // Sync history
  const getSyncHistory = useCallback(async (): Promise<SyncStatus[]> => {
    if (!isInitialized) {
      throw new Error('SQLite staging not initialized');
    }
    return await syncService.getSyncHistory();
  }, [isInitialized]);

  return {
    // Initialization
    isInitialized,
    initializeError,

    // Data operations
    createProduct,
    createInventory,
    createCCPMonitoring,
    createTemperatureReading,
    createQualityTest,
    createAllergenTest,
    createHACCPEvent,

    // Data retrieval
    getAllProducts,
    getAllInventory,
    getStats,

    // Sync operations
    startSync,
    stopSync,
    isSyncRunning,
    getSyncStatus,

    // Confirmation workflow
    getPendingConfirmations,
    confirmAndSync,

    // Sync history
    getSyncHistory,
  };
}