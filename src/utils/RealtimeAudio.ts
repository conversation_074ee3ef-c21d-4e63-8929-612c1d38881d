/**
 * Audio optimization configuration for dynamic bitrate and quality adjustment
 */
interface AudioOptimizationConfig {
  minBitrate: number; // 16000 bps
  maxBitrate: number; // 128000 bps
  targetBitrate: number; // 48000 bps
  sampleRate: number; // 24000 Hz
  bufferSize: number; // ScriptProcessor buffer size (power of two)
}

const VALID_PROCESSOR_SIZES = [256, 512, 1024, 2048, 4096, 8192, 16384] as const;
const MAX_LOW_LATENCY_BUFFER_MS = 85; // Anything larger noticeably delays VAD

/**
 * Network conditions for bitrate adjustment
 */
interface NetworkConditions {
  latency: number; // ms
  packetLoss: number; // 0-1
  bandwidth: number; // bps
}

export class AudioRecorder {
  private stream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private processor: ScriptProcessorNode | null = null;
  private source: MediaStreamAudioSourceNode | null = null;
  private optimizationConfig: AudioOptimizationConfig;
  private networkConditions: NetworkConditions = { latency: 0, packetLoss: 0, bandwidth: 1000000 };
  private audioBuffer: Float32Array[] = [];
  private lastEncodedTime = 0;

  constructor(
    private onAudioData: (audioData: Float32Array) => void,
    config?: Partial<AudioOptimizationConfig>
  ) {
    this.optimizationConfig = {
      minBitrate: 16000,
      maxBitrate: 128000,
      targetBitrate: 48000,
      sampleRate: 24000,
      bufferSize: 512,
      ...config
    };

    this.optimizationConfig.bufferSize = this.normalizeBufferSize(this.optimizationConfig.bufferSize);
  }

  async start() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.optimizationConfig.sampleRate,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // Log audio constraints that were applied
      const audioTracks = this.stream.getAudioTracks();
      if (audioTracks.length > 0) {
        const settings = audioTracks[0].getSettings();
        console.log('WebRTC Audio Constraints Applied:', {
          echoCancellation: settings.echoCancellation,
          noiseSuppression: settings.noiseSuppression,
          autoGainControl: settings.autoGainControl,
          sampleRate: settings.sampleRate,
          channelCount: settings.channelCount
        });
      }

      this.audioContext = new AudioContext({
        sampleRate: this.optimizationConfig.sampleRate,
      });

      this.source = this.audioContext.createMediaStreamSource(this.stream);
      this.processor = this.audioContext.createScriptProcessor(
        this.optimizationConfig.bufferSize,
        1,
        1
      );

      this.processor.onaudioprocess = (e) => {
        const inputData = e.inputBuffer.getChannelData(0);
        const audioData = new Float32Array(inputData);

        // Apply audio optimization (VAD, buffering)
        this.processAudioWithOptimization(audioData);
      };

      this.source.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      // Start network monitoring
      this.startNetworkMonitoring();

      console.log('AudioRecorder started successfully with optimization config:', this.optimizationConfig);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      throw error;
    }
  }

  /**
   * Ensure provided ScriptProcessor buffer matches browser-supported sizes
   * and stays within our low-latency envelope.
   */
  private normalizeBufferSize(size: number): number {
    const requested = VALID_PROCESSOR_SIZES.reduce((prev, curr) => {
      const prevDiff = Math.abs(prev - size);
      const currDiff = Math.abs(curr - size);
      return currDiff < prevDiff ? curr : prev;
    });

    // Cap to roughly ~85ms @ 24kHz to avoid instant overflow warnings
    const maxSamples = Math.floor((MAX_LOW_LATENCY_BUFFER_MS / 1000) * this.optimizationConfig.sampleRate);
    const underCap = VALID_PROCESSOR_SIZES.filter((value) => value <= maxSamples);

    if (underCap.length === 0) {
      return requested;
    }

    // Use the closest allowed size that remains under the latency cap
    return underCap.reduce((prev, curr) => {
      const prevDiff = Math.abs(prev - requested);
      const currDiff = Math.abs(curr - requested);
      return currDiff < prevDiff ? curr : prev;
    });
  }

  /**
   * Process audio with optimization (buffering, VAD-aware handling)
   */
  private processAudioWithOptimization(audioData: Float32Array) {
    // Add to jitter buffer
    this.audioBuffer.push(audioData);

    // Check if enough buffer accumulated (simple jitter buffer)
    const chunkDurationMs = (this.optimizationConfig.bufferSize / this.optimizationConfig.sampleRate) * 1000;
    const bufferTimeMs = (this.audioBuffer.length * this.optimizationConfig.bufferSize) / this.optimizationConfig.sampleRate * 1000;
    
    // AGGRESSIVE Dynamic target buffer based on network conditions
    let targetBufferMs = Math.max(25, chunkDurationMs * 1.5); // Tie to actual chunk duration
    if (this.networkConditions.latency > 300) {
      targetBufferMs = 35; // Minimal increase even for very high latency
    } else if (this.networkConditions.latency > 200) {
      targetBufferMs = 30; // Small increase for high latency
    } else if (this.networkConditions.latency > 100) {
      targetBufferMs = 28; // Minimal increase for medium latency
    }

    // AGGRESSIVE Buffer overflow prevention
    const maxBufferMs = Math.max(80, chunkDurationMs * 3); // Keep within low-latency window without dropping every chunk
    if (bufferTimeMs > maxBufferMs) {
      console.warn(`Buffer overflow detected: ${bufferTimeMs.toFixed(2)}ms, clearing excess`);
      // Keep only the most recent audio chunks - be more aggressive
      const maxBuffers = Math.max(2, Math.floor((maxBufferMs * this.optimizationConfig.sampleRate) / (this.optimizationConfig.bufferSize * 1000)));
      this.audioBuffer = this.audioBuffer.slice(-maxBuffers);
      return; // Skip processing this cycle to prevent accumulation
    }

    if (bufferTimeMs >= targetBufferMs) {
      // Combine buffered audio
      const combinedAudio = this.combineAudioBuffers();

      // Check timing for throttling based on network conditions
      const now = Date.now();
      const minIntervalMs = this.calculateOptimalSendInterval();

      if (now - this.lastEncodedTime >= minIntervalMs) {
        // Log audio optimization metrics periodically (every 10 sends)
        if (Math.random() < 0.1) {
          console.debug('Audio Optimization - Jitter Buffer:', {
            bufferTimeMs: bufferTimeMs.toFixed(2),
            targetBufferMs,
            sendIntervalMs: minIntervalMs,
            audioDataSize: combinedAudio.length,
            networkLatency: this.networkConditions.latency,
            networkPacketLoss: this.networkConditions.packetLoss
          });
        }

        this.onAudioData(combinedAudio);
        this.lastEncodedTime = now;
        // Clear buffer completely to prevent accumulation
        this.audioBuffer = [];
      }
    }
  }

  /**
   * Combine buffered audio into single array
   */
  private combineAudioBuffers(): Float32Array {
    const totalLength = this.audioBuffer.reduce((sum, buf) => sum + buf.length, 0);
    const combined = new Float32Array(totalLength);

    let offset = 0;
    for (const buffer of this.audioBuffer) {
      combined.set(buffer, offset);
      offset += buffer.length;
    }

    return combined;
  }

  /**
   * Calculate optimal send interval based on network conditions
   */
  private calculateOptimalSendInterval(): number {
    const { latency, packetLoss, bandwidth } = this.networkConditions;

    // Much more aggressive base interval for better responsiveness
    let interval = 25; // Reduced from 40ms to 25ms

    // EXTREMELY aggressive adjustments for poor network conditions
    if (packetLoss > 0.1) {
      interval = Math.min(interval * 1.3, 45); // Much less increase for high packet loss
    } else if (packetLoss > 0.05) {
      interval = Math.min(interval * 1.2, 35); // Minimal increase for moderate packet loss
    }

    // Much more aggressive latency adjustments
    if (latency > 300) {
      interval = Math.min(interval * 1.2, 40); // Minimal increase even for very high latency
    } else if (latency > 200) {
      interval = Math.min(interval * 1.1, 35); // Small increase for high latency
    } else if (latency > 100) {
      interval = Math.min(interval * 1.05, 30); // Very small increase for medium latency
    }

    // Much less aggressive bandwidth constraints
    if (bandwidth < 500000) { // < 500kbps
      interval = Math.min(interval * 1.1, 40); // Minimal increase for low bandwidth
    } else if (bandwidth < 1000000) { // < 1Mbps
      interval = Math.min(interval * 1.05, 35); // Very small increase for medium bandwidth
    }

    // Ensure maximum responsiveness - much lower caps
    const maxInterval = Math.max(20, Math.min(interval, 50)); // 20-50ms range instead of 60-120ms
    
    return maxInterval;
  }

  /**
   * Update network conditions for adaptive bitrate
   */
  updateNetworkConditions(conditions: Partial<NetworkConditions>) {
    this.networkConditions = { ...this.networkConditions, ...conditions };

    // Log network-aware bitrate adjustment
    const adjustedBitrate = this.calculateAdaptiveBitrate();
    console.debug(`Network conditions updated - Adaptive bitrate: ${adjustedBitrate} bps`, {
      latency: this.networkConditions.latency,
      packetLoss: this.networkConditions.packetLoss,
      bandwidth: this.networkConditions.bandwidth
    });
  }

  /**
   * Calculate adaptive bitrate based on network conditions
   */
  private calculateAdaptiveBitrate(): number {
    const { minBitrate, maxBitrate, targetBitrate } = this.optimizationConfig;
    const { packetLoss, bandwidth } = this.networkConditions;

    let bitrate = targetBitrate;

    // Reduce bitrate if packet loss is high
    if (packetLoss > 0.05) {
      bitrate = Math.max(bitrate * (1 - packetLoss * 2), minBitrate);
    }

    // Reduce bitrate if bandwidth is constrained
    if (bandwidth < targetBitrate * 2) {
      bitrate = Math.min(bitrate, bandwidth / 2);
    }

    return Math.max(minBitrate, Math.min(bitrate, maxBitrate));
  }

  /**
   * Start monitoring network conditions
   */
  private startNetworkMonitoring() {
    // Monitor via Network Information API if available
    const monitor = () => {
      if (typeof window !== 'undefined') {
        const nav = window.navigator as unknown as Record<string, unknown>;
        if (nav && 'connection' in nav) {
          const connection = nav.connection as unknown as Record<string, unknown>;
          const effectiveType = connection?.effectiveType as string | undefined;

          if (effectiveType === '4g') {
            this.updateNetworkConditions({ bandwidth: 4000000 });
          } else if (effectiveType === '3g') {
            this.updateNetworkConditions({ bandwidth: 1000000 });
          } else if (effectiveType === '2g') {
            this.updateNetworkConditions({ bandwidth: 400000 });
          }
        }
      }
    };

    setInterval(monitor, 5000); // Check every 5 seconds
  }

  stop() {
    if (this.source) {
      this.source.disconnect();
      this.source = null;
    }
    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    this.audioBuffer = [];
  }
}

export const encodeAudioForAPI = (float32Array: Float32Array): string => {
  const int16Array = new Int16Array(float32Array.length);
  for (let i = 0; i < float32Array.length; i++) {
    const s = Math.max(-1, Math.min(1, float32Array[i]));
    int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
  }
  
  const uint8Array = new Uint8Array(int16Array.buffer);
  let binary = '';
  const chunkSize = 0x8000;
  
  for (let i = 0; i < uint8Array.length; i += chunkSize) {
    const chunk = uint8Array.subarray(i, Math.min(i + chunkSize, uint8Array.length));
    binary += String.fromCharCode.apply(null, Array.from(chunk));
  }
  
  return btoa(binary);
};

const createWavFromPCM = (pcmData: Uint8Array): Uint8Array => {
  // Convert bytes to 16-bit samples
  const int16Data = new Int16Array(pcmData.length / 2);
  for (let i = 0; i < pcmData.length; i += 2) {
    int16Data[i / 2] = (pcmData[i + 1] << 8) | pcmData[i];
  }
  
  // Create WAV header
  const wavHeader = new ArrayBuffer(44);
  const view = new DataView(wavHeader);
  
  const writeString = (view: DataView, offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  // WAV header parameters
  const sampleRate = 24000;
  const numChannels = 1;
  const bitsPerSample = 16;
  const blockAlign = (numChannels * bitsPerSample) / 8;
  const byteRate = sampleRate * blockAlign;

  // Write WAV header
  writeString(view, 0, 'RIFF');
  view.setUint32(4, 36 + int16Data.byteLength, true);
  writeString(view, 8, 'WAVE');
  writeString(view, 12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, numChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, bitsPerSample, true);
  writeString(view, 36, 'data');
  view.setUint32(40, int16Data.byteLength, true);

  // Combine header and data
  const wavArray = new Uint8Array(wavHeader.byteLength + int16Data.byteLength);
  wavArray.set(new Uint8Array(wavHeader), 0);
  wavArray.set(new Uint8Array(int16Data.buffer), wavHeader.byteLength);
  
  return wavArray;
};

class AudioQueue {
  private queue: Uint8Array[] = [];
  private isPlaying = false;
  private audioContext: AudioContext;

  constructor(audioContext: AudioContext) {
    this.audioContext = audioContext;
  }

  async addToQueue(audioData: Uint8Array) {
    this.queue.push(audioData);
    if (!this.isPlaying) {
      await this.playNext();
    }
  }

  private async playNext() {
    if (this.queue.length === 0) {
      this.isPlaying = false;
      return;
    }

    this.isPlaying = true;
    const audioData = this.queue.shift()!;

    try {
      const wavData = createWavFromPCM(audioData);
      const audioBuffer = await this.audioContext.decodeAudioData(wavData.buffer as ArrayBuffer);
      
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.audioContext.destination);
      
      // Add timeout to prevent hanging
      const timeoutId = setTimeout(() => {
        if (this.isPlaying) {
          console.warn('Audio playback timeout, forcing next');
          source.stop();
          this.playNext();
        }
      }, 3000); // 3 second timeout
      
      source.onended = () => {
        clearTimeout(timeoutId);
        this.playNext();
      };
      
      source.start(0);
    } catch (error) {
      console.error('Error playing audio:', error);
      // Clear queue if there are too many failed attempts
      if (this.queue.length > 5) {
        console.warn('Clearing audio queue due to repeated failures');
        this.queue = [];
      }
      this.playNext(); // Continue with next segment even if current fails
    }
  }
}

let audioQueueInstance: AudioQueue | null = null;

export const playAudioData = async (audioContext: AudioContext, audioData: Uint8Array) => {
  if (!audioQueueInstance) {
    audioQueueInstance = new AudioQueue(audioContext);
  }
  await audioQueueInstance.addToQueue(audioData);
};
