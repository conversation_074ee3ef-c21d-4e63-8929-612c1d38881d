import { describe, it, expect, beforeEach, vi } from 'vitest';
import { sqliteService } from '@/lib/sqlite-service';
import { VoiceToolExecutor } from '@/lib/voice-tool-executor';

describe('Voice Data Visibility', () => {
  beforeEach(async () => {
    await sqliteService.initialize();
  });

  describe('getVoiceInventoryRecords', () => {
    it('should filter records by created_by = voice-agent', async () => {
      // Create voice-agent record
      await sqliteService.createInventory({
        product_id: 'test-1',
        batch_number: 'B001',
        quantity: 100,
        received_date: '2024-01-01',
        created_by: 'voice-agent'
      });

      // Create non-voice record
      await sqliteService.createInventory({
        product_id: 'test-2',
        batch_number: 'B002',
        quantity: 50,
        received_date: '2024-01-01',
        created_by: 'manual'
      });

      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records).toHaveLength(1);
      expect(records[0].created_by).toBe('voice-agent');
      expect(records[0].product_id).toBe('test-1');
    });

    it('should sort by created_at DESC', async () => {
      // Create multiple records with slight delays to ensure different timestamps
      const id1 = await sqliteService.createInventory({
        product_id: 'test-1',
        batch_number: 'B001',
        quantity: 100,
        received_date: '2024-01-01',
        created_by: 'voice-agent'
      });

      // Wait to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));

      const id2 = await sqliteService.createInventory({
        product_id: 'test-2',
        batch_number: 'B002',
        quantity: 50,
        received_date: '2024-01-01',
        created_by: 'voice-agent'
      });

      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records.length).toBeGreaterThanOrEqual(2);
      // Most recent first
      expect(records[0].id).toBe(id2);
      expect(records[1].id).toBe(id1);
    });

    it('should respect limit parameter', async () => {
      // Create 5 records
      for (let i = 0; i < 5; i++) {
        await sqliteService.createInventory({
          product_id: `test-${i}`,
          batch_number: `B00${i}`,
          quantity: 100,
          received_date: '2024-01-01',
          created_by: 'voice-agent'
        });
      }

      const records = await sqliteService.getVoiceInventoryRecords(3);
      expect(records).toHaveLength(3);
    });

    it('should return empty array when no voice records exist', async () => {
      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records).toEqual([]);
    });

    it('should return records with all expected fields', async () => {
      await sqliteService.createInventory({
        product_id: 'test-product',
        batch_number: 'BATCH-001',
        quantity: 150,
        unit: 'kg',
        location: 'Warehouse A',
        received_date: '2024-01-15',
        created_by: 'voice-agent'
      });

      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records).toHaveLength(1);
      
      const record = records[0];
      expect(record).toHaveProperty('id');
      expect(record).toHaveProperty('product_id', 'test-product');
      expect(record).toHaveProperty('batch_number', 'BATCH-001');
      expect(record).toHaveProperty('quantity', 150);
      expect(record).toHaveProperty('unit', 'kg');
      expect(record).toHaveProperty('location', 'Warehouse A');
      expect(record).toHaveProperty('created_by', 'voice-agent');
      expect(record).toHaveProperty('created_at');
    });
  });

  describe('getVoiceCCPRecords', () => {
    it('should filter CCP records by created_by = voice-agent', async () => {
      // Create voice-agent CCP record
      await sqliteService.createCCPMonitoring({
        ccp_name: 'Temperature Check',
        measurement_value: 4.5,
        measurement_unit: '°C',
        monitoring_datetime: new Date().toISOString(),
        monitored_by: 'voice-agent',
        is_within_limits: true,
        created_by: 'voice-agent'
      });

      // Create non-voice CCP record
      await sqliteService.createCCPMonitoring({
        ccp_name: 'pH Check',
        measurement_value: 7.0,
        measurement_unit: 'pH',
        monitoring_datetime: new Date().toISOString(),
        monitored_by: 'manual-operator',
        is_within_limits: true,
        created_by: 'manual'
      });

      const records = await sqliteService.getVoiceCCPRecords();
      expect(records).toHaveLength(1);
      expect(records[0].created_by).toBe('voice-agent');
      expect(records[0].ccp_name).toBe('Temperature Check');
    });

    it('should respect limit parameter for CCP records', async () => {
      // Create 4 CCP records
      for (let i = 0; i < 4; i++) {
        await sqliteService.createCCPMonitoring({
          ccp_name: `CCP-${i}`,
          measurement_value: 5 + i,
          measurement_unit: '°C',
          monitoring_datetime: new Date().toISOString(),
          monitored_by: 'voice-agent',
          is_within_limits: true,
          created_by: 'voice-agent'
        });
      }

      const records = await sqliteService.getVoiceCCPRecords(2);
      expect(records).toHaveLength(2);
    });
  });

  describe('Voice Tool Executor Logging', () => {
    it('should log inventory creation with console.log', async () => {
      const consoleSpy = vi.spyOn(console, 'log');

      const result = await VoiceToolExecutor.executeTool({
        name: 'add_inventory_event',
        arguments: {
          product_id: 'test-product',
          batch_number: 'B001',
          quantity: 100,
          unit: 'kg',
          location: 'Warehouse A'
        }
      });

      expect(result.success).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('✅ Voice Agent: Inventory Record Created')
      );
      
      consoleSpy.mockRestore();
    });

    it('should log inventory creation with console.table', async () => {
      const tableSpy = vi.spyOn(console, 'table');

      await VoiceToolExecutor.executeTool({
        name: 'add_inventory_event',
        arguments: {
          product_id: 'test-product',
          batch_number: 'B001',
          quantity: 100,
          unit: 'kg'
        }
      });

      expect(tableSpy).toHaveBeenCalled();
      const tableCall = tableSpy.mock.calls[0][0];
      expect(tableCall).toHaveProperty('Product ID', 'test-product');
      expect(tableCall).toHaveProperty('Batch Number', 'B001');
      expect(tableCall).toHaveProperty('Quantity', '100 kg');
      expect(tableCall).toHaveProperty('Created By', 'voice-agent');
      
      tableSpy.mockRestore();
    });

    it('should log CCP monitoring creation', async () => {
      const consoleSpy = vi.spyOn(console, 'log');
      const tableSpy = vi.spyOn(console, 'table');

      const result = await VoiceToolExecutor.executeTool({
        name: 'record_ccp_monitoring',
        arguments: {
          ccp_name: 'Temperature',
          measurement_value: 4.5,
          measurement_unit: '°C',
          monitored_by: 'voice-system'
        }
      });

      expect(result.success).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('✅ Voice Agent: CCP Monitoring Record Created')
      );
      expect(tableSpy).toHaveBeenCalled();
      
      const tableCall = tableSpy.mock.calls[0][0];
      expect(tableCall).toHaveProperty('CCP Name', 'Temperature');
      expect(tableCall).toHaveProperty('Measurement', '4.5 °C');
      expect(tableCall).toHaveProperty('Created By', 'voice-agent');
      
      consoleSpy.mockRestore();
      tableSpy.mockRestore();
    });
  });

  describe('Integration Tests', () => {
    it('should create and retrieve voice inventory records end-to-end', async () => {
      // Create via tool executor
      const result = await VoiceToolExecutor.executeTool({
        name: 'add_inventory_event',
        arguments: {
          product_id: 'salmon-001',
          batch_number: 'BATCH-2024-001',
          quantity: 250,
          unit: 'kg',
          location: 'Freezer-A'
        }
      });

      expect(result.success).toBe(true);

      // Retrieve via new method
      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records.length).toBeGreaterThan(0);
      
      const latestRecord = records[0];
      expect(latestRecord.product_id).toBe('salmon-001');
      expect(latestRecord.batch_number).toBe('BATCH-2024-001');
      expect(latestRecord.quantity).toBe(250);
      expect(latestRecord.created_by).toBe('voice-agent');
    });

    it('should handle multiple voice records correctly', async () => {
      // Create multiple records through different methods
      await VoiceToolExecutor.executeTool({
        name: 'add_inventory_event',
        arguments: {
          product_id: 'product-1',
          batch_number: 'B001',
          quantity: 100,
          unit: 'kg'
        }
      });

      await sqliteService.createInventory({
        product_id: 'product-2',
        batch_number: 'B002',
        quantity: 200,
        received_date: '2024-01-01',
        created_by: 'voice-agent'
      });

      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records.length).toBeGreaterThanOrEqual(2);
      
      // All should have voice-agent as creator
      records.forEach(record => {
        expect(record.created_by).toBe('voice-agent');
      });
    });
  });
});