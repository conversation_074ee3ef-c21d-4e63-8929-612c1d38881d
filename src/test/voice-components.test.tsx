import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import VoiceToolFeedback from '@/components/VoiceToolFeedback';
import { ToolCall, ToolResult } from '@/lib/voice-tool-executor';

/**
 * VOICE COMPONENT INTEGRATION TESTS
 *
 * Tests for:
 * 1. VoiceToolFeedback component rendering
 * 2. Tool feedback UI states (executing, success, error)
 * 3. User interactions with feedback UI
 * 4. Accessibility and responsiveness
 */

describe('VoiceToolFeedback Component', () => {
  const mockToolCall: ToolCall = {
    name: 'add_inventory_event',
    arguments: {
      product_id: 'salmon-001',
      quantity: 10,
      event_type: 'receipt',
      location: 'Freezer A'
    },
    call_id: 'test-call-001'
  };

  const mockToolResult: ToolResult = {
    success: true,
    message: 'Inventory added successfully',
    data: {
      inventory_id: 'inv-001'
    }
  };

  describe('Rendering States', () => {
    it('should render executing state with loading spinner', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          isExecuting={true}
        />
      );

      expect(screen.getByText(/add inventory event/i)).toBeInTheDocument();
      expect(screen.getByText(/processing/i)).toBeInTheDocument();
    });

    it('should render success state with checkmark', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={mockToolResult}
          isExecuting={false}
        />
      );

      expect(screen.getByText(/complete/i)).toBeInTheDocument();
      expect(screen.getByText(/inventory added successfully/i)).toBeInTheDocument();
    });

    it('should render error state with error icon', () => {
      const errorResult: ToolResult = {
        success: false,
        message: 'Failed to add inventory',
        error: 'Database connection failed'
      };

      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={errorResult}
          error="Database connection failed"
          isExecuting={false}
        />
      );

      expect(screen.getByText(/database connection failed/i)).toBeInTheDocument();
    });

    it('should display tool name with underscores replaced', () => {
      render(
        <VoiceToolFeedback
          toolCall={{
            ...mockToolCall,
            name: 'check_product_status'
          }}
          isExecuting={true}
        />
      );

      expect(screen.getByText(/check product status/i)).toBeInTheDocument();
    });
  });

  describe('Tool Arguments Display', () => {
    it('should display key arguments in a readable format', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          isExecuting={true}
        />
      );

      // Component should render without crashing
      expect(screen.getByText(/add inventory event/i)).toBeInTheDocument();
    });

    it('should truncate long argument values', () => {
      const longArguments: Record<string, unknown> = {
        product_id: 'test' + 'a'.repeat(100),
        notes: 'This is a very long description ' + 'x'.repeat(100)
      };

      render(
        <VoiceToolFeedback
          toolCall={{
            name: 'add_inventory_event',
            arguments: longArguments
          }}
          isExecuting={true}
        />
      );

      // Should not crash and should render
      expect(screen.getByText(/add inventory event/i)).toBeInTheDocument();
    });

    it('should handle various data types in arguments', () => {
      render(
        <VoiceToolFeedback
          toolCall={{
            name: 'test_tool',
            arguments: {
              string_val: 'test',
              number_val: 42,
              boolean_val: true,
              null_val: null,
              object_val: { nested: 'value' }
            }
          }}
          isExecuting={true}
        />
      );

      expect(screen.getByText(/test tool/i)).toBeInTheDocument();
    });
  });

  describe('Result Data Display', () => {
    it('should display inventory event result with ID', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={mockToolResult}
          isExecuting={false}
        />
      );

      expect(screen.getByText(/inventory added successfully/i)).toBeInTheDocument();
    });

    it('should display location update result', () => {
      const locationResult: ToolResult = {
        success: true,
        message: 'Location updated',
        data: {
          product_id: 'salmon-001',
          new_location: 'Freezer B'
        }
      };

      const locationCall: ToolCall = {
        name: 'update_product_location',
        arguments: {
          product_id: 'salmon-001',
          new_location: 'Freezer B'
        }
      };

      render(
        <VoiceToolFeedback
          toolCall={locationCall}
          result={locationResult}
          isExecuting={false}
        />
      );

      // Check for the specific message
      expect(screen.getAllByText(/location updated/i)[0]).toBeInTheDocument();
    });

    it('should display product status result', () => {
      const statusResult: ToolResult = {
        success: true,
        message: 'Status retrieved',
        data: {
          quantity: 25,
          location: 'Cold Storage Room 1',
          last_updated: '2025-10-24T10:30:00Z'
        }
      };

      const statusCall: ToolCall = {
        name: 'check_product_status',
        arguments: {
          product_id: 'salmon-001'
        }
      };

      render(
        <VoiceToolFeedback
          toolCall={statusCall}
          result={statusResult}
          isExecuting={false}
        />
      );

      // Check for the specific message
      expect(screen.getAllByText(/status retrieved/i)[0]).toBeInTheDocument();
    });

    it('should display HACCP events count', () => {
      const hacppResult: ToolResult = {
        success: true,
        message: 'HACCP Events retrieved',
        data: [
          { id: 1, type: 'temperature_check' },
          { id: 2, type: 'humidity_check' },
          { id: 3, type: 'contamination_test' }
        ]
      };

      const hacppCall: ToolCall = {
        name: 'get_haccp_events',
        arguments: {
          limit: 10
        }
      };

      render(
        <VoiceToolFeedback
          toolCall={hacppCall}
          result={hacppResult}
          isExecuting={false}
        />
      );

      // Verify the component renders with events data
      expect(screen.getByText(/get haccp events/i)).toBeInTheDocument();
    });

    it('should display CCP monitoring result', () => {
      const ccpResult: ToolResult = {
        success: true,
        message: 'CCP Monitoring recorded',
        data: {
          ccp_id: 'ccp-001',
          timestamp: '2025-10-24T10:30:00Z'
        }
      };

      const ccpCall: ToolCall = {
        name: 'record_ccp_monitoring',
        arguments: {
          ccp_type: 'temperature',
          value: 4.5,
          unit: 'celsius'
        }
      };

      render(
        <VoiceToolFeedback
          toolCall={ccpCall}
          result={ccpResult}
          isExecuting={false}
        />
      );

      // Component renders successfully
      const container = screen.getByText(/record ccp monitoring/i).closest('div');
      expect(container).toBeInTheDocument();
    });
  });

  describe('Error Display', () => {
    it('should display error message clearly', () => {
      const errorResult: ToolResult = {
        success: false,
        message: 'Database error occurred',
        error: 'Connection timeout after 30 seconds'
      };

      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={errorResult}
          error="Connection timeout after 30 seconds"
          isExecuting={false}
        />
      );

      expect(screen.getByText(/connection timeout/i)).toBeInTheDocument();
    });

    it('should show error icon in failure state', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={{ success: false, message: 'Error', error: 'Test error' }}
          error="Test error"
          isExecuting={false}
        />
      );

      expect(screen.getByText(/failed/i)).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should call onDismiss when dismiss button is clicked', async () => {
      const onDismiss = vi.fn();
      const user = userEvent.setup();

      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={mockToolResult}
          isExecuting={false}
          onDismiss={onDismiss}
        />
      );

      const dismissButton = screen.getByLabelText(/dismiss feedback/i);
      await user.click(dismissButton);

      expect(onDismiss).toHaveBeenCalledTimes(1);
    });

    it('should not show dismiss button while executing', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          isExecuting={true}
          onDismiss={vi.fn()}
        />
      );

      expect(screen.queryByLabelText(/dismiss feedback/i)).not.toBeInTheDocument();
    });

    it('should show dismiss button after execution completes', async () => {
      const { rerender } = render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          isExecuting={true}
          onDismiss={vi.fn()}
        />
      );

      expect(screen.queryByLabelText(/dismiss feedback/i)).not.toBeInTheDocument();

      rerender(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={mockToolResult}
          isExecuting={false}
          onDismiss={vi.fn()}
        />
      );

      expect(screen.getByLabelText(/dismiss feedback/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels on dismissible feedback', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={mockToolResult}
          isExecuting={false}
          onDismiss={vi.fn()}
        />
      );

      expect(screen.getByLabelText(/dismiss feedback/i)).toHaveAttribute('aria-label');
    });

    it('should render with semantic HTML structure', () => {
      const { container } = render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={mockToolResult}
          isExecuting={false}
        />
      );

      // Should have proper div structure with border and padding (from className)
      const feedbackDiv = container.querySelector('[class*="border"]');
      expect(feedbackDiv).toBeInTheDocument();
    });
  });

  describe('Visual States', () => {
    it('should have different background colors for different states', () => {
      const { container: executingContainer } = render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          isExecuting={true}
        />
      );
      const executingDiv = executingContainer.querySelector('[class*="bg-"]');

      const { container: successContainer } = render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={mockToolResult}
          isExecuting={false}
        />
      );
      const successDiv = successContainer.querySelector('[class*="bg-"]');

      const { container: errorContainer } = render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={{ success: false, message: 'Error', error: 'Test' }}
          error="Test"
          isExecuting={false}
        />
      );
      const errorDiv = errorContainer.querySelector('[class*="bg-"]');

      // All should be present and potentially different
      expect(executingDiv).toBeInTheDocument();
      expect(successDiv).toBeInTheDocument();
      expect(errorDiv).toBeInTheDocument();
    });

    it('should render loading spinner in executing state', () => {
      const { container } = render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          isExecuting={true}
        />
      );

      // Check for animated spinner (Loader2 icon with animate-spin class)
      const spinner = container.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined result gracefully', () => {
      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          isExecuting={false}
        />
      );

      expect(screen.getByText(/add inventory event/i)).toBeInTheDocument();
    });

    it('should handle empty arguments object', () => {
      render(
        <VoiceToolFeedback
          toolCall={{
            name: 'empty_tool',
            arguments: {}
          }}
          isExecuting={true}
        />
      );

      expect(screen.getByText(/empty tool/i)).toBeInTheDocument();
    });

    it('should handle very long tool names', () => {
      const longName = 'a'.repeat(100) + '_tool_with_very_long_name';
      render(
        <VoiceToolFeedback
          toolCall={{
            name: longName,
            arguments: {}
          }}
          isExecuting={true}
        />
      );

      expect(screen.getByText(/a/)).toBeInTheDocument();
    });

    it('should handle special characters in messages', () => {
      const specialCharsResult: ToolResult = {
        success: true,
        message: 'Success <>&"\'special!@#$%^',
        data: null
      };

      render(
        <VoiceToolFeedback
          toolCall={mockToolCall}
          result={specialCharsResult}
          isExecuting={false}
        />
      );

      expect(screen.getByText(/special/i)).toBeInTheDocument();
    });
  });
});
