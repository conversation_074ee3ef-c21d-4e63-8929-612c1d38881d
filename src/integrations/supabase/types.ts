export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.12 (cd3cf9e)"
  }
  public: {
    Tables: {
      allergen_testing: {
        Row: {
          allergen_type: string
          certificate_number: string | null
          compliant: boolean
          created_at: string | null
          detection_limit: number | null
          id: string
          laboratory: string
          lot_id: string
          quantitative_result: number | null
          result: string
          test_date: string
          test_method: string
        }
        Insert: {
          allergen_type: string
          certificate_number?: string | null
          compliant: boolean
          created_at?: string | null
          detection_limit?: number | null
          id?: string
          laboratory: string
          lot_id: string
          quantitative_result?: number | null
          result: string
          test_date: string
          test_method: string
        }
        Update: {
          allergen_type?: string
          certificate_number?: string | null
          compliant?: boolean
          created_at?: string | null
          detection_limit?: number | null
          id?: string
          laboratory?: string
          lot_id?: string
          quantitative_result?: number | null
          result?: string
          test_date?: string
          test_method?: string
        }
        Relationships: [
          {
            foreignKeyName: "allergen_testing_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "allergen_testing_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "allergen_testing_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "lots"
            referencedColumns: ["id"]
          },
        ]
      }
      batch_tracking: {
        Row: {
          batch_number: string
          created_at: string
          expiry_date: string | null
          id: string
          notes: string | null
          product_id: string | null
          quality_check_status: string | null
          quantity: number
          received_date: string
          unit_cost: number
          updated_at: string
          vendor_id: string | null
        }
        Insert: {
          batch_number: string
          created_at?: string
          expiry_date?: string | null
          id?: string
          notes?: string | null
          product_id?: string | null
          quality_check_status?: string | null
          quantity: number
          received_date: string
          unit_cost: number
          updated_at?: string
          vendor_id?: string | null
        }
        Update: {
          batch_number?: string
          created_at?: string
          expiry_date?: string | null
          id?: string
          notes?: string | null
          product_id?: string | null
          quality_check_status?: string | null
          quantity?: number
          received_date?: string
          unit_cost?: number
          updated_at?: string
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "batch_tracking_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "batch_tracking_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "batch_tracking_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      batches: {
        Row: {
          batch_number: string
          created_at: string
          created_by: string | null
          expiry_date: string | null
          id: string
          images: string[] | null
          notes: string | null
          product_id: string
          quality: string | null
          quantity: number
          received_date: string
          source: string | null
          suplier_id: string | null
          temperature: number | null
          updated_at: string
          vendor: string | null
        }
        Insert: {
          batch_number: string
          created_at?: string
          created_by?: string | null
          expiry_date?: string | null
          id?: string
          images?: string[] | null
          notes?: string | null
          product_id: string
          quality?: string | null
          quantity: number
          received_date: string
          source?: string | null
          suplier_id?: string | null
          temperature?: number | null
          updated_at?: string
          vendor?: string | null
        }
        Update: {
          batch_number?: string
          created_at?: string
          created_by?: string | null
          expiry_date?: string | null
          id?: string
          images?: string[] | null
          notes?: string | null
          product_id?: string
          quality?: string | null
          quantity?: number
          received_date?: string
          source?: string | null
          suplier_id?: string | null
          temperature?: number | null
          updated_at?: string
          vendor?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "batches_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: true
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "batches_supplier_id_fkey"
            columns: ["suplier_id"]
            isOneToOne: true
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      calendar_events: {
        Row: {
          all_day: boolean
          created_at: string
          description: string | null
          end_at: string | null
          event_type: string | null
          id: string
          inventory_event_id: string | null
          metadata: Json
          product_id: string | null
          source: string
          source_id: string | null
          start_at: string
          title: string
          updated_at: string
        }
        Insert: {
          all_day?: boolean
          created_at?: string
          description?: string | null
          end_at?: string | null
          event_type?: string | null
          id?: string
          inventory_event_id?: string | null
          metadata?: Json
          product_id?: string | null
          source?: string
          source_id?: string | null
          start_at: string
          title: string
          updated_at?: string
        }
        Update: {
          all_day?: boolean
          created_at?: string
          description?: string | null
          end_at?: string | null
          event_type?: string | null
          id?: string
          inventory_event_id?: string | null
          metadata?: Json
          product_id?: string | null
          source?: string
          source_id?: string | null
          start_at?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "calendar_events_inventory_event_id_fkey"
            columns: ["inventory_event_id"]
            isOneToOne: false
            referencedRelation: "inventory_events"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          parent_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      ccp_monitoring_logs: {
        Row: {
          batch_number: string | null
          ccp_id: string
          corrective_action_needed: boolean | null
          created_at: string | null
          critical_limit_max: number | null
          critical_limit_min: number | null
          equipment_used: string | null
          id: string
          is_within_limits: boolean | null
          measurement_unit: string
          measurement_value: number
          monitored_by: string
          monitoring_datetime: string
          observations: string | null
          product_id: string | null
        }
        Insert: {
          batch_number?: string | null
          ccp_id: string
          corrective_action_needed?: boolean | null
          created_at?: string | null
          critical_limit_max?: number | null
          critical_limit_min?: number | null
          equipment_used?: string | null
          id?: string
          is_within_limits?: boolean | null
          measurement_unit: string
          measurement_value: number
          monitored_by: string
          monitoring_datetime?: string
          observations?: string | null
          product_id?: string | null
        }
        Update: {
          batch_number?: string | null
          ccp_id?: string
          corrective_action_needed?: boolean | null
          created_at?: string | null
          critical_limit_max?: number | null
          critical_limit_min?: number | null
          equipment_used?: string | null
          id?: string
          is_within_limits?: boolean | null
          measurement_unit?: string
          measurement_value?: number
          monitored_by?: string
          monitoring_datetime?: string
          observations?: string | null
          product_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ccp_monitoring_logs_ccp_id_fkey"
            columns: ["ccp_id"]
            isOneToOne: false
            referencedRelation: "critical_control_points"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ccp_monitoring_logs_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      certifications: {
        Row: {
          certificate_number: string
          certificate_url: string | null
          certification_body: string
          certification_type: string
          created_at: string | null
          expiry_date: string
          id: string
          is_valid: boolean | null
          issue_date: string
          partner_id: string | null
          product_id: string | null
          scope_description: string | null
          updated_at: string | null
          vessel_id: string | null
        }
        Insert: {
          certificate_number: string
          certificate_url?: string | null
          certification_body: string
          certification_type: string
          created_at?: string | null
          expiry_date: string
          id?: string
          is_valid?: boolean | null
          issue_date: string
          partner_id?: string | null
          product_id?: string | null
          scope_description?: string | null
          updated_at?: string | null
          vessel_id?: string | null
        }
        Update: {
          certificate_number?: string
          certificate_url?: string | null
          certification_body?: string
          certification_type?: string
          created_at?: string | null
          expiry_date?: string
          id?: string
          is_valid?: boolean | null
          issue_date?: string
          partner_id?: string | null
          product_id?: string | null
          scope_description?: string | null
          updated_at?: string | null
          vessel_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "certifications_partner_id_fkey"
            columns: ["partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "certifications_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "certifications_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "certifications_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "certifications_vessel_id_fkey"
            columns: ["vessel_id"]
            isOneToOne: false
            referencedRelation: "vessels"
            referencedColumns: ["id"]
          },
        ]
      }
      compliance_alerts: {
        Row: {
          acknowledged: boolean
          acknowledged_at: string | null
          acknowledged_by: string | null
          alert_type: string
          created_at: string | null
          description: string
          id: string
          metadata: Json | null
          notification_sent: boolean
          resolution_notes: string | null
          resolved: boolean
          resolved_at: string | null
          resolved_by: string | null
          severity: string
          source_id: string
          source_table: string
          title: string
          triggered_at: string
        }
        Insert: {
          acknowledged?: boolean
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          alert_type: string
          created_at?: string | null
          description: string
          id?: string
          metadata?: Json | null
          notification_sent?: boolean
          resolution_notes?: string | null
          resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          severity: string
          source_id: string
          source_table: string
          title: string
          triggered_at?: string
        }
        Update: {
          acknowledged?: boolean
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          alert_type?: string
          created_at?: string | null
          description?: string
          id?: string
          metadata?: Json | null
          notification_sent?: boolean
          resolution_notes?: string | null
          resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string
          source_id?: string
          source_table?: string
          title?: string
          triggered_at?: string
        }
        Relationships: []
      }
      corrective_actions: {
        Row: {
          action_description: string
          action_timestamp: string
          action_type: string
          ccp_id: string
          created_at: string | null
          effectiveness_verified: boolean | null
          id: string
          monitoring_log_id: string
          performed_by: string
          preventive_measures: string[] | null
          product_disposition: string
          root_cause_analysis: string | null
          verification_by: string | null
          verification_completed: boolean
          verification_required: boolean
          verification_timestamp: string | null
        }
        Insert: {
          action_description: string
          action_timestamp?: string
          action_type: string
          ccp_id: string
          created_at?: string | null
          effectiveness_verified?: boolean | null
          id?: string
          monitoring_log_id: string
          performed_by: string
          preventive_measures?: string[] | null
          product_disposition: string
          root_cause_analysis?: string | null
          verification_by?: string | null
          verification_completed?: boolean
          verification_required?: boolean
          verification_timestamp?: string | null
        }
        Update: {
          action_description?: string
          action_timestamp?: string
          action_type?: string
          ccp_id?: string
          created_at?: string | null
          effectiveness_verified?: boolean | null
          id?: string
          monitoring_log_id?: string
          performed_by?: string
          preventive_measures?: string[] | null
          product_disposition?: string
          root_cause_analysis?: string | null
          verification_by?: string | null
          verification_completed?: boolean
          verification_required?: boolean
          verification_timestamp?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "corrective_actions_ccp_id_fkey"
            columns: ["ccp_id"]
            isOneToOne: false
            referencedRelation: "critical_control_points"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "corrective_actions_monitoring_log_id_fkey"
            columns: ["monitoring_log_id"]
            isOneToOne: false
            referencedRelation: "ccp_monitoring_logs"
            referencedColumns: ["id"]
          },
        ]
      }
      critical_control_points: {
        Row: {
          ccp_name: string
          ccp_number: string
          created_at: string | null
          critical_limits: Json
          hazard_analysis_id: string | null
          hazard_controlled: string
          id: string
          is_active: boolean
          monitoring_equipment: string | null
          monitoring_frequency: string
          monitoring_method: string
          process_step: string
          product_id: string | null
          record_keeping_requirements: string
          responsible_person: string
          verification_frequency: string
        }
        Insert: {
          ccp_name: string
          ccp_number: string
          created_at?: string | null
          critical_limits: Json
          hazard_analysis_id?: string | null
          hazard_controlled: string
          id?: string
          is_active?: boolean
          monitoring_equipment?: string | null
          monitoring_frequency: string
          monitoring_method: string
          process_step: string
          product_id?: string | null
          record_keeping_requirements: string
          responsible_person: string
          verification_frequency: string
        }
        Update: {
          ccp_name?: string
          ccp_number?: string
          created_at?: string | null
          critical_limits?: Json
          hazard_analysis_id?: string | null
          hazard_controlled?: string
          id?: string
          is_active?: boolean
          monitoring_equipment?: string | null
          monitoring_frequency?: string
          monitoring_method?: string
          process_step?: string
          product_id?: string | null
          record_keeping_requirements?: string
          responsible_person?: string
          verification_frequency?: string
        }
        Relationships: [
          {
            foreignKeyName: "critical_control_points_hazard_analysis_id_fkey"
            columns: ["hazard_analysis_id"]
            isOneToOne: false
            referencedRelation: "hazard_analysis"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "critical_control_points_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          address: string
          channel_type: string
          contact_name: string
          created_at: string
          credit_limit: number | null
          customer_source: string
          email: string
          id: string
          last_order_date: string | null
          metadata: Json | null
          name: string
          payment_terms: string | null
          phone: string
          status: string
          tags: string[] | null
          total_orders: number | null
          total_revenue: number | null
          updated_at: string
        }
        Insert: {
          address: string
          channel_type: string
          contact_name: string
          created_at?: string
          credit_limit?: number | null
          customer_source: string
          email: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name: string
          payment_terms?: string | null
          phone: string
          status: string
          tags?: string[] | null
          total_orders?: number | null
          total_revenue?: number | null
          updated_at?: string
        }
        Update: {
          address?: string
          channel_type?: string
          contact_name?: string
          created_at?: string
          credit_limit?: number | null
          customer_source?: string
          email?: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name?: string
          payment_terms?: string | null
          phone?: string
          status?: string
          tags?: string[] | null
          total_orders?: number | null
          total_revenue?: number | null
          updated_at?: string
        }
        Relationships: []
      }
      discounts: {
        Row: {
          amount: number
          created_at: string
          event_id: string | null
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          event_id?: string | null
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          event_id?: string | null
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "discounts_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      disposal_events: {
        Row: {
          authorized_by: string | null
          created_at: string
          created_by: string | null
          disposal_method: string
          event_id: string | null
          id: string
          reason: string
          witness: string | null
        }
        Insert: {
          authorized_by?: string | null
          created_at?: string
          created_by?: string | null
          disposal_method: string
          event_id?: string | null
          id?: string
          reason: string
          witness?: string | null
        }
        Update: {
          authorized_by?: string | null
          created_at?: string
          created_by?: string | null
          disposal_method?: string
          event_id?: string | null
          id?: string
          reason?: string
          witness?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "disposal_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      environmental_monitoring: {
        Row: {
          alert_triggered: boolean
          created_at: string | null
          critical_limit_max: number | null
          critical_limit_min: number | null
          id: string
          location_id: string | null
          measurement_timestamp: string
          measurement_type: string
          measurement_unit: string
          measurement_value: number
          sensor_id: string
          within_limits: boolean | null
          zone_name: string
        }
        Insert: {
          alert_triggered?: boolean
          created_at?: string | null
          critical_limit_max?: number | null
          critical_limit_min?: number | null
          id?: string
          location_id?: string | null
          measurement_timestamp?: string
          measurement_type: string
          measurement_unit: string
          measurement_value: number
          sensor_id: string
          within_limits?: boolean | null
          zone_name: string
        }
        Update: {
          alert_triggered?: boolean
          created_at?: string | null
          critical_limit_max?: number | null
          critical_limit_min?: number | null
          id?: string
          location_id?: string | null
          measurement_timestamp?: string
          measurement_type?: string
          measurement_unit?: string
          measurement_value?: number
          sensor_id?: string
          within_limits?: boolean | null
          zone_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "environmental_monitoring_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
        ]
      }
      equipment_calibrations: {
        Row: {
          adjustment_made: boolean
          after_readings: Json | null
          before_readings: Json | null
          calibration_date: string
          calibration_method: string
          calibration_passed: boolean
          calibration_standard: string
          certificate_number: string | null
          created_at: string | null
          equipment_id: string
          equipment_name: string
          equipment_type: string
          id: string
          next_calibration_due: string
          notes: string | null
          performed_by: string
        }
        Insert: {
          adjustment_made?: boolean
          after_readings?: Json | null
          before_readings?: Json | null
          calibration_date?: string
          calibration_method: string
          calibration_passed: boolean
          calibration_standard: string
          certificate_number?: string | null
          created_at?: string | null
          equipment_id: string
          equipment_name: string
          equipment_type: string
          id?: string
          next_calibration_due: string
          notes?: string | null
          performed_by: string
        }
        Update: {
          adjustment_made?: boolean
          after_readings?: Json | null
          before_readings?: Json | null
          calibration_date?: string
          calibration_method?: string
          calibration_passed?: boolean
          calibration_standard?: string
          certificate_number?: string | null
          created_at?: string | null
          equipment_id?: string
          equipment_name?: string
          equipment_type?: string
          id?: string
          next_calibration_due?: string
          notes?: string | null
          performed_by?: string
        }
        Relationships: []
      }
      event_lots: {
        Row: {
          event_id: string
          id: string
          lot_id: string
          qty: number | null
          role: string
          uom: string | null
        }
        Insert: {
          event_id: string
          id?: string
          lot_id: string
          qty?: number | null
          role: string
          uom?: string | null
        }
        Update: {
          event_id?: string
          id?: string
          lot_id?: string
          qty?: number | null
          role?: string
          uom?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_lots_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "event_lots_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "event_lots_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "traceability_events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "event_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "event_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "lots"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          cogs: number | null
          created_at: string
          created_by: string | null
          customer_id: string | null
          event_type: string | null
          external_refs: Json | null
          gdst_data: Json | null
          id: string
          metadata: Json | null
          move_to_balance_sheet: boolean | null
          notes: string | null
          per_unit_cogs: number | null
          product_id: string | null
          quality_status: string | null
          quantity: number
          temperature: number | null
          transaction_id: string | null
          unit: string
          updated_at: string
          vendor_id: string | null
        }
        Insert: {
          cogs?: number | null
          created_at?: string
          created_by?: string | null
          customer_id?: string | null
          event_type?: string | null
          external_refs?: Json | null
          gdst_data?: Json | null
          id?: string
          metadata?: Json | null
          move_to_balance_sheet?: boolean | null
          notes?: string | null
          per_unit_cogs?: number | null
          product_id?: string | null
          quality_status?: string | null
          quantity: number
          temperature?: number | null
          transaction_id?: string | null
          unit: string
          updated_at?: string
          vendor_id?: string | null
        }
        Update: {
          cogs?: number | null
          created_at?: string
          created_by?: string | null
          customer_id?: string | null
          event_type?: string | null
          external_refs?: Json | null
          gdst_data?: Json | null
          id?: string
          metadata?: Json | null
          move_to_balance_sheet?: boolean | null
          notes?: string | null
          per_unit_cogs?: number | null
          product_id?: string | null
          quality_status?: string | null
          quantity?: number
          temperature?: number | null
          transaction_id?: string | null
          unit?: string
          updated_at?: string
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "events_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      fda_request_lots: {
        Row: {
          fda_request_id: string
          id: string
          lot_id: string
          provided_data: Json | null
          requested_data_elements: string[] | null
        }
        Insert: {
          fda_request_id: string
          id?: string
          lot_id: string
          provided_data?: Json | null
          requested_data_elements?: string[] | null
        }
        Update: {
          fda_request_id?: string
          id?: string
          lot_id?: string
          provided_data?: Json | null
          requested_data_elements?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "fda_request_lots_fda_request_id_fkey"
            columns: ["fda_request_id"]
            isOneToOne: false
            referencedRelation: "fda_requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fda_request_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "fda_request_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "fda_request_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "lots"
            referencedColumns: ["id"]
          },
        ]
      }
      fda_requests: {
        Row: {
          created_at: string | null
          id: string
          notes: string | null
          request_details: string
          request_number: string
          request_type: string
          requested_at: string
          requested_by: string
          responded_at: string | null
          responded_by: string | null
          response_data: Json | null
          response_deadline: string
          response_files: string[] | null
          status: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          notes?: string | null
          request_details: string
          request_number: string
          request_type: string
          requested_at?: string
          requested_by: string
          responded_at?: string | null
          responded_by?: string | null
          response_data?: Json | null
          response_deadline: string
          response_files?: string[] | null
          status?: string
        }
        Update: {
          created_at?: string | null
          id?: string
          notes?: string | null
          request_details?: string
          request_number?: string
          request_type?: string
          requested_at?: string
          requested_by?: string
          responded_at?: string | null
          responded_by?: string | null
          response_data?: Json | null
          response_deadline?: string
          response_files?: string[] | null
          status?: string
        }
        Relationships: []
      }
      fish_data: {
        Row: {
          average_percentage: number | null
          from: string
          id: number
          range_percentage: string | null
          species: string
          to: string
        }
        Insert: {
          average_percentage?: number | null
          from: string
          id?: number
          range_percentage?: string | null
          species: string
          to: string
        }
        Update: {
          average_percentage?: number | null
          from?: string
          id?: number
          range_percentage?: string | null
          species?: string
          to?: string
        }
        Relationships: []
      }
      fishing_areas: {
        Row: {
          area_code: string
          area_name: string
          area_type: string | null
          coordinates: Json | null
          created_at: string | null
          id: string
          management_authority: string | null
          regulations: string[] | null
        }
        Insert: {
          area_code: string
          area_name: string
          area_type?: string | null
          coordinates?: Json | null
          created_at?: string | null
          id?: string
          management_authority?: string | null
          regulations?: string[] | null
        }
        Update: {
          area_code?: string
          area_name?: string
          area_type?: string | null
          coordinates?: Json | null
          created_at?: string | null
          id?: string
          management_authority?: string | null
          regulations?: string[] | null
        }
        Relationships: []
      }
      genetic_analysis: {
        Row: {
          analysis_date: string
          analysis_type: string
          certificate_number: string | null
          confidence_level: number | null
          created_at: string | null
          id: string
          is_verified: boolean
          laboratory: string
          lot_id: string
          origin_confirmed: string | null
          results: Json
          sample_id: string
          species_confirmed: string | null
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          analysis_date: string
          analysis_type: string
          certificate_number?: string | null
          confidence_level?: number | null
          created_at?: string | null
          id?: string
          is_verified?: boolean
          laboratory: string
          lot_id: string
          origin_confirmed?: string | null
          results: Json
          sample_id: string
          species_confirmed?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          analysis_date?: string
          analysis_type?: string
          certificate_number?: string | null
          confidence_level?: number | null
          created_at?: string | null
          id?: string
          is_verified?: boolean
          laboratory?: string
          lot_id?: string
          origin_confirmed?: string | null
          results?: Json
          sample_id?: string
          species_confirmed?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "genetic_analysis_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "genetic_analysis_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "genetic_analysis_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "lots"
            referencedColumns: ["id"]
          },
        ]
      }
      haccp_plan_templates: {
        Row: {
          created_at: string | null
          description: string | null
          hazards: Json
          id: string
          is_active: boolean
          monitoring_procedures: Json
          product_category: string
          recommended_ccps: Json
          regulatory_references: string[] | null
          species_category: string
          template_name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          hazards: Json
          id?: string
          is_active?: boolean
          monitoring_procedures: Json
          product_category: string
          recommended_ccps: Json
          regulatory_references?: string[] | null
          species_category: string
          template_name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          hazards?: Json
          id?: string
          is_active?: boolean
          monitoring_procedures?: Json
          product_category?: string
          recommended_ccps?: Json
          regulatory_references?: string[] | null
          species_category?: string
          template_name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      hazard_analysis: {
        Row: {
          created_at: string | null
          hazard_description: string
          hazard_type: string
          id: string
          likelihood: string
          process_step: string
          product_id: string | null
          risk_level: string | null
          severity: string
        }
        Insert: {
          created_at?: string | null
          hazard_description: string
          hazard_type: string
          id?: string
          likelihood: string
          process_step: string
          product_id?: string | null
          risk_level?: string | null
          severity: string
        }
        Update: {
          created_at?: string | null
          hazard_description?: string
          hazard_type?: string
          id?: string
          likelihood?: string
          process_step?: string
          product_id?: string | null
          risk_level?: string | null
          severity?: string
        }
        Relationships: [
          {
            foreignKeyName: "hazard_analysis_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      import_logs: {
        Row: {
          created_at: string
          created_by: string | null
          error_details: Json | null
          error_rows: number
          filename: string
          id: string
          processed_rows: number
          status: string
          total_rows: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          error_details?: Json | null
          error_rows?: number
          filename: string
          id?: string
          processed_rows?: number
          status?: string
          total_rows?: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          error_details?: Json | null
          error_rows?: number
          filename?: string
          id?: string
          processed_rows?: number
          status?: string
          total_rows?: number
          updated_at?: string
        }
        Relationships: []
      }
      inventory: {
        Row: {
          category: string
          cost: number | null
          created_at: string
          event_type: string
          expiry_date: string | null
          handling_instructions: string | null
          id: string
          images: string[] | null
          metadata: Json
          min_stock: number
          name: string
          notes: string
          origin: string | null
          price: number | null
          product_id: string | null
          quantity: number
          seasonal_availability: string[] | null
          source: string | null
          species_details: Json | null
          stock: number
          storage_temp: string | null
          sub_category: string | null
          supplier_id: string | null
          total_amount: number
          unit_price: number
          updated_at: string
        }
        Insert: {
          category: string
          cost?: number | null
          created_at?: string
          event_type?: string
          expiry_date?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          metadata?: Json
          min_stock?: number
          name?: string
          notes?: string
          origin?: string | null
          price?: number | null
          product_id?: string | null
          quantity?: number
          seasonal_availability?: string[] | null
          source?: string | null
          species_details?: Json | null
          stock?: number
          storage_temp?: string | null
          sub_category?: string | null
          supplier_id?: string | null
          total_amount?: number
          unit_price?: number
          updated_at?: string
        }
        Update: {
          category?: string
          cost?: number | null
          created_at?: string
          event_type?: string
          expiry_date?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          metadata?: Json
          min_stock?: number
          name?: string
          notes?: string
          origin?: string | null
          price?: number | null
          product_id?: string | null
          quantity?: number
          seasonal_availability?: string[] | null
          source?: string | null
          species_details?: Json | null
          stock?: number
          storage_temp?: string | null
          sub_category?: string | null
          supplier_id?: string | null
          total_amount?: number
          unit_price?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_inventory_product"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "fk_inventory_product"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "fk_inventory_product"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory_events: {
        Row: {
          category: string
          created_at: string | null
          event_type: string
          id: string
          images: string[] | null
          metadata: Json | null
          name: string | null
          notes: string | null
          product_id: string | null
          quantity: number | null
          total_amount: number | null
          unit_price: number | null
          updated_at: string | null
        }
        Insert: {
          category?: string
          created_at?: string | null
          event_type: string
          id?: string
          images?: string[] | null
          metadata?: Json | null
          name?: string | null
          notes?: string | null
          product_id?: string | null
          quantity?: number | null
          total_amount?: number | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          event_type?: string
          id?: string
          images?: string[] | null
          metadata?: Json | null
          name?: string | null
          notes?: string | null
          product_id?: string | null
          quantity?: number | null
          total_amount?: number | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_events_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "inventory_events_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "inventory_events_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
        ]
      }
      locations: {
        Row: {
          address: Json | null
          created_at: string | null
          gln: string | null
          id: string
          name: string
          partner_id: string
          role: string | null
          updated_at: string | null
        }
        Insert: {
          address?: Json | null
          created_at?: string | null
          gln?: string | null
          id?: string
          name: string
          partner_id: string
          role?: string | null
          updated_at?: string | null
        }
        Update: {
          address?: Json | null
          created_at?: string | null
          gln?: string | null
          id?: string
          name?: string
          partner_id?: string
          role?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "locations_partner_id_fkey"
            columns: ["partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
        ]
      }
      lot_shelf_life: {
        Row: {
          created_at: string | null
          expiry_date: string
          id: string
          last_quality_check: string | null
          lot_id: string
          production_date: string
          quality_adjustments: Json | null
          quality_status: string
          remaining_shelf_life_days: number | null
          shelf_life_parameters_id: string
          temperature_abuse_incidents: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          expiry_date: string
          id?: string
          last_quality_check?: string | null
          lot_id: string
          production_date: string
          quality_adjustments?: Json | null
          quality_status?: string
          remaining_shelf_life_days?: number | null
          shelf_life_parameters_id: string
          temperature_abuse_incidents?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          expiry_date?: string
          id?: string
          last_quality_check?: string | null
          lot_id?: string
          production_date?: string
          quality_adjustments?: Json | null
          quality_status?: string
          remaining_shelf_life_days?: number | null
          shelf_life_parameters_id?: string
          temperature_abuse_incidents?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lot_shelf_life_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "lot_shelf_life_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "lot_shelf_life_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "lots"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "lot_shelf_life_shelf_life_parameters_id_fkey"
            columns: ["shelf_life_parameters_id"]
            isOneToOne: false
            referencedRelation: "shelf_life_parameters"
            referencedColumns: ["id"]
          },
        ]
      }
      lots: {
        Row: {
          created_at: string | null
          expiry_date: string | null
          harvest_or_prod_date: string | null
          id: string
          initial_qty: number | null
          landing_date: string | null
          notes: string | null
          origin_country: string | null
          product_id: string
          status: string
          tlc: string | null
          uom: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          expiry_date?: string | null
          harvest_or_prod_date?: string | null
          id?: string
          initial_qty?: number | null
          landing_date?: string | null
          notes?: string | null
          origin_country?: string | null
          product_id: string
          status?: string
          tlc?: string | null
          uom?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          expiry_date?: string | null
          harvest_or_prod_date?: string | null
          id?: string
          initial_qty?: number | null
          landing_date?: string | null
          notes?: string | null
          origin_country?: string | null
          product_id?: string
          status?: string
          tlc?: string | null
          uom?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "lots_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "lots_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "lots_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
        ]
      }
      message_queue: {
        Row: {
          created_at: string
          id: number
          message: string | null
          message_id: number | null
          user_id: number
        }
        Insert: {
          created_at?: string
          id?: number
          message?: string | null
          message_id?: number | null
          user_id?: number
        }
        Update: {
          created_at?: string
          id?: number
          message?: string | null
          message_id?: number | null
          user_id?: number
        }
        Relationships: []
      }
      notifications: {
        Row: {
          created_at: string
          data: Json | null
          id: string
          message: string
          read: boolean | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          data?: Json | null
          id?: string
          message: string
          read?: boolean | null
          title: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string
          data?: Json | null
          id?: string
          message?: string
          read?: boolean | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      packaging_materials: {
        Row: {
          cost_per_unit: number
          created_at: string | null
          id: string
          name: string
        }
        Insert: {
          cost_per_unit: number
          created_at?: string | null
          id?: string
          name: string
        }
        Update: {
          cost_per_unit?: number
          created_at?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      partners: {
        Row: {
          contact: Json | null
          created_at: string | null
          gln: string | null
          id: string
          name: string
          type: string
          updated_at: string | null
        }
        Insert: {
          contact?: Json | null
          created_at?: string | null
          gln?: string | null
          id?: string
          name: string
          type: string
          updated_at?: string | null
        }
        Update: {
          contact?: Json | null
          created_at?: string | null
          gln?: string | null
          id?: string
          name?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      platform_integrations: {
        Row: {
          config: Json
          created_at: string
          error_log: Json | null
          id: string
          last_sync: string | null
          platform: string | null
          status: string | null
          updated_at: string
        }
        Insert: {
          config: Json
          created_at?: string
          error_log?: Json | null
          id?: string
          last_sync?: string | null
          platform?: string | null
          status?: string | null
          updated_at?: string
        }
        Update: {
          config?: Json
          created_at?: string
          error_log?: Json | null
          id?: string
          last_sync?: string | null
          platform?: string | null
          status?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      processing_templates: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          name: string
          template_data: Json
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name: string
          template_data: Json
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name?: string
          template_data?: Json
          updated_at?: string
        }
        Relationships: []
      }
      product_allergens: {
        Row: {
          allergen_status: string
          allergen_type: string
          control_measures: string | null
          created_at: string | null
          declaration_required: boolean | null
          id: string
          product_id: string
          source_of_contamination: string | null
          testing_required: boolean
        }
        Insert: {
          allergen_status: string
          allergen_type: string
          control_measures?: string | null
          created_at?: string | null
          declaration_required?: boolean | null
          id?: string
          product_id: string
          source_of_contamination?: string | null
          testing_required?: boolean
        }
        Update: {
          allergen_status?: string
          allergen_type?: string
          control_measures?: string | null
          created_at?: string | null
          declaration_required?: boolean | null
          id?: string
          product_id?: string
          source_of_contamination?: string | null
          testing_required?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "product_allergens_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "product_allergens_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "product_allergens_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
        ]
      }
      product_categories: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      product_cost: {
        Row: {
          batch_id: string | null
          created_at: string
          dbp_sku: string | null
          id: string
          pcs_sku: string | null
          product_id: string | null
          product_name: string | null
          shipping_id: string | null
          supplier: string | null
          supplier_id: string | null
        }
        Insert: {
          batch_id?: string | null
          created_at?: string
          dbp_sku?: string | null
          id?: string
          pcs_sku?: string | null
          product_id?: string | null
          product_name?: string | null
          shipping_id?: string | null
          supplier?: string | null
          supplier_id?: string | null
        }
        Update: {
          batch_id?: string | null
          created_at?: string
          dbp_sku?: string | null
          id?: string
          pcs_sku?: string | null
          product_id?: string | null
          product_name?: string | null
          shipping_id?: string | null
          supplier?: string | null
          supplier_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_cost_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "batches"
            referencedColumns: ["product_id"]
          },
        ]
      }
      product_forms: {
        Row: {
          created_at: string | null
          id: string
          name: string
          processing_cost_per_pound: number
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          processing_cost_per_pound?: number
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          processing_cost_per_pound?: number
        }
        Relationships: []
      }
      production_events: {
        Row: {
          batch_number: string
          created_at: string
          created_by: string | null
          event_id: string | null
          id: string
          labor_hours: number | null
          quality_check_status: string | null
          recipe_id: string | null
          yield_amount: number | null
          yield_unit: string | null
        }
        Insert: {
          batch_number: string
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          labor_hours?: number | null
          quality_check_status?: string | null
          recipe_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Update: {
          batch_number?: string
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          labor_hours?: number | null
          quality_check_status?: string | null
          recipe_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "production_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          created_at: string | null
          id: string
          name: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      Products: {
        Row: {
          amount: number | null
          catch_method: string | null
          category: string | null
          category_id: string | null
          condition: string | null
          cost: number | null
          created_at: string
          date: string | null
          expiry_date: string | null
          fao_area: string | null
          gtin: string | null
          handling_instructions: string | null
          id: string
          images: string[] | null
          market_name: string | null
          market_price_history: Json | null
          metadata: Json | null
          min_stock: number
          name: string
          notes: string | null
          old_category: string | null
          origin: string | null
          origin_country: string | null
          other_condition: string | null
          price: number | null
          product_category_id: string | null
          scientific_name: string | null
          species_details: Json | null
          stock: number | null
          storage_temp: string | null
          storage_temp_max: number | null
          storage_temp_min: number | null
          sub_category: string | null
          supplier: string | null
          supplier_id: string | null
          unit: string | null
          Unit: string | null
          updated_at: string
        }
        Insert: {
          amount?: number | null
          catch_method?: string | null
          category?: string | null
          category_id?: string | null
          condition?: string | null
          cost?: number | null
          created_at?: string
          date?: string | null
          expiry_date?: string | null
          fao_area?: string | null
          gtin?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          market_name?: string | null
          market_price_history?: Json | null
          metadata?: Json | null
          min_stock?: number
          name: string
          notes?: string | null
          old_category?: string | null
          origin?: string | null
          origin_country?: string | null
          other_condition?: string | null
          price?: number | null
          product_category_id?: string | null
          scientific_name?: string | null
          species_details?: Json | null
          stock?: number | null
          storage_temp?: string | null
          storage_temp_max?: number | null
          storage_temp_min?: number | null
          sub_category?: string | null
          supplier?: string | null
          supplier_id?: string | null
          unit?: string | null
          Unit?: string | null
          updated_at?: string
        }
        Update: {
          amount?: number | null
          catch_method?: string | null
          category?: string | null
          category_id?: string | null
          condition?: string | null
          cost?: number | null
          created_at?: string
          date?: string | null
          expiry_date?: string | null
          fao_area?: string | null
          gtin?: string | null
          handling_instructions?: string | null
          id?: string
          images?: string[] | null
          market_name?: string | null
          market_price_history?: Json | null
          metadata?: Json | null
          min_stock?: number
          name?: string
          notes?: string | null
          old_category?: string | null
          origin?: string | null
          origin_country?: string | null
          other_condition?: string | null
          price?: number | null
          product_category_id?: string | null
          scientific_name?: string | null
          species_details?: Json | null
          stock?: number | null
          storage_temp?: string | null
          storage_temp_max?: number | null
          storage_temp_min?: number | null
          sub_category?: string | null
          supplier?: string | null
          supplier_id?: string | null
          unit?: string | null
          Unit?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_products_supplier_to_vendors"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "fk_products_supplier_to_vendors"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "Products_product_category_id_fkey"
            columns: ["product_category_id"]
            isOneToOne: false
            referencedRelation: "product_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          email: string
          full_name: string | null
          id: string
          role: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          role?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          role?: string
          updated_at?: string
        }
        Relationships: []
      }
      quote_items: {
        Row: {
          created_at: string | null
          final_weight: number | null
          id: string
          input_weight: number | null
          price_per_pound: number | null
          product_form_id: string | null
          quote_id: string | null
          species_id: string | null
          total_price: number | null
        }
        Insert: {
          created_at?: string | null
          final_weight?: number | null
          id?: string
          input_weight?: number | null
          price_per_pound?: number | null
          product_form_id?: string | null
          quote_id?: string | null
          species_id?: string | null
          total_price?: number | null
        }
        Update: {
          created_at?: string | null
          final_weight?: number | null
          id?: string
          input_weight?: number | null
          price_per_pound?: number | null
          product_form_id?: string | null
          quote_id?: string | null
          species_id?: string | null
          total_price?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "quote_items_product_form_id_fkey"
            columns: ["product_form_id"]
            isOneToOne: false
            referencedRelation: "product_forms"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quote_items_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes"
            referencedColumns: ["id"]
          },
        ]
      }
      quotes: {
        Row: {
          created_at: string | null
          customer_name: string | null
          delivery_timeframe: string | null
          id: string
          packaging_id: string | null
          payment_terms: string | null
          shipping_address: string | null
          shipping_cost: number | null
          shipping_method: string | null
          subtotal: number | null
          total: number | null
        }
        Insert: {
          created_at?: string | null
          customer_name?: string | null
          delivery_timeframe?: string | null
          id?: string
          packaging_id?: string | null
          payment_terms?: string | null
          shipping_address?: string | null
          shipping_cost?: number | null
          shipping_method?: string | null
          subtotal?: number | null
          total?: number | null
        }
        Update: {
          created_at?: string | null
          customer_name?: string | null
          delivery_timeframe?: string | null
          id?: string
          packaging_id?: string | null
          payment_terms?: string | null
          shipping_address?: string | null
          shipping_cost?: number | null
          shipping_method?: string | null
          subtotal?: number | null
          total?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "quotes_packaging_id_fkey"
            columns: ["packaging_id"]
            isOneToOne: false
            referencedRelation: "packaging_materials"
            referencedColumns: ["id"]
          },
        ]
      }
      recall_lots: {
        Row: {
          created_at: string | null
          customer_notifications_sent: number | null
          disposition: string | null
          id: string
          lot_id: string
          product_recovered: boolean
          quantity_recalled: number
          recall_id: string
          recovery_date: string | null
        }
        Insert: {
          created_at?: string | null
          customer_notifications_sent?: number | null
          disposition?: string | null
          id?: string
          lot_id: string
          product_recovered?: boolean
          quantity_recalled: number
          recall_id: string
          recovery_date?: string | null
        }
        Update: {
          created_at?: string | null
          customer_notifications_sent?: number | null
          disposition?: string | null
          id?: string
          lot_id?: string
          product_recovered?: boolean
          quantity_recalled?: number
          recall_id?: string
          recovery_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recall_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "recall_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "recall_lots_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "lots"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recall_lots_recall_id_fkey"
            columns: ["recall_id"]
            isOneToOne: false
            referencedRelation: "recalls"
            referencedColumns: ["id"]
          },
        ]
      }
      recall_notifications: {
        Row: {
          acknowledged: boolean
          acknowledged_at: string | null
          created_at: string | null
          customer_id: string
          follow_up_completed: boolean
          follow_up_required: boolean
          id: string
          notification_date: string
          notification_method: string
          recall_id: string
          response_received: string | null
        }
        Insert: {
          acknowledged?: boolean
          acknowledged_at?: string | null
          created_at?: string | null
          customer_id: string
          follow_up_completed?: boolean
          follow_up_required?: boolean
          id?: string
          notification_date?: string
          notification_method: string
          recall_id: string
          response_received?: string | null
        }
        Update: {
          acknowledged?: boolean
          acknowledged_at?: string | null
          created_at?: string | null
          customer_id?: string
          follow_up_completed?: boolean
          follow_up_required?: boolean
          id?: string
          notification_date?: string
          notification_method?: string
          recall_id?: string
          response_received?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "recall_notifications_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "recall_notifications_recall_id_fkey"
            columns: ["recall_id"]
            isOneToOne: false
            referencedRelation: "recalls"
            referencedColumns: ["id"]
          },
        ]
      }
      recalls: {
        Row: {
          created_at: string | null
          distribution_pattern: string | null
          effectiveness_checks_completed: boolean
          fda_recall_number: string | null
          health_hazard_evaluation: string | null
          id: string
          initiated_date: string
          public_notification_date: string | null
          public_notification_required: boolean
          quantity_distributed: number | null
          quantity_produced: number | null
          quantity_recovered: number | null
          reason_for_recall: string
          recall_class: string
          recall_coordinator: string
          recall_number: string
          recall_type: string
          recalled_product_description: string
          recovery_percentage: number | null
          status: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          distribution_pattern?: string | null
          effectiveness_checks_completed?: boolean
          fda_recall_number?: string | null
          health_hazard_evaluation?: string | null
          id?: string
          initiated_date: string
          public_notification_date?: string | null
          public_notification_required?: boolean
          quantity_distributed?: number | null
          quantity_produced?: number | null
          quantity_recovered?: number | null
          reason_for_recall: string
          recall_class: string
          recall_coordinator: string
          recall_number: string
          recall_type: string
          recalled_product_description: string
          recovery_percentage?: number | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          distribution_pattern?: string | null
          effectiveness_checks_completed?: boolean
          fda_recall_number?: string | null
          health_hazard_evaluation?: string | null
          id?: string
          initiated_date?: string
          public_notification_date?: string | null
          public_notification_required?: boolean
          quantity_distributed?: number | null
          quantity_produced?: number | null
          quantity_recovered?: number | null
          reason_for_recall?: string
          recall_class?: string
          recall_coordinator?: string
          recall_number?: string
          recall_type?: string
          recalled_product_description?: string
          recovery_percentage?: number | null
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      receiving_events: {
        Row: {
          carrier: string | null
          created_at: string
          created_by: string | null
          event_id: string | null
          id: string
          product_form: string
          quality_check_status: string | null
          quality_notes: string | null
          received_temp: number | null
          shipping_method: string | null
          tracking_number: string | null
          transit_duration: string | null
        }
        Insert: {
          carrier?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          product_form: string
          quality_check_status?: string | null
          quality_notes?: string | null
          received_temp?: number | null
          shipping_method?: string | null
          tracking_number?: string | null
          transit_duration?: string | null
        }
        Update: {
          carrier?: string | null
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          product_form?: string
          quality_check_status?: string | null
          quality_notes?: string | null
          received_temp?: number | null
          shipping_method?: string | null
          tracking_number?: string | null
          transit_duration?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "receiving_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      reprocessing_events: {
        Row: {
          created_at: string
          created_by: string | null
          event_id: string | null
          id: string
          new_batch_number: string
          quality_check_status: string | null
          reason: string
          source_batch_id: string | null
          yield_amount: number | null
          yield_unit: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          new_batch_number: string
          quality_check_status?: string | null
          reason: string
          source_batch_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          event_id?: string | null
          id?: string
          new_batch_number?: string
          quality_check_status?: string | null
          reason?: string
          source_batch_id?: string | null
          yield_amount?: number | null
          yield_unit?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reprocessing_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reprocessing_events_source_batch_id_fkey"
            columns: ["source_batch_id"]
            isOneToOne: false
            referencedRelation: "production_events"
            referencedColumns: ["id"]
          },
        ]
      }
      sales_events: {
        Row: {
          category: string | null
          channel: string
          created_at: string
          created_by: string | null
          custormer: string | null
          date: string | null
          discount_amount: number | null
          event_id: string | null
          fulfilled_by: string | null
          gross_amount: number
          id: string
          margin_percentage: number | null
          net_amount: string | null
          payment_method: string | null
          price_per_unit: number
          product_name: string | null
          qty: number | null
          sale_type: string | null
          sku: number | null
          unit: string | null
          vendor: string | null
        }
        Insert: {
          category?: string | null
          channel: string
          created_at?: string
          created_by?: string | null
          custormer?: string | null
          date?: string | null
          discount_amount?: number | null
          event_id?: string | null
          fulfilled_by?: string | null
          gross_amount: number
          id?: string
          margin_percentage?: number | null
          net_amount?: string | null
          payment_method?: string | null
          price_per_unit: number
          product_name?: string | null
          qty?: number | null
          sale_type?: string | null
          sku?: number | null
          unit?: string | null
          vendor?: string | null
        }
        Update: {
          category?: string | null
          channel?: string
          created_at?: string
          created_by?: string | null
          custormer?: string | null
          date?: string | null
          discount_amount?: number | null
          event_id?: string | null
          fulfilled_by?: string | null
          gross_amount?: number
          id?: string
          margin_percentage?: number | null
          net_amount?: string | null
          payment_method?: string | null
          price_per_unit?: number
          product_name?: string | null
          qty?: number | null
          sale_type?: string | null
          sku?: number | null
          unit?: string | null
          vendor?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sales_events_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
        ]
      }
      sensors: {
        Row: {
          battery_level: number | null
          battery_voltage: number | null
          calibrated_by: string | null
          connection_status: string | null
          created_at: string | null
          created_by: string | null
          custom_humidity_max: number | null
          custom_humidity_min: number | null
          custom_temp_max_celsius: number | null
          custom_temp_max_fahrenheit: number | null
          custom_temp_min_celsius: number | null
          custom_temp_min_fahrenheit: number | null
          data_sync_enabled: boolean | null
          description: string | null
          device_name: string
          device_type: string | null
          firmware_version: string | null
          humidity_offset: number | null
          id: string
          installation_date: string | null
          is_active: boolean | null
          is_online: boolean | null
          last_calibrated_at: string | null
          last_maintenance_date: string | null
          last_seen_at: string | null
          location_description: string | null
          maintenance_notes: string | null
          metadata: Json | null
          name: string
          next_maintenance_due: string | null
          reading_interval_minutes: number | null
          sensor_id: string
          signal_strength: number | null
          storage_area_id: string | null
          temp_offset_celsius: number | null
          temp_offset_fahrenheit: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          battery_level?: number | null
          battery_voltage?: number | null
          calibrated_by?: string | null
          connection_status?: string | null
          created_at?: string | null
          created_by?: string | null
          custom_humidity_max?: number | null
          custom_humidity_min?: number | null
          custom_temp_max_celsius?: number | null
          custom_temp_max_fahrenheit?: number | null
          custom_temp_min_celsius?: number | null
          custom_temp_min_fahrenheit?: number | null
          data_sync_enabled?: boolean | null
          description?: string | null
          device_name: string
          device_type?: string | null
          firmware_version?: string | null
          humidity_offset?: number | null
          id?: string
          installation_date?: string | null
          is_active?: boolean | null
          is_online?: boolean | null
          last_calibrated_at?: string | null
          last_maintenance_date?: string | null
          last_seen_at?: string | null
          location_description?: string | null
          maintenance_notes?: string | null
          metadata?: Json | null
          name: string
          next_maintenance_due?: string | null
          reading_interval_minutes?: number | null
          sensor_id: string
          signal_strength?: number | null
          storage_area_id?: string | null
          temp_offset_celsius?: number | null
          temp_offset_fahrenheit?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          battery_level?: number | null
          battery_voltage?: number | null
          calibrated_by?: string | null
          connection_status?: string | null
          created_at?: string | null
          created_by?: string | null
          custom_humidity_max?: number | null
          custom_humidity_min?: number | null
          custom_temp_max_celsius?: number | null
          custom_temp_max_fahrenheit?: number | null
          custom_temp_min_celsius?: number | null
          custom_temp_min_fahrenheit?: number | null
          data_sync_enabled?: boolean | null
          description?: string | null
          device_name?: string
          device_type?: string | null
          firmware_version?: string | null
          humidity_offset?: number | null
          id?: string
          installation_date?: string | null
          is_active?: boolean | null
          is_online?: boolean | null
          last_calibrated_at?: string | null
          last_maintenance_date?: string | null
          last_seen_at?: string | null
          location_description?: string | null
          maintenance_notes?: string | null
          metadata?: Json | null
          name?: string
          next_maintenance_due?: string | null
          reading_interval_minutes?: number | null
          sensor_id?: string
          signal_strength?: number | null
          storage_area_id?: string | null
          temp_offset_celsius?: number | null
          temp_offset_fahrenheit?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "sensors_storage_area_id_fkey"
            columns: ["storage_area_id"]
            isOneToOne: false
            referencedRelation: "storage_areas"
            referencedColumns: ["id"]
          },
        ]
      }
      shelf_life_parameters: {
        Row: {
          created_at: string | null
          degradation_factors: string[] | null
          id: string
          packaging_requirements: string | null
          product_id: string
          quality_indicators: Json | null
          shelf_life_days: number
          storage_conditions: string
          study_reference: string | null
          temperature_range: string
          updated_at: string | null
          validated_by: string | null
          validation_date: string | null
        }
        Insert: {
          created_at?: string | null
          degradation_factors?: string[] | null
          id?: string
          packaging_requirements?: string | null
          product_id: string
          quality_indicators?: Json | null
          shelf_life_days: number
          storage_conditions: string
          study_reference?: string | null
          temperature_range: string
          updated_at?: string | null
          validated_by?: string | null
          validation_date?: string | null
        }
        Update: {
          created_at?: string | null
          degradation_factors?: string[] | null
          id?: string
          packaging_requirements?: string | null
          product_id?: string
          quality_indicators?: Json | null
          shelf_life_days?: number
          storage_conditions?: string
          study_reference?: string | null
          temperature_range?: string
          updated_at?: string | null
          validated_by?: string | null
          validation_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "shelf_life_parameters_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "shelf_life_parameters_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["product_id"]
          },
          {
            foreignKeyName: "shelf_life_parameters_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "Products"
            referencedColumns: ["id"]
          },
        ]
      }
      shipping_rates: {
        Row: {
          created_at: string | null
          id: string
          max_weight: number | null
          method: string
          min_weight: number | null
          rate_per_pound: number
          region: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          max_weight?: number | null
          method: string
          min_weight?: number | null
          rate_per_pound: number
          region?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          max_weight?: number | null
          method?: string
          min_weight?: number | null
          rate_per_pound?: number
          region?: string | null
        }
        Relationships: []
      }
      shipping_templates: {
        Row: {
          created_at: string
          created_by: string | null
          description: string | null
          id: string
          name: string
          template_data: Json
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name: string
          template_data: Json
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string | null
          description?: string | null
          id?: string
          name?: string
          template_data?: Json
          updated_at?: string
        }
        Relationships: []
      }
      simp_documentation: {
        Row: {
          aquaculture_method: string | null
          chain_of_custody_number: string | null
          created_at: string | null
          customs_district: string | null
          customs_entry_type: string | null
          harvest_date: string
          harvest_location: string
          id: string
          import_entry_number: string
          importer_address: string
          importer_name: string
          lot_id: string
          point_of_first_sale: string | null
          product_form: string
          production_method: string
          species_common_name: string
          species_scientific_name: string
          vessel_id: string | null
          wild_catch_method: string | null
        }
        Insert: {
          aquaculture_method?: string | null
          chain_of_custody_number?: string | null
          created_at?: string | null
          customs_district?: string | null
          customs_entry_type?: string | null
          harvest_date: string
          harvest_location: string
          id?: string
          import_entry_number: string
          importer_address: string
          importer_name: string
          lot_id: string
          point_of_first_sale?: string | null
          product_form: string
          production_method: string
          species_common_name: string
          species_scientific_name: string
          vessel_id?: string | null
          wild_catch_method?: string | null
        }
        Update: {
          aquaculture_method?: string | null
          chain_of_custody_number?: string | null
          created_at?: string | null
          customs_district?: string | null
          customs_entry_type?: string | null
          harvest_date?: string
          harvest_location?: string
          id?: string
          import_entry_number?: string
          importer_address?: string
          importer_name?: string
          lot_id?: string
          point_of_first_sale?: string | null
          product_form?: string
          production_method?: string
          species_common_name?: string
          species_scientific_name?: string
          vessel_id?: string | null
          wild_catch_method?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "simp_documentation_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "fsma_traceability_view"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "simp_documentation_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "gdst_traceability_export"
            referencedColumns: ["lot_id"]
          },
          {
            foreignKeyName: "simp_documentation_lot_id_fkey"
            columns: ["lot_id"]
            isOneToOne: false
            referencedRelation: "lots"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "simp_documentation_vessel_id_fkey"
            columns: ["vessel_id"]
            isOneToOne: false
            referencedRelation: "vessels"
            referencedColumns: ["id"]
          },
        ]
      }
      skus: {
        Row: {
          batch_number: string | null
          created_at: string
          id: string
          product_id: string | null
          sku: string
          updated_at: string
          vendor_id: string | null
          vendor_sku: string | null
        }
        Insert: {
          batch_number?: string | null
          created_at?: string
          id?: string
          product_id?: string | null
          sku: string
          updated_at?: string
          vendor_id?: string | null
          vendor_sku?: string | null
        }
        Update: {
          batch_number?: string | null
          created_at?: string
          id?: string
          product_id?: string | null
          sku?: string
          updated_at?: string
          vendor_id?: string | null
          vendor_sku?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "skus_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "inventory"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "skus_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "skus_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      storage_areas: {
        Row: {
          alert_enabled: boolean | null
          alert_threshold_minutes: number | null
          area_code: string | null
          area_type: string
          capacity_info: Json | null
          compliance_notes: string | null
          created_at: string | null
          created_by: string | null
          custom_area_type: string | null
          description: string | null
          escalation_minutes: number | null
          haccp_ccp_number: string | null
          haccp_required: boolean | null
          humidity_max: number | null
          humidity_min: number | null
          id: string
          is_active: boolean | null
          location: string | null
          metadata: Json | null
          name: string
          temp_max_celsius: number | null
          temp_max_fahrenheit: number | null
          temp_min_celsius: number | null
          temp_min_fahrenheit: number | null
          temp_unit: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          alert_enabled?: boolean | null
          alert_threshold_minutes?: number | null
          area_code?: string | null
          area_type: string
          capacity_info?: Json | null
          compliance_notes?: string | null
          created_at?: string | null
          created_by?: string | null
          custom_area_type?: string | null
          description?: string | null
          escalation_minutes?: number | null
          haccp_ccp_number?: string | null
          haccp_required?: boolean | null
          humidity_max?: number | null
          humidity_min?: number | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          metadata?: Json | null
          name: string
          temp_max_celsius?: number | null
          temp_max_fahrenheit?: number | null
          temp_min_celsius?: number | null
          temp_min_fahrenheit?: number | null
          temp_unit?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          alert_enabled?: boolean | null
          alert_threshold_minutes?: number | null
          area_code?: string | null
          area_type?: string
          capacity_info?: Json | null
          compliance_notes?: string | null
          created_at?: string | null
          created_by?: string | null
          custom_area_type?: string | null
          description?: string | null
          escalation_minutes?: number | null
          haccp_ccp_number?: string | null
          haccp_required?: boolean | null
          humidity_max?: number | null
          humidity_min?: number | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          metadata?: Json | null
          name?: string
          temp_max_celsius?: number | null
          temp_max_fahrenheit?: number | null
          temp_min_celsius?: number | null
          temp_min_fahrenheit?: number | null
          temp_unit?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      suppliers: {
        Row: {
          certifications: string[] | null
          contact_name: string | null
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
          rating: number | null
          specialties: string[] | null
          updated_at: string
        }
        Insert: {
          certifications?: string[] | null
          contact_name?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          rating?: number | null
          specialties?: string[] | null
          updated_at?: string
        }
        Update: {
          certifications?: string[] | null
          contact_name?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          rating?: number | null
          specialties?: string[] | null
          updated_at?: string
        }
        Relationships: []
      }
      sync_logs: {
        Row: {
          created_at: string
          direction: string | null
          error_details: Json | null
          id: string
          platform_id: string | null
          records_failed: number | null
          records_processed: number | null
          status: string | null
        }
        Insert: {
          created_at?: string
          direction?: string | null
          error_details?: Json | null
          id?: string
          platform_id?: string | null
          records_failed?: number | null
          records_processed?: number | null
          status?: string | null
        }
        Update: {
          created_at?: string
          direction?: string | null
          error_details?: Json | null
          id?: string
          platform_id?: string | null
          records_failed?: number | null
          records_processed?: number | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sync_logs_platform_id_fkey"
            columns: ["platform_id"]
            isOneToOne: false
            referencedRelation: "platform_integrations"
            referencedColumns: ["id"]
          },
        ]
      }
      temperature_alerts: {
        Row: {
          acknowledged_at: string | null
          acknowledged_by: string | null
          actual_value: number | null
          alert_status: string | null
          alert_type: string
          corrective_action_required: boolean | null
          corrective_action_taken: string | null
          created_at: string | null
          duration_minutes: number | null
          escalated_at: string | null
          escalated_to: string | null
          escalation_level: number | null
          haccp_critical: boolean | null
          id: string
          message: string
          metadata: Json | null
          reading_id: string | null
          resolution_notes: string | null
          resolved_at: string | null
          resolved_by: string | null
          sensor_id: string
          severity: string
          storage_area_id: string | null
          threshold_value: number | null
          title: string
          updated_at: string | null
          user_id: string
          verification_required: boolean | null
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          actual_value?: number | null
          alert_status?: string | null
          alert_type: string
          corrective_action_required?: boolean | null
          corrective_action_taken?: string | null
          created_at?: string | null
          duration_minutes?: number | null
          escalated_at?: string | null
          escalated_to?: string | null
          escalation_level?: number | null
          haccp_critical?: boolean | null
          id?: string
          message: string
          metadata?: Json | null
          reading_id?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          sensor_id: string
          severity: string
          storage_area_id?: string | null
          threshold_value?: number | null
          title: string
          updated_at?: string | null
          user_id: string
          verification_required?: boolean | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          actual_value?: number | null
          alert_status?: string | null
          alert_type?: string
          corrective_action_required?: boolean | null
          corrective_action_taken?: string | null
          created_at?: string | null
          duration_minutes?: number | null
          escalated_at?: string | null
          escalated_to?: string | null
          escalation_level?: number | null
          haccp_critical?: boolean | null
          id?: string
          message?: string
          metadata?: Json | null
          reading_id?: string | null
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          sensor_id?: string
          severity?: string
          storage_area_id?: string | null
          threshold_value?: number | null
          title?: string
          updated_at?: string | null
          user_id?: string
          verification_required?: boolean | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "temperature_alerts_reading_id_fkey"
            columns: ["reading_id"]
            isOneToOne: false
            referencedRelation: "temperature_readings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temperature_alerts_sensor_id_fkey"
            columns: ["sensor_id"]
            isOneToOne: false
            referencedRelation: "sensors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temperature_alerts_storage_area_id_fkey"
            columns: ["storage_area_id"]
            isOneToOne: false
            referencedRelation: "storage_areas"
            referencedColumns: ["id"]
          },
        ]
      }
      temperature_readings: {
        Row: {
          api_reading_id: string | null
          battery_level: number | null
          created_at: string | null
          data_source: string | null
          humidity: number | null
          humidity_violation: boolean | null
          id: string
          metadata: Json | null
          reading_quality: string | null
          recorded_at: string
          sensor_id: string
          signal_strength: number | null
          storage_area_id: string | null
          sync_status: string | null
          temp_celsius: number
          temp_fahrenheit: number
          temp_violation: boolean | null
          user_id: string
          within_safe_range: boolean
        }
        Insert: {
          api_reading_id?: string | null
          battery_level?: number | null
          created_at?: string | null
          data_source?: string | null
          humidity?: number | null
          humidity_violation?: boolean | null
          id?: string
          metadata?: Json | null
          reading_quality?: string | null
          recorded_at: string
          sensor_id: string
          signal_strength?: number | null
          storage_area_id?: string | null
          sync_status?: string | null
          temp_celsius: number
          temp_fahrenheit: number
          temp_violation?: boolean | null
          user_id: string
          within_safe_range?: boolean
        }
        Update: {
          api_reading_id?: string | null
          battery_level?: number | null
          created_at?: string | null
          data_source?: string | null
          humidity?: number | null
          humidity_violation?: boolean | null
          id?: string
          metadata?: Json | null
          reading_quality?: string | null
          recorded_at?: string
          sensor_id?: string
          signal_strength?: number | null
          storage_area_id?: string | null
          sync_status?: string | null
          temp_celsius?: number
          temp_fahrenheit?: number
          temp_violation?: boolean | null
          user_id?: string
          within_safe_range?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "temperature_readings_sensor_id_fkey"
            columns: ["sensor_id"]
            isOneToOne: false
            referencedRelation: "sensors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temperature_readings_storage_area_id_fkey"
            columns: ["storage_area_id"]
            isOneToOne: false
            referencedRelation: "storage_areas"
            referencedColumns: ["id"]
          },
        ]
      }
      traceability_events: {
        Row: {
          actor_location_id: string | null
          actor_partner_id: string | null
          catch_certificate_number: string | null
          catch_method: string | null
          chain_of_custody_certificate: string | null
          created_at: string | null
          event_time: string
          event_type: string
          fishing_area_id: string | null
          gdst_kde_version: string | null
          gear_type: string | null
          health_certificate_number: string | null
          id: string
          landing_location: string | null
          notes: string | null
          production_method: string | null
          reference_doc: string | null
          species_fao_code: string | null
          species_scientific_name: string | null
          temperature_data: Json | null
          transporter_partner_id: string | null
          updated_at: string | null
          vessel_id: string | null
        }
        Insert: {
          actor_location_id?: string | null
          actor_partner_id?: string | null
          catch_certificate_number?: string | null
          catch_method?: string | null
          chain_of_custody_certificate?: string | null
          created_at?: string | null
          event_time?: string
          event_type: string
          fishing_area_id?: string | null
          gdst_kde_version?: string | null
          gear_type?: string | null
          health_certificate_number?: string | null
          id?: string
          landing_location?: string | null
          notes?: string | null
          production_method?: string | null
          reference_doc?: string | null
          species_fao_code?: string | null
          species_scientific_name?: string | null
          temperature_data?: Json | null
          transporter_partner_id?: string | null
          updated_at?: string | null
          vessel_id?: string | null
        }
        Update: {
          actor_location_id?: string | null
          actor_partner_id?: string | null
          catch_certificate_number?: string | null
          catch_method?: string | null
          chain_of_custody_certificate?: string | null
          created_at?: string | null
          event_time?: string
          event_type?: string
          fishing_area_id?: string | null
          gdst_kde_version?: string | null
          gear_type?: string | null
          health_certificate_number?: string | null
          id?: string
          landing_location?: string | null
          notes?: string | null
          production_method?: string | null
          reference_doc?: string | null
          species_fao_code?: string | null
          species_scientific_name?: string | null
          temperature_data?: Json | null
          transporter_partner_id?: string | null
          updated_at?: string | null
          vessel_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "traceability_events_actor_location_id_fkey"
            columns: ["actor_location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "traceability_events_actor_partner_id_fkey"
            columns: ["actor_partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "traceability_events_fishing_area_id_fkey"
            columns: ["fishing_area_id"]
            isOneToOne: false
            referencedRelation: "fishing_areas"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "traceability_events_transporter_partner_id_fkey"
            columns: ["transporter_partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "traceability_events_vessel_id_fkey"
            columns: ["vessel_id"]
            isOneToOne: false
            referencedRelation: "vessels"
            referencedColumns: ["id"]
          },
        ]
      }
      vendor_compliance: {
        Row: {
          assessed_date: string | null
          audit_date: string | null
          audit_result: string | null
          audit_type: string | null
          auditor_name: string | null
          catch_certificate_provided: boolean | null
          chain_of_custody_complete: boolean | null
          cold_chain_maintained: boolean | null
          compliance_issues: string | null
          compliance_score: number | null
          compliance_status: string | null
          compliance_type: string
          corrective_actions_documented: boolean | null
          corrective_actions_required: string | null
          corrective_actions_taken: string | null
          created_by: string
          critical_control_points_met: boolean | null
          haccp_cert_expiry_date: string | null
          haccp_certified: boolean | null
          id: string
          next_review_date: string | null
          origin_documentation_complete: boolean | null
          processing_facility_certified: boolean | null
          species_verification_done: boolean | null
          temp_deviation_reported: boolean | null
          temp_log_provided: boolean | null
          temp_monitoring_device_used: boolean | null
          temp_within_safe_range: boolean | null
          temperature_logs_complete: boolean | null
          updated_at: string | null
          vendor_id: string
          vendor_interaction_id: string | null
        }
        Insert: {
          assessed_date?: string | null
          audit_date?: string | null
          audit_result?: string | null
          audit_type?: string | null
          auditor_name?: string | null
          catch_certificate_provided?: boolean | null
          chain_of_custody_complete?: boolean | null
          cold_chain_maintained?: boolean | null
          compliance_issues?: string | null
          compliance_score?: number | null
          compliance_status?: string | null
          compliance_type: string
          corrective_actions_documented?: boolean | null
          corrective_actions_required?: string | null
          corrective_actions_taken?: string | null
          created_by: string
          critical_control_points_met?: boolean | null
          haccp_cert_expiry_date?: string | null
          haccp_certified?: boolean | null
          id?: string
          next_review_date?: string | null
          origin_documentation_complete?: boolean | null
          processing_facility_certified?: boolean | null
          species_verification_done?: boolean | null
          temp_deviation_reported?: boolean | null
          temp_log_provided?: boolean | null
          temp_monitoring_device_used?: boolean | null
          temp_within_safe_range?: boolean | null
          temperature_logs_complete?: boolean | null
          updated_at?: string | null
          vendor_id: string
          vendor_interaction_id?: string | null
        }
        Update: {
          assessed_date?: string | null
          audit_date?: string | null
          audit_result?: string | null
          audit_type?: string | null
          auditor_name?: string | null
          catch_certificate_provided?: boolean | null
          chain_of_custody_complete?: boolean | null
          cold_chain_maintained?: boolean | null
          compliance_issues?: string | null
          compliance_score?: number | null
          compliance_status?: string | null
          compliance_type?: string
          corrective_actions_documented?: boolean | null
          corrective_actions_required?: string | null
          corrective_actions_taken?: string | null
          created_by?: string
          critical_control_points_met?: boolean | null
          haccp_cert_expiry_date?: string | null
          haccp_certified?: boolean | null
          id?: string
          next_review_date?: string | null
          origin_documentation_complete?: boolean | null
          processing_facility_certified?: boolean | null
          species_verification_done?: boolean | null
          temp_deviation_reported?: boolean | null
          temp_log_provided?: boolean | null
          temp_monitoring_device_used?: boolean | null
          temp_within_safe_range?: boolean | null
          temperature_logs_complete?: boolean | null
          updated_at?: string | null
          vendor_id?: string
          vendor_interaction_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "vendor_compliance_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "vendor_compliance_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vendor_compliance_vendor_interaction_id_fkey"
            columns: ["vendor_interaction_id"]
            isOneToOne: false
            referencedRelation: "vendor_interactions"
            referencedColumns: ["id"]
          },
        ]
      }
      vendor_interactions: {
        Row: {
          actual_delivery_date: string | null
          completion_rate: number | null
          created_at: string | null
          created_by: string | null
          delivered_amount: number | null
          documentation_complete: boolean | null
          expected_delivery_date: string | null
          id: string
          interaction_type: string
          issue_reported: boolean | null
          issue_resolution_time_hours: number | null
          issue_resolved: boolean | null
          notes: string | null
          on_time_delivery: boolean | null
          order_total_amount: number | null
          po_number: string | null
          product_condition: string | null
          status: string
          temperature_compliant: boolean | null
          updated_at: string | null
          vendor_id: string
        }
        Insert: {
          actual_delivery_date?: string | null
          completion_rate?: number | null
          created_at?: string | null
          created_by?: string | null
          delivered_amount?: number | null
          documentation_complete?: boolean | null
          expected_delivery_date?: string | null
          id?: string
          interaction_type: string
          issue_reported?: boolean | null
          issue_resolution_time_hours?: number | null
          issue_resolved?: boolean | null
          notes?: string | null
          on_time_delivery?: boolean | null
          order_total_amount?: number | null
          po_number?: string | null
          product_condition?: string | null
          status?: string
          temperature_compliant?: boolean | null
          updated_at?: string | null
          vendor_id: string
        }
        Update: {
          actual_delivery_date?: string | null
          completion_rate?: number | null
          created_at?: string | null
          created_by?: string | null
          delivered_amount?: number | null
          documentation_complete?: boolean | null
          expected_delivery_date?: string | null
          id?: string
          interaction_type?: string
          issue_reported?: boolean | null
          issue_resolution_time_hours?: number | null
          issue_resolved?: boolean | null
          notes?: string | null
          on_time_delivery?: boolean | null
          order_total_amount?: number | null
          po_number?: string | null
          product_condition?: string | null
          status?: string
          temperature_compliant?: boolean | null
          updated_at?: string | null
          vendor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "vendor_interactions_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "vendor_interactions_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      vendor_metrics: {
        Row: {
          average_communication_score: number | null
          average_delivery_score: number | null
          average_order_value: number | null
          average_overall_satisfaction: number | null
          average_quality_score: number | null
          average_resolution_time_hours: number | null
          calculated_at: string | null
          calculation_period: string
          completed_deliveries: number | null
          completion_rate: number | null
          documentation_completeness_rate: number | null
          failed_deliveries: number | null
          id: string
          issue_resolution_rate: number | null
          issues_resolved: number | null
          last_updated: string | null
          late_deliveries: number | null
          on_time_deliveries: number | null
          on_time_delivery_rate: number | null
          overall_letter_grade: string | null
          overall_rank: number | null
          period_end_date: string
          period_start_date: string
          temperature_compliance_rate: number | null
          total_delivered_value: number | null
          total_interactions: number | null
          total_issues_reported: number | null
          total_order_value: number | null
          total_ratings: number | null
          total_vendors_in_period: number | null
          vendor_id: string
        }
        Insert: {
          average_communication_score?: number | null
          average_delivery_score?: number | null
          average_order_value?: number | null
          average_overall_satisfaction?: number | null
          average_quality_score?: number | null
          average_resolution_time_hours?: number | null
          calculated_at?: string | null
          calculation_period: string
          completed_deliveries?: number | null
          completion_rate?: number | null
          documentation_completeness_rate?: number | null
          failed_deliveries?: number | null
          id?: string
          issue_resolution_rate?: number | null
          issues_resolved?: number | null
          last_updated?: string | null
          late_deliveries?: number | null
          on_time_deliveries?: number | null
          on_time_delivery_rate?: number | null
          overall_letter_grade?: string | null
          overall_rank?: number | null
          period_end_date: string
          period_start_date: string
          temperature_compliance_rate?: number | null
          total_delivered_value?: number | null
          total_interactions?: number | null
          total_issues_reported?: number | null
          total_order_value?: number | null
          total_ratings?: number | null
          total_vendors_in_period?: number | null
          vendor_id: string
        }
        Update: {
          average_communication_score?: number | null
          average_delivery_score?: number | null
          average_order_value?: number | null
          average_overall_satisfaction?: number | null
          average_quality_score?: number | null
          average_resolution_time_hours?: number | null
          calculated_at?: string | null
          calculation_period?: string
          completed_deliveries?: number | null
          completion_rate?: number | null
          documentation_completeness_rate?: number | null
          failed_deliveries?: number | null
          id?: string
          issue_resolution_rate?: number | null
          issues_resolved?: number | null
          last_updated?: string | null
          late_deliveries?: number | null
          on_time_deliveries?: number | null
          on_time_delivery_rate?: number | null
          overall_letter_grade?: string | null
          overall_rank?: number | null
          period_end_date?: string
          period_start_date?: string
          temperature_compliance_rate?: number | null
          total_delivered_value?: number | null
          total_interactions?: number | null
          total_issues_reported?: number | null
          total_order_value?: number | null
          total_ratings?: number | null
          total_vendors_in_period?: number | null
          vendor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "vendor_metrics_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "vendor_metrics_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      vendor_performance_alerts: {
        Row: {
          acknowledged_at: string | null
          acknowledged_by: string | null
          actual_value: number | null
          alert_type: string
          auto_generated: boolean | null
          created_at: string | null
          description: string
          id: string
          next_check_date: string | null
          recurring: boolean | null
          resolution_notes: string | null
          resolved_at: string | null
          resolved_by: string | null
          severity: string
          status: string
          threshold_value: number | null
          title: string
          triggered_by_metric: string | null
          updated_at: string | null
          vendor_id: string
        }
        Insert: {
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          actual_value?: number | null
          alert_type: string
          auto_generated?: boolean | null
          created_at?: string | null
          description: string
          id?: string
          next_check_date?: string | null
          recurring?: boolean | null
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          severity: string
          status?: string
          threshold_value?: number | null
          title: string
          triggered_by_metric?: string | null
          updated_at?: string | null
          vendor_id: string
        }
        Update: {
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          actual_value?: number | null
          alert_type?: string
          auto_generated?: boolean | null
          created_at?: string | null
          description?: string
          id?: string
          next_check_date?: string | null
          recurring?: boolean | null
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string
          status?: string
          threshold_value?: number | null
          title?: string
          triggered_by_metric?: string | null
          updated_at?: string | null
          vendor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "vendor_performance_alerts_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "vendor_performance_alerts_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      vendor_ratings: {
        Row: {
          areas_for_improvement: string | null
          communication_score: number | null
          composite_score: number | null
          created_by: string
          delivery_timeliness_score: number | null
          id: string
          issue_resolution_score: number | null
          letter_grade: string | null
          manager_comments: string | null
          overall_satisfaction: number | null
          price_competitiveness_score: number | null
          quality_score: number | null
          rating_date: string | null
          strengths: string | null
          updated_at: string | null
          vendor_id: string
          vendor_interaction_id: string
          would_recommend: boolean | null
        }
        Insert: {
          areas_for_improvement?: string | null
          communication_score?: number | null
          composite_score?: number | null
          created_by: string
          delivery_timeliness_score?: number | null
          id?: string
          issue_resolution_score?: number | null
          letter_grade?: string | null
          manager_comments?: string | null
          overall_satisfaction?: number | null
          price_competitiveness_score?: number | null
          quality_score?: number | null
          rating_date?: string | null
          strengths?: string | null
          updated_at?: string | null
          vendor_id: string
          vendor_interaction_id: string
          would_recommend?: boolean | null
        }
        Update: {
          areas_for_improvement?: string | null
          communication_score?: number | null
          composite_score?: number | null
          created_by?: string
          delivery_timeliness_score?: number | null
          id?: string
          issue_resolution_score?: number | null
          letter_grade?: string | null
          manager_comments?: string | null
          overall_satisfaction?: number | null
          price_competitiveness_score?: number | null
          quality_score?: number | null
          rating_date?: string | null
          strengths?: string | null
          updated_at?: string | null
          vendor_id?: string
          vendor_interaction_id?: string
          would_recommend?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "vendor_ratings_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendor_dashboard_summary"
            referencedColumns: ["vendor_id"]
          },
          {
            foreignKeyName: "vendor_ratings_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vendor_ratings_vendor_interaction_id_fkey"
            columns: ["vendor_interaction_id"]
            isOneToOne: true
            referencedRelation: "vendor_interactions"
            referencedColumns: ["id"]
          },
        ]
      }
      vendors: {
        Row: {
          address: string
          batches: string | null
          certifications: string[] | null
          contact_name: string
          created_at: string
          credit_limit: number | null
          email: string
          id: string
          last_order_date: string | null
          metadata: Json | null
          name: string
          notes: string | null
          payment_terms: string | null
          phone: string
          rating: number | null
          specialties: string[] | null
          status: string
          updated_at: string
        }
        Insert: {
          address: string
          batches?: string | null
          certifications?: string[] | null
          contact_name: string
          created_at?: string
          credit_limit?: number | null
          email: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name: string
          notes?: string | null
          payment_terms?: string | null
          phone: string
          rating?: number | null
          specialties?: string[] | null
          status: string
          updated_at?: string
        }
        Update: {
          address?: string
          batches?: string | null
          certifications?: string[] | null
          contact_name?: string
          created_at?: string
          credit_limit?: number | null
          email?: string
          id?: string
          last_order_date?: string | null
          metadata?: Json | null
          name?: string
          notes?: string | null
          payment_terms?: string | null
          phone?: string
          rating?: number | null
          specialties?: string[] | null
          status?: string
          updated_at?: string
        }
        Relationships: []
      }
      verification_activities: {
        Row: {
          action_required: boolean
          activity_description: string
          ccp_id: string | null
          created_at: string | null
          findings: string | null
          follow_up_actions: string[] | null
          id: string
          last_performed: string | null
          next_due: string | null
          performed_by: string | null
          results: Json | null
          scheduled_frequency: string
          status: string
          updated_at: string | null
          verification_type: string
        }
        Insert: {
          action_required?: boolean
          activity_description: string
          ccp_id?: string | null
          created_at?: string | null
          findings?: string | null
          follow_up_actions?: string[] | null
          id?: string
          last_performed?: string | null
          next_due?: string | null
          performed_by?: string | null
          results?: Json | null
          scheduled_frequency: string
          status?: string
          updated_at?: string | null
          verification_type: string
        }
        Update: {
          action_required?: boolean
          activity_description?: string
          ccp_id?: string | null
          created_at?: string | null
          findings?: string | null
          follow_up_actions?: string[] | null
          id?: string
          last_performed?: string | null
          next_due?: string | null
          performed_by?: string | null
          results?: Json | null
          scheduled_frequency?: string
          status?: string
          updated_at?: string | null
          verification_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "verification_activities_ccp_id_fkey"
            columns: ["ccp_id"]
            isOneToOne: false
            referencedRelation: "critical_control_points"
            referencedColumns: ["id"]
          },
        ]
      }
      vessels: {
        Row: {
          call_sign: string | null
          created_at: string | null
          fishing_authorization: string[] | null
          flag_country: string
          gear_type: string | null
          gross_tonnage: number | null
          id: string
          imo_number: string | null
          is_active: boolean
          length_meters: number | null
          metadata: Json | null
          operator_partner_id: string | null
          owner_partner_id: string | null
          updated_at: string | null
          vessel_name: string
          vessel_registration: string | null
          vessel_type: string | null
        }
        Insert: {
          call_sign?: string | null
          created_at?: string | null
          fishing_authorization?: string[] | null
          flag_country: string
          gear_type?: string | null
          gross_tonnage?: number | null
          id?: string
          imo_number?: string | null
          is_active?: boolean
          length_meters?: number | null
          metadata?: Json | null
          operator_partner_id?: string | null
          owner_partner_id?: string | null
          updated_at?: string | null
          vessel_name: string
          vessel_registration?: string | null
          vessel_type?: string | null
        }
        Update: {
          call_sign?: string | null
          created_at?: string | null
          fishing_authorization?: string[] | null
          flag_country?: string
          gear_type?: string | null
          gross_tonnage?: number | null
          id?: string
          imo_number?: string | null
          is_active?: boolean
          length_meters?: number | null
          metadata?: Json | null
          operator_partner_id?: string | null
          owner_partner_id?: string | null
          updated_at?: string | null
          vessel_name?: string
          vessel_registration?: string | null
          vessel_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "vessels_operator_partner_id_fkey"
            columns: ["operator_partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vessels_owner_partner_id_fkey"
            columns: ["owner_partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      fsma_traceability_view: {
        Row: {
          actor_location_id: string | null
          actor_partner_id: string | null
          event_id: string | null
          event_time: string | null
          event_type: string | null
          lot_id: string | null
          notes: string | null
          product_id: string | null
          product_name: string | null
          qty: number | null
          reference_doc: string | null
          role: string | null
          tlc: string | null
          transporter_partner_id: string | null
          uom: string | null
        }
        Relationships: [
          {
            foreignKeyName: "traceability_events_actor_location_id_fkey"
            columns: ["actor_location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "traceability_events_actor_partner_id_fkey"
            columns: ["actor_partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "traceability_events_transporter_partner_id_fkey"
            columns: ["transporter_partner_id"]
            isOneToOne: false
            referencedRelation: "partners"
            referencedColumns: ["id"]
          },
        ]
      }
      gdst_traceability_export: {
        Row: {
          actor_gln: string | null
          actor_name: string | null
          catch_method: string | null
          certifications: Json | null
          event_id: string | null
          event_quantity: number | null
          event_time: string | null
          event_type: string | null
          event_unit: string | null
          fao_area: string | null
          fishing_area_name: string | null
          flag_country: string | null
          gdst_kde_version: string | null
          gear_type: string | null
          gtin: string | null
          harvest_or_prod_date: string | null
          landing_date: string | null
          location_gln: string | null
          location_name: string | null
          lot_id: string | null
          lot_role: string | null
          origin_country: string | null
          product_fao_area: string | null
          product_id: string | null
          product_name: string | null
          production_method: string | null
          quantity: number | null
          record_created_at: string | null
          reference_doc: string | null
          scientific_name: string | null
          species_fao_code: string | null
          species_scientific_name: string | null
          temperature_data: Json | null
          traceability_lot_code: string | null
          transporter_name: string | null
          unit_of_measure: string | null
          vessel_name: string | null
          vessel_registration: string | null
        }
        Relationships: []
      }
      vendor_dashboard_summary: {
        Row: {
          active_alerts_count: number | null
          average_quality_score: number | null
          completion_rate: number | null
          compliance_status: string | null
          contact_name: string | null
          critical_alerts_count: number | null
          email: string | null
          haccp_cert_expiry_date: string | null
          last_delivery_date: string | null
          last_delivery_status: string | null
          latest_compliance_score: number | null
          metrics_updated_at: string | null
          on_time_delivery_rate: number | null
          overall_letter_grade: string | null
          overall_rank: number | null
          phone: string | null
          total_interactions: number | null
          vendor_id: string | null
          vendor_name: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      add_lookup_data: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      calculate_performance_grade: {
        Args: {
          avg_overall: number
          avg_quality: number
          completion_rate: number
          on_time_rate: number
        }
        Returns: string
      }
      calculate_vendor_metrics: {
        Args:
          | {
              input_period_type?: string
              input_reference_date?: string
              input_vendor_id: string
            }
          | { p_calculation_period?: string; p_vendor_id?: string }
        Returns: undefined
      }
      check_vendor_performance_alerts: {
        Args:
          | { input_vendor_id: string; interaction_id?: string }
          | { p_vendor_id?: string }
        Returns: undefined
      }
      cleanup_old_vendor_metrics: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      create_compliance_alert: {
        Args: {
          p_alert_type: string
          p_description: string
          p_metadata?: Json
          p_severity: string
          p_source_id: string
          p_source_table: string
          p_title: string
        }
        Returns: string
      }
      create_sales_tables: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_tables: {
        Args: { schema: string; table_name: string }
        Returns: undefined
      }
      exec_sql: {
        Args: { command: string }
        Returns: Json
      }
      gbt_bit_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bool_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bool_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bpchar_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_bytea_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_cash_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_cash_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_date_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_date_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_enum_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_enum_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float4_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float4_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_float8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_inet_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int2_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int2_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int4_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int4_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_int8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_intv_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad8_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_macad8_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_numeric_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_oid_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_oid_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_text_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_time_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_time_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_timetz_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_ts_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_ts_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_tstz_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_uuid_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_uuid_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_var_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbt_var_fetch: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey_var_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey_var_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey16_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey16_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey2_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey2_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey32_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey32_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey4_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey4_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey8_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gbtreekey8_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      get_columns_for_table: {
        Args: { p_table_name: string }
        Returns: {
          column_name: unknown
          data_type: unknown
        }[]
      }
      get_database_schema: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_period_bounds: {
        Args: { period_type: string; reference_date?: string }
        Returns: {
          period_end: string
          period_start: string
        }[]
      }
      get_public_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: unknown
        }[]
      }
      get_vendor_dashboard_summary: {
        Args: Record<PropertyKey, never>
        Returns: {
          active_alerts_count: number | null
          average_quality_score: number | null
          completion_rate: number | null
          compliance_status: string | null
          contact_name: string | null
          critical_alerts_count: number | null
          email: string | null
          haccp_cert_expiry_date: string | null
          last_delivery_date: string | null
          last_delivery_status: string | null
          latest_compliance_score: number | null
          metrics_updated_at: string | null
          on_time_delivery_rate: number | null
          overall_letter_grade: string | null
          overall_rank: number | null
          phone: string | null
          total_interactions: number | null
          vendor_id: string | null
          vendor_name: string | null
        }[]
      }
      gtrgm_compress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_decompress: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_in: {
        Args: { "": unknown }
        Returns: unknown
      }
      gtrgm_options: {
        Args: { "": unknown }
        Returns: undefined
      }
      gtrgm_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      recalculate_all_vendor_metrics: {
        Args: { input_period_type?: string }
        Returns: {
          status: string
          vendor_id: string
        }[]
      }
      refresh_vendor_dashboard_summary: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      set_limit: {
        Args: { "": number }
        Returns: number
      }
      setup_database: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      show_limit: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      show_trgm: {
        Args: { "": string }
        Returns: string[]
      }
      sync_inventory_to_platform: {
        Args: { platform_id: string; product_ids: string[] }
        Returns: undefined
      }
      unaccent: {
        Args: { "": string }
        Returns: string
      }
      unaccent_init: {
        Args: { "": unknown }
        Returns: unknown
      }
      update_vendor_rankings: {
        Args: { input_period_type?: string }
        Returns: undefined
      }
      validate_gdst_kdes: {
        Args: { event_id: string }
        Returns: Json
      }
      view_database_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          row_count: number
          table_name: string
        }[]
      }
    }
    Enums: {
      condition_type: "fresh" | "frozen" | "other"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      condition_type: ["fresh", "frozen", "other"],
    },
  },
} as const
