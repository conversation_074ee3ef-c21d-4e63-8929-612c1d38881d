import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, CheckCircle, Clock, Database, User } from 'lucide-react';
import { useSQLiteStaging } from '@/hooks/use-sqlite-staging';
import { ConfirmationQueueItem } from '@/lib/sqlite-service';

// Helper function to safely render unknown data values
const renderValue = (value: unknown): string => {
  if (value === null || value === undefined) return 'N/A';
  if (typeof value === 'string') return value;
  if (typeof value === 'number') return value.toString();
  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
  return String(value);
};

interface StagingConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function StagingConfirmationDialog({ open, onOpenChange }: StagingConfirmationDialogProps) {
  const { getPendingConfirmations, confirmAndSync, getStats } = useSQLiteStaging();
  const [pendingItems, setPendingItems] = useState<ConfirmationQueueItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<ConfirmationQueueItem | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [stats, setStats] = useState<{
    products: number;
    inventory: number;
    ccp_monitoring: number;
    temperature_readings: number;
    quality_tests: number;
    allergen_tests: number;
    haccp_events: number;
    pending_sync: number;
    pending_confirmations: number;
  } | null>(null);

  useEffect(() => {
    if (open) {
      loadPendingConfirmations();
      loadStats();
    }
  }, [open]);

  const loadPendingConfirmations = async () => {
    try {
      const items = await getPendingConfirmations();
      setPendingItems(items);
    } catch (error) {
      console.error('Failed to load pending confirmations:', error);
    }
  };

  const loadStats = async () => {
    try {
      const statistics = await getStats();
      setStats(statistics);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleConfirm = async (item: ConfirmationQueueItem) => {
    setIsProcessing(true);
    try {
      const confirmedBy = 'current_user'; // In a real app, get from auth context
      const success = await confirmAndSync(item.id!, confirmedBy);

      if (success) {
        // Remove from pending items
        setPendingItems(prev => prev.filter(p => p.id !== item.id));
        setSelectedItem(null);
      } else {
        alert('Failed to confirm and sync item. Please try again.');
      }
    } catch (error) {
      console.error('Error confirming item:', error);
      alert('An error occurred while confirming the item.');
    } finally {
      setIsProcessing(false);
    }
  };

  const getRecordTypeLabel = (recordType: string): string => {
    const typeMap: Record<string, string> = {
      staged_products: 'Product',
      staged_inventory: 'Inventory',
      staged_ccp_monitoring: 'CCP Monitoring',
      staged_temperature_readings: 'Temperature Reading',
      staged_quality_tests: 'Quality Test',
      staged_allergen_tests: 'Allergen Test',
      staged_haccp_events: 'HACCP Event',
    };
    return typeMap[recordType] || recordType;
  };

  const getSeverityBadge = (recordType: string, data: Record<string, unknown>) => {
    // Determine severity based on record type and data
    if (recordType === 'staged_ccp_monitoring') {
      return data.corrective_action_needed ?
        <Badge variant="destructive">Action Needed</Badge> :
        <Badge variant="secondary">Normal</Badge>;
    }

    if (recordType === 'staged_temperature_readings') {
      return data.within_safe_range ?
        <Badge variant="secondary">Safe</Badge> :
        <Badge variant="destructive">Violation</Badge>;
    }

    if (recordType === 'staged_haccp_events') {
      const severity = String(data.severity || '').toLowerCase();
      if (severity === 'critical') return <Badge variant="destructive">Critical</Badge>;
      if (severity === 'major') return <Badge variant="destructive">Major</Badge>;
      if (severity === 'minor') return <Badge variant="secondary">Minor</Badge>;
    }

    return <Badge variant="outline">Standard</Badge>;
  };

  const renderRecordDetails = (item: ConfirmationQueueItem) => {
    const { record_type, data } = item;

    switch (record_type) {
      case 'staged_products':
        return (
          <div className="space-y-2">
            <div><strong>Name:</strong> {renderValue(data.name)}</div>
            <div><strong>Category:</strong> {renderValue(data.category)}</div>
            <div><strong>Supplier:</strong> {renderValue(data.supplier_id)}</div>
            <div><strong>Unit Price:</strong> ${renderValue(data.unit_price)}</div>
            <div><strong>Storage Temp:</strong> {renderValue(data.storage_temp_min)}°C - {renderValue(data.storage_temp_max)}°C</div>
          </div>
        );

      case 'staged_inventory':
        return (
          <div className="space-y-2">
            <div><strong>Product:</strong> {renderValue(data.product_id)}</div>
            <div><strong>Batch:</strong> {renderValue(data.batch_number)}</div>
            <div><strong>Quantity:</strong> {renderValue(data.quantity)} {renderValue(data.unit)}</div>
            <div><strong>Expiry:</strong> {renderValue(data.expiry_date)}</div>
            <div><strong>Quality Status:</strong> {renderValue(data.quality_status)}</div>
            <div><strong>Temperature:</strong> {renderValue(data.temperature)}°C</div>
          </div>
        );

      case 'staged_ccp_monitoring':
        return (
          <div className="space-y-2">
            <div><strong>CCP Name:</strong> {renderValue(data.ccp_name)}</div>
            <div><strong>Product:</strong> {renderValue(data.product_id)}</div>
            <div><strong>Measurement:</strong> {renderValue(data.measurement_value)} {renderValue(data.measurement_unit)}</div>
            <div><strong>Limits:</strong> {renderValue(data.critical_limit_min)} - {renderValue(data.critical_limit_max)}</div>
            <div><strong>Within Limits:</strong> {data.is_within_limits ? 'Yes' : 'No'}</div>
            <div><strong>Monitored By:</strong> {renderValue(data.monitored_by)}</div>
            {data.observations && <div><strong>Observations:</strong> {renderValue(data.observations)}</div>}
          </div>
        );

      case 'staged_temperature_readings':
        return (
          <div className="space-y-2">
            <div><strong>Sensor:</strong> {renderValue(data.sensor_id)}</div>
            <div><strong>Temperature:</strong> {renderValue(data.temp_celsius)}°C / {renderValue(data.temp_fahrenheit)}°F</div>
            <div><strong>Humidity:</strong> {renderValue(data.humidity)}%</div>
            <div><strong>Recorded At:</strong> {data.recorded_at ? new Date(String(data.recorded_at)).toLocaleString() : 'N/A'}</div>
            <div><strong>Within Safe Range:</strong> {data.within_safe_range ? 'Yes' : 'No'}</div>
            {data.violation_type && <div><strong>Violation:</strong> {renderValue(data.violation_type)}</div>}
          </div>
        );

      case 'staged_haccp_events':
        return (
          <div className="space-y-2">
            <div><strong>Event Type:</strong> {renderValue(data.event_type)}</div>
            <div><strong>Description:</strong> {renderValue(data.description)}</div>
            <div><strong>Severity:</strong> {renderValue(data.severity)}</div>
            <div><strong>Reported By:</strong> {renderValue(data.reported_by)}</div>
            <div><strong>Date/Time:</strong> {data.event_datetime ? new Date(String(data.event_datetime)).toLocaleString() : 'N/A'}</div>
            {data.immediate_action && <div><strong>Immediate Action:</strong> {renderValue(data.immediate_action)}</div>}
            {data.root_cause && <div><strong>Root Cause:</strong> {renderValue(data.root_cause)}</div>}
          </div>
        );

      default:
        return <div className="text-sm text-gray-600">Record details not available</div>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Staging Confirmation
          </DialogTitle>
          <DialogDescription>
            Review and confirm staged data before syncing to the main database.
          </DialogDescription>
        </DialogHeader>

        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">Pending Sync</p>
                    <p className="text-2xl font-bold">{stats.pending_sync}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-orange-500" />
                  <div>
                    <p className="text-sm font-medium">Need Confirmation</p>
                    <p className="text-2xl font-bold">{stats.pending_confirmations}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-sm font-medium">Products</p>
                    <p className="text-2xl font-bold">{stats.products}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-purple-500" />
                  <div>
                    <p className="text-sm font-medium">Inventory Items</p>
                    <p className="text-2xl font-bold">{stats.inventory}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs defaultValue="pending" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="pending">
              Pending Confirmations ({pendingItems.length})
            </TabsTrigger>
            <TabsTrigger value="details">
              Record Details
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending" className="space-y-4">
            {pendingItems.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <p>No items pending confirmation</p>
              </div>
            ) : (
              <div className="space-y-3">
                {pendingItems.map((item) => (
                  <Card key={item.id} className="cursor-pointer hover:bg-gray-50"
                        onClick={() => setSelectedItem(item)}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">
                              {getRecordTypeLabel(item.record_type)}
                            </Badge>
                            {getSeverityBadge(item.record_type, item.data)}
                          </div>
                          <p className="font-medium">
                            {renderValue(item.data.name) || renderValue(item.data.batch_number) || renderValue(item.data.description) || 'Unnamed Record'}
                          </p>
                          <p className="text-sm text-gray-600">
                            Created: {new Date(item.created_at!).toLocaleString()}
                          </p>
                        </div>
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleConfirm(item);
                          }}
                          disabled={isProcessing}
                        >
                          Confirm & Sync
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            {selectedItem ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    {getRecordTypeLabel(selectedItem.record_type)} Details
                  </CardTitle>
                  <CardDescription>
                    Review the details below before confirming
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {renderRecordDetails(selectedItem)}
                  <div className="mt-4 pt-4 border-t">
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handleConfirm(selectedItem)}
                        disabled={isProcessing}
                        className="flex-1"
                      >
                        {isProcessing ? 'Processing...' : 'Confirm & Sync to Database'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setSelectedItem(null)}
                        disabled={isProcessing}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Select an item from the pending confirmations to view details</p>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}