import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Fish, Calendar, Thermometer, Scale, AlertTriangle, Settings, Plus, Database } from "lucide-react";
import { useSQLiteStaging } from "@/hooks/use-sqlite-staging";
import { StagingConfirmationDialog } from "./StagingConfirmationDialog";
import { sqliteService } from "@/lib/sqlite-service";

export type EventType = "receiving" | "sales" | "disposal" | "re-sealing";

interface HaccpEvent {
  eventType: EventType;
  date: string;
  time: string;
  temperature: string;
  weight: string;
  weightUnit?: string;
  species: string;
  supplier: string;
  batchNumber: string;
  notes: string;
  // Event-specific fields
  salePrice?: string;
  customer?: string;
  disposalReason?: string;
  resealingReason?: string;
  newPackagingDate?: string;
}

interface WebhookConfig {
  googleSheetUrl: string;
  databaseUrl: string;
  n8nUrl: string;
}

const HaccpEventForm = () => {
  const { toast } = useToast();
  const { createHACCPEvent, getAllProducts } = useSQLiteStaging();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showWebhookConfig, setShowWebhookConfig] = useState(false);
  const [showStagingDialog, setShowStagingDialog] = useState(false);
  
  // Database data states
  interface Product {
    id: string;
    name: string;
    category?: string;
    sub_category?: string;
    scientific_name?: string;
  }
  
  interface Customer {
    id: string;
    name: string;
    email?: string;
  }
  
  interface Partner {
    id: string;
    name: string;
    type: string;
  }
  
  const [products, setProducts] = useState<Product[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [batches, setBatches] = useState<unknown[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Modal states
  const [showProductModal, setShowProductModal] = useState(false);
  const [showSupplierModal, setShowSupplierModal] = useState(false);
  
  // New item forms
  const [newProduct, setNewProduct] = useState({
    name: "",
    scientific_name: "",
    category: "",
    sub_category: "",
  });
  const [newSupplier, setNewSupplier] = useState({
    name: "",
    contact: { email: "", phone: "" },
  });
  
  const [event, setEvent] = useState<HaccpEvent>({
    eventType: "receiving",
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5),
    temperature: "",
    weight: "",
    weightUnit: "lbs",
    species: "",
    supplier: "",
    batchNumber: `BATCH-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
    notes: "",
  });

  const [webhooks, setWebhooks] = useState<WebhookConfig>({
    googleSheetUrl: localStorage.getItem('haccp-google-sheet-webhook') || '',
    databaseUrl: localStorage.getItem('haccp-database-webhook') || '',
    n8nUrl: localStorage.getItem('haccp-n8n-webhook') || '',
  });

  // Generate new batch number
  const generateBatchNumber = () => {
    const now = new Date();
    const dateStr = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`;
    const randomStr = Math.random().toString(36).substr(2, 6).toUpperCase();
    return `BATCH-${dateStr}-${randomStr}`;
  };

  // Fetch data from SQLite
  useEffect(() => {
    const fetchData = async () => {
      try {
        // For now, use the SQLite staging data. In a real app, you might have a separate products table
        const productsData = await sqliteService.getAllProducts();
        
        // Mock customers and suppliers data since these tables don't exist in SQLite schema yet
        const mockCustomers = [
          { id: '1', name: 'Restaurant A', email: '<EMAIL>' },
          { id: '2', name: 'Grocery Chain B', email: '<EMAIL>' },
          { id: '3', name: 'Hotel C', email: '<EMAIL>' }
        ];
        
        const mockSuppliers = [
          { id: '1', name: 'Pacific Seafood Co.', type: 'supplier' },
          { id: '2', name: 'Ocean Harvest Inc.', type: 'supplier' },
          { id: '3', name: 'Bay Fresh Fish', type: 'supplier' }
        ];

        setProducts(productsData.map(p => ({
          id: p.id,
          name: p.name,
          category: p.category,
          sub_category: p.subcategory,
          scientific_name: p.name // Using name as fallback for scientific name
        })));
        setCustomers(mockCustomers);
        setPartners(mockSuppliers);
      } catch (error) {
        console.error('Error loading data:', error);
        toast({
          title: "Error loading data",
          description: "Failed to load dropdown options from database.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  const eventTypes = [
    { value: "receiving", label: "Receiving", icon: "📦", description: "Product arrival and inspection" },
    { value: "sales", label: "Sales", icon: "🛒", description: "Product sale and distribution" },
    { value: "disposal", label: "Disposal", icon: "🗑️", description: "Product waste disposal" },
    { value: "re-sealing", label: "Re-sealing", icon: "📋", description: "Product repackaging" },
  ];

  const handleInputChange = (field: keyof HaccpEvent, value: string) => {
    setEvent(prev => ({ ...prev, [field]: value }));
  };

  // Add new product
  const handleAddProduct = async () => {
    if (!newProduct.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Product name is required.",
        variant: "destructive",
      });
      return;
    }

    try {
      const productId = await sqliteService.createProduct({
        name: newProduct.name,
        category: newProduct.category || undefined,
        subcategory: newProduct.sub_category || undefined,
        created_by: 'current_user' // In a real app, get from auth context
      });

      const newProductData = {
        id: productId,
        name: newProduct.name,
        category: newProduct.category,
        sub_category: newProduct.sub_category,
        scientific_name: newProduct.scientific_name
      };

      setProducts(prev => [...prev, newProductData]);
      setEvent(prev => ({ ...prev, species: newProductData.name }));
      setNewProduct({ name: "", scientific_name: "", category: "", sub_category: "" });
      setShowProductModal(false);
      
      toast({
        title: "Success",
        description: "Product added successfully.",
      });
    } catch (error) {
      console.error('Error adding product:', error);
      toast({
        title: "Error",
        description: "Failed to add product.",
        variant: "destructive",
      });
    }
  };

  // Add new supplier
  const handleAddSupplier = async () => {
    if (!newSupplier.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Supplier name is required.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Since we don't have a suppliers table in SQLite schema yet, just add to local state
      const newSupplierData = {
        id: Date.now().toString(), // Simple ID generation for mock data
        name: newSupplier.name,
        type: 'supplier'
      };

      setPartners(prev => [...prev, newSupplierData]);
      setEvent(prev => ({ ...prev, supplier: newSupplierData.name }));
      setNewSupplier({ name: "", contact: { email: "", phone: "" } });
      setShowSupplierModal(false);
      
      toast({
        title: "Success",
        description: "Supplier added successfully.",
      });
    } catch (error) {
      console.error('Error adding supplier:', error);
      toast({
        title: "Error",
        description: "Failed to add supplier.",
        variant: "destructive",
      });
    }
  };

  const handleWebhookChange = (field: keyof WebhookConfig, value: string) => {
    const newWebhooks = { ...webhooks, [field]: value };
    setWebhooks(newWebhooks);
    localStorage.setItem(`haccp-${field.replace('Url', '')}-webhook`, value);
  };

  const sendWebhook = async (url: string, data: Record<string, unknown>, type: string) => {
    if (!url.trim()) return false;
    
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        mode: "no-cors",
        body: JSON.stringify({
          ...data,
          timestamp: new Date().toISOString(),
          source: "HACCP Event Form",
        }),
      });
      return true;
    } catch (error) {
      console.error(`Error sending ${type} webhook:`, error);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!event.eventType || !event.species || !event.temperature) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Create HACCP event in SQLite staging
      const stagedEventId = await createHACCPEvent({
        event_type: event.eventType,
        event_datetime: `${event.date}T${event.time}:00.000Z`,
        description: `${event.eventType} event - ${event.species} (${event.batchNumber})`,
        severity: event.temperature && parseFloat(event.temperature) > 45 ? 'major' : 'minor',
        reported_by: 'current_user', // In a real app, get from auth context
        product_id: event.species,
        batch_number: event.batchNumber,
        status: 'pending',
        immediate_action: event.notes || undefined,
      });

      toast({
        title: "Event Staged Successfully",
        description: `${event.eventType} event has been saved to staging database. Review and confirm to sync with main database.`,
        variant: "default",
      });

      // Show staging confirmation dialog
      setShowStagingDialog(true);

      // Reset form
      setEvent({
        eventType: "receiving",
        date: new Date().toISOString().split('T')[0],
        time: new Date().toTimeString().slice(0, 5),
        temperature: "",
        weight: "",
        weightUnit: "lbs",
        species: "",
        supplier: "",
        batchNumber: generateBatchNumber(),
        notes: "",
      });

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to stage event. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderEventSpecificFields = () => {
    switch (event.eventType) {
      case "sales":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="customer">Customer *</Label>
              <Select value={event.customer || ""} onValueChange={(value) => handleInputChange("customer", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.name}>
                      {customer.name} {customer.email && `(${customer.email})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="salePrice">Sale Price</Label>
              <Input
                id="salePrice"
                type="number"
                step="0.01"
                value={event.salePrice || ""}
                onChange={(e) => handleInputChange("salePrice", e.target.value)}
                placeholder="0.00"
              />
            </div>
          </div>
        );
      case "disposal":
        return (
          <div className="space-y-2">
            <Label htmlFor="disposalReason">Disposal Reason *</Label>
            <Select value={event.disposalReason || ""} onValueChange={(value) => handleInputChange("disposalReason", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select disposal reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="expired">Expired</SelectItem>
                <SelectItem value="damaged">Damaged</SelectItem>
                <SelectItem value="contaminated">Contaminated</SelectItem>
                <SelectItem value="temperature-abuse">Temperature Abuse</SelectItem>
                <SelectItem value="quality-issue">Quality Issue</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      case "re-sealing":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="resealingReason">Re-sealing Reason *</Label>
              <Select value={event.resealingReason || ""} onValueChange={(value) => handleInputChange("resealingReason", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="damaged-packaging">Damaged Packaging</SelectItem>
                  <SelectItem value="size-change">Size Change</SelectItem>
                  <SelectItem value="rebranding">Rebranding</SelectItem>
                  <SelectItem value="bulk-breakdown">Bulk Breakdown</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="newPackagingDate">New Packaging Date</Label>
              <Input
                id="newPackagingDate"
                type="date"
                value={event.newPackagingDate || ""}
                onChange={(e) => handleInputChange("newPackagingDate", e.target.value)}
              />
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <Fish className="h-8 w-8 text-primary" />
            <h1 className="text-3xl font-bold text-foreground">HACCP Event Tracking</h1>
          </div>
          <p className="text-muted-foreground">Record and monitor critical control points for seafood safety compliance</p>

          {/* Staging Status */}
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowStagingDialog(true)}
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              Data Staging
            </Button>
          </div>
        </div>

        {/* Webhook Configuration */}
        <Card className="shadow-card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-primary" />
                <CardTitle>Webhook Configuration</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowWebhookConfig(!showWebhookConfig)}
              >
                {showWebhookConfig ? "Hide" : "Configure"}
              </Button>
            </div>
          </CardHeader>
          {showWebhookConfig && (
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="googleSheetUrl">Google Sheet Webhook</Label>
                  <Input
                    id="googleSheetUrl"
                    value={webhooks.googleSheetUrl}
                    onChange={(e) => handleWebhookChange("googleSheetUrl", e.target.value)}
                    placeholder="https://hooks.zapier.com/..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="databaseUrl">Database Webhook</Label>
                  <Input
                    id="databaseUrl"
                    value={webhooks.databaseUrl}
                    onChange={(e) => handleWebhookChange("databaseUrl", e.target.value)}
                    placeholder="https://your-database-webhook..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="n8nUrl">n8n Workflow Webhook</Label>
                  <Input
                    id="n8nUrl"
                    value={webhooks.n8nUrl}
                    onChange={(e) => handleWebhookChange("n8nUrl", e.target.value)}
                    placeholder="https://your-n8n-webhook..."
                  />
                </div>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertTriangle className="h-4 w-4" />
                <span>Webhook URLs are stored locally in your browser</span>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Main Form */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              HACCP Event Entry
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Event Type Selection */}
              <div className="space-y-3">
                <Label>Event Type *</Label>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                  {eventTypes.map((type) => (
                    <div
                      key={type.value}
                      className={`cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 hover:shadow-md ${
                        event.eventType === type.value
                          ? "border-primary bg-primary/5 shadow-ocean"
                          : "border-border hover:border-primary/50"
                      }`}
                      onClick={() => handleInputChange("eventType", type.value as EventType)}
                    >
                      <div className="text-center space-y-2">
                        <div className="text-2xl">{type.icon}</div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-xs text-muted-foreground">{type.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">Date *</Label>
                  <Input
                    id="date"
                    type="date"
                    value={event.date}
                    onChange={(e) => handleInputChange("date", e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="time">Time *</Label>
                  <Input
                    id="time"
                    type="time"
                    value={event.time}
                    onChange={(e) => handleInputChange("time", e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="batchNumber">Batch Number</Label>
                  <div className="flex gap-2">
                    <Input
                      id="batchNumber"
                      value={event.batchNumber}
                      onChange={(e) => handleInputChange("batchNumber", e.target.value)}
                      placeholder="Auto-generated batch number"
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => handleInputChange("batchNumber", generateBatchNumber())}
                    >
                      Generate New
                    </Button>
                  </div>
                </div>
              </div>

              {/* Product Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="species">Product *</Label>
                  <div className="flex gap-2">
                    <Select value={event.species} onValueChange={(value) => handleInputChange("species", value)} disabled={loading}>
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder={loading ? "Loading products..." : "Select product"} />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product.id} value={product.name}>
                            <div className="flex flex-col">
                              <span>{product.name}</span>
                              {product.scientific_name && (
                                <span className="text-xs text-muted-foreground">{product.scientific_name}</span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Dialog open={showProductModal} onOpenChange={setShowProductModal}>
                      <DialogTrigger asChild>
                        <Button type="button" variant="outline" size="icon">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add New Product</DialogTitle>
                          <DialogDescription>
                            Set basic attributes so the product is available for event selection.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="productName">Product Name *</Label>
                            <Input
                              id="productName"
                              value={newProduct.name}
                              onChange={(e) => setNewProduct(prev => ({ ...prev, name: e.target.value }))}
                              placeholder="Enter product name"
                            />
                          </div>
                          <div>
                            <Label htmlFor="scientificName">Scientific Name</Label>
                            <Input
                              id="scientificName"
                              value={newProduct.scientific_name}
                              onChange={(e) => setNewProduct(prev => ({ ...prev, scientific_name: e.target.value }))}
                              placeholder="Enter scientific name"
                            />
                          </div>
                          <div>
                            <Label htmlFor="productCategory">Category</Label>
                            <Input
                              id="productCategory"
                              value={newProduct.category}
                              onChange={(e) => setNewProduct(prev => ({ ...prev, category: e.target.value }))}
                              placeholder="Enter category"
                            />
                          </div>
                          <div>
                            <Label htmlFor="productSubCategory">Sub Category</Label>
                            <Input
                              id="productSubCategory"
                              value={newProduct.sub_category}
                              onChange={(e) => setNewProduct(prev => ({ ...prev, sub_category: e.target.value }))}
                              placeholder="Enter sub category"
                            />
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button type="button" variant="outline" onClick={() => setShowProductModal(false)}>
                              Cancel
                            </Button>
                            <Button type="button" onClick={handleAddProduct}>
                              Add Product
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weight" className="flex items-center gap-1">
                    <Scale className="h-4 w-4" />
                    Weight/Quantity
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="weight"
                      type="number"
                      step="0.1"
                      value={event.weight}
                      onChange={(e) => handleInputChange("weight", e.target.value)}
                      placeholder="0.0"
                      className="flex-1"
                    />
                    <Select value={event.weightUnit || "lbs"} onValueChange={(value) => handleInputChange("weightUnit", value)}>
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="lbs">lbs</SelectItem>
                        <SelectItem value="pcs">pcs</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="temperature" className="flex items-center gap-1">
                    <Thermometer className="h-4 w-4" />
                    Temperature (°F) *
                  </Label>
                  <Input
                    id="temperature"
                    type="number"
                    step="0.1"
                    value={event.temperature}
                    onChange={(e) => handleInputChange("temperature", e.target.value)}
                    placeholder="32.0"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplier">Supplier/Source</Label>
                <div className="flex gap-2">
                  <Select value={event.supplier} onValueChange={(value) => handleInputChange("supplier", value)} disabled={loading}>
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder={loading ? "Loading suppliers..." : "Select supplier"} />
                    </SelectTrigger>
                    <SelectContent>
                      {partners.map((partner) => (
                        <SelectItem key={partner.id} value={partner.name}>
                          {partner.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Dialog open={showSupplierModal} onOpenChange={setShowSupplierModal}>
                    <DialogTrigger asChild>
                      <Button type="button" variant="outline" size="icon">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New Supplier</DialogTitle>
                        <DialogDescription>
                          Capture supplier contact details for future HACCP submissions.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="supplierName">Supplier Name *</Label>
                          <Input
                            id="supplierName"
                            value={newSupplier.name}
                            onChange={(e) => setNewSupplier(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Enter supplier name"
                          />
                        </div>
                        <div>
                          <Label htmlFor="supplierEmail">Email</Label>
                          <Input
                            id="supplierEmail"
                            type="email"
                            value={newSupplier.contact.email}
                            onChange={(e) => setNewSupplier(prev => ({ 
                              ...prev, 
                              contact: { ...prev.contact, email: e.target.value }
                            }))}
                            placeholder="Enter email"
                          />
                        </div>
                        <div>
                          <Label htmlFor="supplierPhone">Phone</Label>
                          <Input
                            id="supplierPhone"
                            value={newSupplier.contact.phone}
                            onChange={(e) => setNewSupplier(prev => ({ 
                              ...prev, 
                              contact: { ...prev.contact, phone: e.target.value }
                            }))}
                            placeholder="Enter phone number"
                          />
                        </div>
                        <div className="flex justify-end gap-2">
                          <Button type="button" variant="outline" onClick={() => setShowSupplierModal(false)}>
                            Cancel
                          </Button>
                          <Button type="button" onClick={handleAddSupplier}>
                            Add Supplier
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              <Separator />

              {/* Event-specific fields */}
              {renderEventSpecificFields()}

              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  value={event.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Any additional observations, concerns, or notes..."
                  rows={3}
                />
              </div>

              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={() => window.location.reload()}>
                  Reset Form
                </Button>
                <Button type="submit" variant="ocean" disabled={isSubmitting}>
                  {isSubmitting ? "Recording..." : "Record Event"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Staging Confirmation Dialog */}
      <StagingConfirmationDialog
        open={showStagingDialog}
        onOpenChange={setShowStagingDialog}
      />
    </div>
  );
};

export default HaccpEventForm;