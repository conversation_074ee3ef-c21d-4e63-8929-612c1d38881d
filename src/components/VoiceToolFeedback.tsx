import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Check<PERSON>ircle, Loader2, Info, X } from 'lucide-react';
import { Too<PERSON><PERSON><PERSON><PERSON>, ToolCall } from '@/lib/voice-tool-executor';

interface VoiceToolFeedbackProps {
  toolCall: ToolCall;
  result?: ToolResult;
  isExecuting?: boolean;
  error?: string;
  onDismiss?: () => void;
}

/**
 * Format tool result data for rich display
 */
function formatToolResultData(
  toolName: string,
  data: unknown
): React.ReactNode {
  if (!data) return null;

  if (typeof data === 'object' && data !== null) {
    const obj = data as Record<string, unknown>;

    // Format specific tool results with rich structure
    switch (toolName) {
      case 'add_inventory_event':
        return (
          <div className="space-y-1 text-xs">
            <div className="font-semibold">Inventory Added</div>
            <div className="text-muted-foreground">
              ID: <span className="font-mono text-foreground">{obj.inventory_id}</span>
            </div>
          </div>
        );

      case 'update_product_location':
        return (
          <div className="space-y-1 text-xs">
            <div className="font-semibold">Location Updated</div>
            {obj.product_id && (
              <div className="text-muted-foreground">
                Product: <span className="font-mono text-foreground">{obj.product_id}</span>
              </div>
            )}
            {obj.new_location && (
              <div className="text-muted-foreground">
                Location: <span className="font-mono text-foreground">{obj.new_location}</span>
              </div>
            )}
          </div>
        );

      case 'check_product_status':
        return (
          <div className="space-y-1 text-xs">
            <div className="font-semibold">Status Retrieved</div>
            {obj.quantity !== undefined && (
              <div className="text-muted-foreground">
                Quantity: <span className="font-mono text-foreground">{obj.quantity}</span>
              </div>
            )}
            {obj.location && (
              <div className="text-muted-foreground">
                Location: <span className="font-mono text-foreground">{obj.location}</span>
              </div>
            )}
            {obj.last_updated && (
              <div className="text-muted-foreground">
                Updated: <span className="font-mono text-foreground">{obj.last_updated}</span>
              </div>
            )}
          </div>
        );

      case 'get_haccp_events': {
        const events = Array.isArray(obj) ? obj : [];
        return (
          <div className="space-y-1 text-xs">
            <div className="font-semibold">HACCP Events</div>
            <div className="text-muted-foreground">
              Count: <span className="font-mono text-foreground">{events.length}</span>
            </div>
          </div>
        );
      }

      case 'record_ccp_monitoring':
        return (
          <div className="space-y-1 text-xs">
            <div className="font-semibold">CCP Monitoring Recorded</div>
            {obj.ccp_id && (
              <div className="text-muted-foreground">
                ID: <span className="font-mono text-foreground">{obj.ccp_id}</span>
              </div>
            )}
            {obj.timestamp && (
              <div className="text-muted-foreground">
                Time: <span className="font-mono text-foreground">{obj.timestamp}</span>
              </div>
            )}
          </div>
        );

      default:
        return JSON.stringify(obj);
    }
  }

  return String(data);
}

/**
 * Get formatted tool arguments for display
 */
function formatToolArguments(toolName: string, args: Record<string, unknown>): string {
  const relevantArgs = Object.entries(args)
    .filter(([key]) => !key.startsWith('_'))
    .slice(0, 3) // Show max 3 arguments
    .map(([key, value]) => {
      if (typeof value === 'string') {
        return `${key}: "${value.substring(0, 20)}${value.length > 20 ? '...' : ''}"`;
      }
      return `${key}: ${value}`;
    });

  return relevantArgs.join(', ');
}

/**
 * VoiceToolFeedback Component
 * Displays rich visual feedback for tool calls with status and results
 */
export const VoiceToolFeedback: React.FC<VoiceToolFeedbackProps> = ({
  toolCall,
  result,
  isExecuting = false,
  error,
  onDismiss,
}) => {
  const isSuccess = result?.success && !error;
  const isError = !isSuccess && (error || result?.error);

  return (
    <div
      className={`rounded-lg border p-3 space-y-2 text-sm ${
        isExecuting
          ? 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800'
          : isError
            ? 'bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800'
            : isSuccess
              ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800'
              : 'bg-gray-50 border-gray-200 dark:bg-gray-950 dark:border-gray-800'
      }`}
    >
      {/* Header: Tool name and status */}
      <div className="flex items-start justify-between gap-2">
        {isExecuting && (
          <Loader2 className="w-4 h-4 text-blue-600 dark:text-blue-400 animate-spin flex-shrink-0 mt-0.5" />
        )}
        {isError && (
          <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
        )}
        {isSuccess && (
          <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
        )}
        {!isExecuting && !isError && !isSuccess && (
          <Info className="w-4 h-4 text-gray-600 dark:text-gray-400 flex-shrink-0 mt-0.5" />
        )}

        <div className="flex-1">
          <div className="font-semibold text-foreground">
            {toolCall.name.replace(/_/g, ' ')}
            {isExecuting && ' (Processing)'}
            {isError && ' (Failed)'}
            {isSuccess && ' (Complete)'}
          </div>
        </div>

        {onDismiss && !isExecuting && (
          <button
            onClick={onDismiss}
            className="flex-shrink-0 text-muted-foreground hover:text-foreground transition-colors"
            aria-label="Dismiss feedback"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Arguments summary */}
      <div className="text-xs text-muted-foreground pl-6">
        {formatToolArguments(toolCall.name, toolCall.arguments as Record<string, unknown>)}
      </div>

      {/* Result message */}
      {(result || error) && (
        <div className="text-xs pl-6">
          {error ? (
            <div className="text-red-700 dark:text-red-300">{error}</div>
          ) : (
            <div
              className={
                isSuccess
                  ? 'text-green-700 dark:text-green-300'
                  : 'text-gray-700 dark:text-gray-300'
              }
            >
              {result?.message}
            </div>
          )}
        </div>
      )}

      {/* Formatted result data */}
      {isSuccess && result?.data && (
        <div
          className={`text-xs pl-6 ${
            isSuccess ? 'text-green-900 dark:text-green-200' : 'text-gray-900 dark:text-gray-200'
          }`}
        >
          {formatToolResultData(toolCall.name, result.data)}
        </div>
      )}
    </div>
  );
};

export default VoiceToolFeedback;
