import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Mi<PERSON>, MicOff, Volume2 } from 'lucide-react';
import { RealtimeAgent, RealtimeSession } from '@openai/agents/realtime';
import VoiceToolExecutor, { Tool<PERSON>all, ToolResult } from '@/lib/voice-tool-executor';
import VoiceToolFeedback from '@/components/VoiceToolFeedback';
import { auditLogger, AuditEventType, AuditSeverity } from '@/lib/voice-audit-logger';
import { getEphemeralKey } from '@/lib/openai-ephemeral-keys';

interface VoiceAgentProps {
  onSpeakingChange?: (speaking: boolean) => void;
}

interface ToolFeedbackState {
  toolCall: ToolCall;
  result?: ToolResult;
  isExecuting: boolean;
  error?: string;
  timestamp: number;
}

interface SessionState {
  id: string;
  startTime: number;
  messageCount: number;
  toolCallCount: number;
  errorCount: number;
  lastMessageTime: number;
}

/**
 * Generate a unique session ID for tracking
 */
function generateSessionId(): string {
  return `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

// Removed getExponentialBackoff - not needed with SDK

// Connection quality handled by SDK

/**
 * Error types for better error messaging
 */
enum ConnectionErrorType {
  API_KEY_MISSING = 'API_KEY_MISSING',
  API_KEY_INVALID = 'API_KEY_INVALID',
  NETWORK_OFFLINE = 'NETWORK_OFFLINE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  WEBSOCKET_FAILED = 'WEBSOCKET_FAILED',
  MICROPHONE_DENIED = 'MICROPHONE_DENIED',
  MAX_RETRIES_REACHED = 'MAX_RETRIES_REACHED',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Get user-friendly error message and suggestions
 */
function getErrorMessage(errorType: ConnectionErrorType, retryCount?: number, backoffMs?: number): {
  title: string;
  description: string;
  suggestions: string[];
} {
  switch (errorType) {
    case ConnectionErrorType.API_KEY_MISSING:
      return {
        title: "Configuration Error",
        description: "OpenAI API key not configured",
        suggestions: [
          "Add VITE_OPENAI_API_KEY to your .env file",
          "Get your API key from https://platform.openai.com/api-keys",
          "Restart the development server after adding the key"
        ]
      };

    case ConnectionErrorType.API_KEY_INVALID:
      return {
        title: "Authentication Failed",
        description: "Invalid OpenAI API key",
        suggestions: [
          "Verify your API key is correct in .env file",
          "Check that the key hasn't been revoked or expired",
          "Ensure you have access to GPT-4 Realtime API"
        ]
      };

    case ConnectionErrorType.NETWORK_OFFLINE:
      return {
        title: "Network Offline",
        description: "No internet connection detected",
        suggestions: [
          "Check your internet connection",
          "Verify WiFi or ethernet is connected",
          "Connection will retry automatically when network is available"
        ]
      };

    case ConnectionErrorType.NETWORK_ERROR: {
      const retryMsg = retryCount !== undefined && backoffMs !== undefined
        ? `Retrying in ${Math.ceil(backoffMs / 1000)}s (attempt ${retryCount + 1}/5)`
        : "Connection will retry automatically";
      return {
        title: "Connection Error",
        description: retryMsg,
        suggestions: [
          "Check your internet connection stability",
          "Verify firewall isn't blocking WebSocket connections",
          "Try disabling VPN if active"
        ]
      };
    }

    case ConnectionErrorType.WEBSOCKET_FAILED:
      return {
        title: "WebSocket Connection Failed",
        description: "Unable to establish real-time connection",
        suggestions: [
          "Check if api.openai.com is accessible",
          "Verify network allows WebSocket connections",
          "Check browser console for detailed error logs"
        ]
      };

    case ConnectionErrorType.MICROPHONE_DENIED:
      return {
        title: "Microphone Access Denied",
        description: "Cannot access microphone",
        suggestions: [
          "Allow microphone access in browser settings",
          "Check system microphone permissions",
          "Reload the page and accept microphone prompt"
        ]
      };

    case ConnectionErrorType.MAX_RETRIES_REACHED:
      return {
        title: "Connection Failed",
        description: "Maximum reconnection attempts reached",
        suggestions: [
          "Click the microphone button to try again",
          "Check your API key and internet connection",
          "View browser console for detailed error logs",
          "Contact support if issue persists"
        ]
      };

    default:
      return {
        title: "Unexpected Error",
        description: "An unknown error occurred",
        suggestions: [
          "Try refreshing the page",
          "Check browser console for error details",
          "Contact support if issue persists"
        ]
      };
  }
}

const VoiceAgent: React.FC<VoiceAgentProps> = ({ onSpeakingChange }) => {
  const { toast } = useToast();
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState<string[]>([]);
  const [isExecutingTool, setIsExecutingTool] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'fair' | 'poor'>('excellent');
  const [toolFeedback, setToolFeedback] = useState<ToolFeedbackState | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isReconnecting, setIsReconnecting] = useState(false);

  const sessionRef = useRef<RealtimeSession | null>(null);
  const agentRef = useRef<RealtimeAgent | null>(null);
  const sessionStateRef = useRef<SessionState>({
    id: generateSessionId(),
    startTime: Date.now(),
    messageCount: 0,
    toolCallCount: 0,
    errorCount: 0,
    lastMessageTime: Date.now()
  });
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const latencyTrackingRef = useRef<number[]>([]);
  const networkMonitoringIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Network monitoring handled by SDK

  const handleToolCall = async (toolCall: ToolCall) => {
    setIsExecutingTool(true);

    // Log tool request
    auditLogger.log(
      AuditEventType.TOOL_REQUESTED,
      AuditSeverity.INFO,
      `Tool requested: ${toolCall.name}`,
      { toolName: toolCall.name, arguments: toolCall.arguments }
    );

    // Initialize feedback state (executing)
    setToolFeedback({
      toolCall,
      isExecuting: true,
      timestamp: Date.now()
    });

    try {
      console.log('Executing tool:', toolCall.name, toolCall.arguments);
      sessionStateRef.current.toolCallCount++;

      const result = await VoiceToolExecutor.executeTool(toolCall);
      const feedbackMessage = VoiceToolExecutor.formatResultForVoice(result);

      // Log tool execution result
      auditLogger.logToolExecution(
        toolCall.name,
        toolCall.arguments as Record<string, unknown>,
        result.success,
        result.data as Record<string, unknown> | undefined,
        result.error
      );

      // Update feedback state with result
      setToolFeedback({
        toolCall,
        result,
        isExecuting: false,
        timestamp: Date.now()
      });

      // Add tool result message
      setMessages(prev => [...prev.slice(-9), `Tool: ${feedbackMessage}`]);

      // Tool results are handled automatically by the SDK
      // The RealtimeSession manages tool execution and responses

      // Show success toast
      if (result.success) {
        toast({
          title: 'Tool Executed',
          description: feedbackMessage,
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to execute tool';
      console.error('Tool execution error:', error);

      // Log tool execution error
      auditLogger.log(
        AuditEventType.TOOL_FAILED,
        AuditSeverity.ERROR,
        `Tool execution failed: ${toolCall.name}`,
        {
          toolName: toolCall.name,
          error: errorMessage,
        }
      );

      // Update feedback state with error
      setToolFeedback({
        toolCall,
        isExecuting: false,
        error: errorMessage,
        timestamp: Date.now()
      });

      toast({
        title: 'Tool Error',
        description: errorMessage,
        variant: 'destructive',
      });
      sessionStateRef.current.errorCount++;
    } finally {
      setIsExecutingTool(false);

      // Auto-clear feedback after 5 seconds if successful
      if (toolFeedback?.result?.success) {
        setTimeout(() => {
          setToolFeedback(null);
        }, 5000);
      }
    }
  };

  /**
   * Create and configure the RealtimeAgent with tools
   */
  const createAgent = () => {
    const agent = new RealtimeAgent({
      name: 'Food Safety Assistant',
      instructions: `You are a helpful food safety assistant specializing in HACCP (Hazard Analysis Critical Control Points) and food safety management.

      You can help with:
      - HACCP plan development and implementation
      - Food safety regulations and compliance
      - Temperature monitoring and control
      - Inventory management and tracking
      - Food safety training and best practices

      You have access to tools for managing inventory records and HACCP events. Use these tools when users need to record or retrieve food safety data.

      Always prioritize food safety and provide accurate, actionable guidance.`,
    });

    // Add tools for food safety management
    // Note: Tool integration with the SDK will be handled through the session events

    return agent;
  };

  const startConversation = async () => {
    try {
      console.log('🔌 Starting conversation with OpenAI Agents SDK...');

      // Reset session state
      sessionStateRef.current = {
        id: generateSessionId(),
        startTime: Date.now(),
        messageCount: 0,
        toolCallCount: 0,
        errorCount: 0,
        lastMessageTime: Date.now()
      };

      // Initialize audit logging for this session
      auditLogger.initSession(sessionStateRef.current.id);

      // Get ephemeral key
      const ephemeralKey = await getEphemeralKey();
      console.log('✅ Ephemeral key obtained');

      // Create agent and session
      agentRef.current = createAgent();
      sessionRef.current = new RealtimeSession(agentRef.current, {
        model: 'gpt-realtime',
      });

      // Set up event listeners
      setupSessionEventListeners();

      // Connect to the session with timeout
      console.log('🔌 Attempting to connect to OpenAI Realtime API...');

      // Add a timeout to the connection attempt
      const connectionPromise = sessionRef.current.connect({ apiKey: ephemeralKey });
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout after 30 seconds')), 30000)
      );

      await Promise.race([connectionPromise, timeoutPromise]);
      console.log('✅ Connected to OpenAI Realtime API via SDK');

      setIsConnected(true);
      setIsRecording(true);
      setRetryCount(0);
      setIsReconnecting(false);

      toast({
        title: "Connected",
        description: "Voice agent ready via OpenAI Agents SDK",
      });

    } catch (error) {
      console.error('❌ Failed to start conversation:', error);
      sessionStateRef.current.errorCount++;

      auditLogger.log(
        AuditEventType.SESSION_ENDED,
        AuditSeverity.ERROR,
        'Failed to start voice session',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to start conversation',
        variant: "destructive",
      });
    }
  };

  /**
   * Set up event listeners for the RealtimeSession
   */
  const setupSessionEventListeners = () => {
    if (!sessionRef.current) return;

    // The OpenAI Agents SDK handles audio and transcription automatically
    // We'll listen for basic session events and let the SDK manage the audio pipeline

    console.log('✅ Session event listeners configured - SDK handles audio automatically');
  };

  /**
   * Note: WebRTC handles audio recording automatically via MediaStream
   * No separate startRecording function needed
   */

  const endConversation = () => {
    // Clear reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Clear network monitoring interval
    if (networkMonitoringIntervalRef.current) {
      clearInterval(networkMonitoringIntervalRef.current);
      networkMonitoringIntervalRef.current = null;
    }

    // Disconnect session (SDK handles cleanup automatically)
    sessionRef.current = null;
    agentRef.current = null;

    // Log session summary
    const duration = Date.now() - sessionStateRef.current.startTime;
    console.log('Session ended', {
      sessionId: sessionStateRef.current.id,
      duration: `${duration}ms`,
      messages: sessionStateRef.current.messageCount,
      toolCalls: sessionStateRef.current.toolCallCount,
      errors: sessionStateRef.current.errorCount
    });

    // Log session end to audit logger
    auditLogger.log(
      AuditEventType.SESSION_ENDED,
      sessionStateRef.current.errorCount > 5 ? AuditSeverity.WARNING : AuditSeverity.INFO,
      'Voice session ended',
      {
        duration,
        messages: sessionStateRef.current.messageCount,
        toolCalls: sessionStateRef.current.toolCallCount,
        errors: sessionStateRef.current.errorCount,
      }
    );

    setIsConnected(false);
    setIsRecording(false);
    setIsSpeaking(false);
    setRetryCount(0);
    setToolFeedback(null);
    onSpeakingChange?.(false);

    toast({
      title: "Disconnected",
      description: "Voice conversation ended",
    });
  };

  const dismissToolFeedback = () => {
    setToolFeedback(null);
  };

  const sendTextMessage = useCallback((text: string) => {
    if (!sessionRef.current || !isConnected) {
      toast({
        title: "Not Connected",
        description: "Please start the conversation first",
        variant: "destructive",
      });
      return;
    }

    try {
      // The SDK handles text message sending automatically
      // For now, we'll just log the message
      console.log('📝 Sending text message:', text);
      setMessages(prev => [...prev.slice(-9), `You: ${text}`]);
      sessionStateRef.current.messageCount++;

      // Note: The OpenAI Agents SDK handles text input through the session
      // This is a simplified implementation for the migration
    } catch (error) {
      console.error('Failed to send text message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  }, [isConnected, toast]);

  // Make sendTextMessage available globally for debugging
  React.useEffect(() => {
    (window as unknown as { sendTextMessage?: typeof sendTextMessage }).sendTextMessage = sendTextMessage;
    return () => {
      delete (window as unknown as { sendTextMessage?: typeof sendTextMessage }).sendTextMessage;
    };
  }, [sendTextMessage]);

  /**
   * Monitor network online/offline status
   */
  useEffect(() => {
    const handleOnline = () => {
      console.log('Network came back online');
      setIsOnline(true);

      // Auto-reconnect if we were previously connected
      if (!isConnected && !sessionRef.current) {
        console.log('Auto-reconnecting after network restoration...');
        setIsReconnecting(true);
        toast({
          title: "Network Restored",
          description: "Reconnecting to voice service...",
        });

        // Reset retry count and attempt connection
        setRetryCount(0);
        startConversation();
      }
    };

    const handleOffline = () => {
      console.log('Network went offline');
      setIsOnline(false);

      // Show offline message with actionable suggestions
      const errorInfo = getErrorMessage(ConnectionErrorType.NETWORK_OFFLINE);
      toast({
        title: errorInfo.title,
        description: errorInfo.description + '\n\n' + errorInfo.suggestions.join('\n'),
        variant: "destructive",
        duration: 10000, // Show longer for offline state
      });

      // Close existing connection
      if (sessionRef.current) {
        sessionRef.current = null;
      }
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConnected, toast]);

  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (networkMonitoringIntervalRef.current) {
        clearInterval(networkMonitoringIntervalRef.current);
      }
      sessionRef.current = null;
    };
  }, []);

  return (
    <div className="fixed bottom-4 right-4 flex flex-col items-end gap-4 z-50">
      {/* Tool Feedback Display */}
      {toolFeedback && (
        <div className="max-w-sm">
          <VoiceToolFeedback
            toolCall={toolFeedback.toolCall}
            result={toolFeedback.result}
            isExecuting={toolFeedback.isExecuting}
            error={toolFeedback.error}
            onDismiss={dismissToolFeedback}
          />
        </div>
      )}

      {/* Messages display */}
      {messages.length > 0 && (
        <div className="bg-background/90 backdrop-blur-sm border rounded-lg p-4 max-w-sm max-h-48 overflow-y-auto">
          <h3 className="text-sm font-medium mb-2">Voice Chat</h3>
          <div className="space-y-1 text-xs">
            {messages.map((message, index) => (
              <div key={index} className={message.startsWith('You:') ? 'text-primary' : 'text-muted-foreground'}>
                {message}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Control buttons */}
      <div className="flex flex-col gap-2">
        {!isConnected ? (
          <div className="flex flex-col gap-2">
            <Button
              onClick={startConversation}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              size="lg"
              disabled={retryCount > 0}
            >
              <Mic className="w-5 h-5 mr-2" />
              {retryCount > 0 ? `Connecting... (${retryCount})` : 'Start Voice Chat'}
            </Button>
            {retryCount > 0 && (
              <div className="text-xs text-amber-600 text-center">
                {isReconnecting ? 'Reconnecting...' : 'Connecting...'} Attempt {retryCount}/5
              </div>
            )}
            {!isOnline && (
              <div className="text-xs text-red-600 text-center font-medium">
                Network Offline - Will auto-reconnect when online
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <div className="flex gap-2">
              <Button
                onClick={endConversation}
                variant="outline"
                size="lg"
              >
                <MicOff className="w-5 h-5 mr-2" />
                End Chat
              </Button>

              {/* Connection quality indicator */}
              <div className={`flex items-center gap-2 rounded-lg px-3 py-2 text-xs font-medium ${
                connectionQuality === 'excellent' ? 'bg-green-100 text-green-800' :
                connectionQuality === 'good' ? 'bg-blue-100 text-blue-800' :
                connectionQuality === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  connectionQuality === 'excellent' ? 'bg-green-600' :
                  connectionQuality === 'good' ? 'bg-blue-600' :
                  connectionQuality === 'fair' ? 'bg-yellow-600' :
                  'bg-red-600'
                }`} />
                {connectionQuality}
              </div>
            </div>

            {(isRecording || isSpeaking || isExecutingTool) && (
              <div className="flex items-center gap-2 bg-background/90 backdrop-blur-sm border rounded-lg px-3 py-2">
                {isRecording && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-destructive rounded-full animate-pulse" />
                    <span className="text-xs">Listening</span>
                  </div>
                )}
                {isSpeaking && (
                  <div className="flex items-center gap-1">
                    <Volume2 className="w-4 h-4 text-primary" />
                    <span className="text-xs">Speaking</span>
                  </div>
                )}
                {isExecutingTool && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-spin" />
                    <span className="text-xs">Processing</span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Quick action buttons */}
        {isConnected && (
          <div className="flex flex-col gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => sendTextMessage("What is HACCP?")}
            >
              Ask about HACCP
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => sendTextMessage("Help me with food safety")}
            >
              Food Safety Help
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VoiceAgent;