import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { <PERSON><PERSON>, MicOff, Volume2 } from 'lucide-react';
import { RealtimeAgent, RealtimeSession, tool } from '@openai/agents/realtime';
import VoiceToolExecutor, { Tool<PERSON>all, ToolResult } from '@/lib/voice-tool-executor';
import VoiceToolFeedback from '@/components/VoiceToolFeedback';
import { auditLogger, AuditEventType, AuditSeverity } from '@/lib/voice-audit-logger';
import { getEphemeralKey } from '@/lib/openai-ephemeral-keys';

interface VoiceAgentProps {
  onSpeakingChange?: (speaking: boolean) => void;
}

interface ToolFeedbackState {
  toolCall: ToolCall;
  result?: ToolResult;
  isExecuting: boolean;
  error?: string;
  timestamp: number;
}

interface SessionState {
  id: string;
  startTime: number;
  messageCount: number;
  toolCallCount: number;
  errorCount: number;
  lastMessageTime: number;
}

/**
 * Generate a unique session ID for tracking
 */
function generateSessionId(): string {
  return `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

// Removed getExponentialBackoff - not needed with SDK

// Connection quality handled by SDK

/**
 * Error types for better error messaging
 */
enum ConnectionErrorType {
  API_KEY_MISSING = 'API_KEY_MISSING',
  API_KEY_INVALID = 'API_KEY_INVALID',
  NETWORK_OFFLINE = 'NETWORK_OFFLINE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  WEBSOCKET_FAILED = 'WEBSOCKET_FAILED',
  MICROPHONE_DENIED = 'MICROPHONE_DENIED',
  MAX_RETRIES_REACHED = 'MAX_RETRIES_REACHED',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Get user-friendly error message and suggestions
 */
function getErrorMessage(errorType: ConnectionErrorType, retryCount?: number, backoffMs?: number): {
  title: string;
  description: string;
  suggestions: string[];
} {
  switch (errorType) {
    case ConnectionErrorType.API_KEY_MISSING:
      return {
        title: "Configuration Error",
        description: "OpenAI API key not configured",
        suggestions: [
          "Add VITE_OPENAI_API_KEY to your .env file",
          "Get your API key from https://platform.openai.com/api-keys",
          "Restart the development server after adding the key"
        ]
      };

    case ConnectionErrorType.API_KEY_INVALID:
      return {
        title: "Authentication Failed",
        description: "Invalid OpenAI API key",
        suggestions: [
          "Verify your API key is correct in .env file",
          "Check that the key hasn't been revoked or expired",
          "Ensure you have access to GPT-4 Realtime API"
        ]
      };

    case ConnectionErrorType.NETWORK_OFFLINE:
      return {
        title: "Network Offline",
        description: "No internet connection detected",
        suggestions: [
          "Check your internet connection",
          "Verify WiFi or ethernet is connected",
          "Connection will retry automatically when network is available"
        ]
      };

    case ConnectionErrorType.NETWORK_ERROR: {
      const retryMsg = retryCount !== undefined && backoffMs !== undefined
        ? `Retrying in ${Math.ceil(backoffMs / 1000)}s (attempt ${retryCount + 1}/5)`
        : "Connection will retry automatically";
      return {
        title: "Connection Error",
        description: retryMsg,
        suggestions: [
          "Check your internet connection stability",
          "Verify firewall isn't blocking WebSocket connections",
          "Try disabling VPN if active"
        ]
      };
    }

    case ConnectionErrorType.WEBSOCKET_FAILED:
      return {
        title: "WebSocket Connection Failed",
        description: "Unable to establish real-time connection",
        suggestions: [
          "Check if api.openai.com is accessible",
          "Verify network allows WebSocket connections",
          "Check browser console for detailed error logs"
        ]
      };

    case ConnectionErrorType.MICROPHONE_DENIED:
      return {
        title: "Microphone Access Denied",
        description: "Cannot access microphone",
        suggestions: [
          "Allow microphone access in browser settings",
          "Check system microphone permissions",
          "Reload the page and accept microphone prompt"
        ]
      };

    case ConnectionErrorType.MAX_RETRIES_REACHED:
      return {
        title: "Connection Failed",
        description: "Maximum reconnection attempts reached",
        suggestions: [
          "Click the microphone button to try again",
          "Check your API key and internet connection",
          "View browser console for detailed error logs",
          "Contact support if issue persists"
        ]
      };

    default:
      return {
        title: "Unexpected Error",
        description: "An unknown error occurred",
        suggestions: [
          "Try refreshing the page",
          "Check browser console for error details",
          "Contact support if issue persists"
        ]
      };
  }
}

const VoiceAgent: React.FC<VoiceAgentProps> = ({ onSpeakingChange }) => {
  const { toast } = useToast();
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState<string[]>([]);
  const [isExecutingTool, setIsExecutingTool] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'fair' | 'poor'>('excellent');
  const [toolFeedback, setToolFeedback] = useState<ToolFeedbackState | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [audioVolume, setAudioVolume] = useState(0.8);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [useManualMode, setUseManualMode] = useState(false); // Toggle for manual commit mode

  const sessionRef = useRef<RealtimeSession | null>(null);
  const agentRef = useRef<RealtimeAgent | null>(null);
  const sessionStateRef = useRef<SessionState>({
    id: generateSessionId(),
    startTime: Date.now(),
    messageCount: 0,
    toolCallCount: 0,
    errorCount: 0,
    lastMessageTime: Date.now()
  });
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const latencyTrackingRef = useRef<number[]>([]);
  const networkMonitoringIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const currentResponseRef = useRef<{
    id: string;
    text: string;
    isComplete: boolean;
  } | null>(null);

  // Network monitoring handled by SDK

  const handleToolCall = async (toolCall: ToolCall): Promise<ToolResult> => {
    setIsExecutingTool(true);

    // Log tool request
    auditLogger.log(
      AuditEventType.TOOL_REQUESTED,
      AuditSeverity.INFO,
      `Tool requested: ${toolCall.name}`,
      { toolName: toolCall.name, arguments: toolCall.arguments }
    );

    // Initialize feedback state (executing)
    setToolFeedback({
      toolCall,
      isExecuting: true,
      timestamp: Date.now()
    });

    try {
      console.log('Executing tool:', toolCall.name, toolCall.arguments);
      sessionStateRef.current.toolCallCount++;

      const result = await VoiceToolExecutor.executeTool(toolCall);
      const feedbackMessage = VoiceToolExecutor.formatResultForVoice(result);

      // Log tool execution result
      auditLogger.logToolExecution(
        toolCall.name,
        toolCall.arguments as Record<string, unknown>,
        result.success,
        result.data as Record<string, unknown> | undefined,
        result.error
      );

      // Update feedback state with result
      setToolFeedback({
        toolCall,
        result,
        isExecuting: false,
        timestamp: Date.now()
      });

      // Add tool result message
      setMessages(prev => [...prev.slice(-9), `Tool: ${feedbackMessage}`]);

      // Tool results are handled automatically by the SDK
      // The RealtimeSession manages tool execution and responses

      // Show success toast
      if (result.success) {
        toast({
          title: 'Tool Executed',
          description: feedbackMessage,
        });
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to execute tool';
      console.error('Tool execution error:', error);

      // Log tool execution error
      auditLogger.log(
        AuditEventType.TOOL_FAILED,
        AuditSeverity.ERROR,
        `Tool execution failed: ${toolCall.name}`,
        {
          toolName: toolCall.name,
          error: errorMessage,
        }
      );

      // Update feedback state with error
      setToolFeedback({
        toolCall,
        isExecuting: false,
        error: errorMessage,
        timestamp: Date.now()
      });

      toast({
        title: 'Tool Error',
        description: errorMessage,
        variant: 'destructive',
      });
      sessionStateRef.current.errorCount++;

      return {
        success: false,
        message: errorMessage,
        error: errorMessage
      };
    } finally {
      setIsExecutingTool(false);

      // Auto-clear feedback after 5 seconds if successful
      if (toolFeedback?.result?.success) {
        setTimeout(() => {
          setToolFeedback(null);
        }, 5000);
      }
    }
  };

  /**
   * Create and configure the RealtimeAgent with tools
   */
  const createAgent = () => {
    const agent = new RealtimeAgent({
      name: 'Food Safety Assistant',
      instructions: `You are a helpful food safety assistant specializing in HACCP (Hazard Analysis Critical Control Points) and food safety management.

      You can help with:
      - HACCP plan development and implementation
      - Food safety regulations and compliance
      - Temperature monitoring and control
      - Inventory management and tracking
      - Food safety training and best practices

      You have access to tools for managing inventory records and HACCP events. Use these tools when users need to record or retrieve food safety data.

      Always prioritize food safety and provide accurate, actionable guidance.`,
      tools: [
        tool({
          name: 'add_inventory_event',
          description: 'Add a new inventory event or stock transaction. Use this when a user wants to record a new inventory addition, receipt, or stock adjustment.',
          parameters: {
            type: 'object',
            additionalProperties: false,
            properties: {
              product_id: {
                type: 'string',
                description: 'The unique identifier of the product'
              },
              batch_number: {
                type: 'string',
                description: 'The batch or lot number for the inventory'
              },
              quantity: {
                type: 'number',
                description: 'The quantity being added'
              },
              unit: {
                type: 'string',
                description: 'The unit of measurement (e.g., kg, lbs, boxes)',
                enum: ['kg', 'lbs', 'boxes', 'units', 'liters', 'gallons']
              },
              temperature: {
                type: 'number',
                description: 'Temperature at time of receipt (in Celsius)'
              },
              supplier: {
                type: 'string',
                description: 'Name of the supplier'
              },
              location: {
                type: 'string',
                description: 'Storage location (e.g., freezer, cooler, dry storage)'
              },
              expiry_date: {
                type: 'string',
                description: 'Expiry date in YYYY-MM-DD format'
              },
              notes: {
                type: 'string',
                description: 'Additional notes or observations'
              }
            },
            required: ['product_id', 'batch_number', 'quantity', 'unit', 'temperature', 'supplier', 'location']
          },
          execute: async (args) => {
            console.log('🔧 Tool called: add_inventory_event', args);
            const result = await handleToolCall({
              name: 'add_inventory_event',
              arguments: args
            });
            return result.message;
          }
        }),
        tool({
          name: 'record_ccp_monitoring',
          description: 'Record a Critical Control Point (CCP) monitoring event for HACCP compliance.',
          parameters: {
            type: 'object',
            additionalProperties: false,
            properties: {
              ccp_name: {
                type: 'string',
                description: 'Name of the Critical Control Point being monitored'
              },
              measurement_value: {
                type: 'number',
                description: 'The measured value (e.g., temperature, pH, time)'
              },
              measurement_unit: {
                type: 'string',
                description: 'Unit of measurement (e.g., °C, °F, pH, minutes)'
              },
              critical_limit: {
                type: 'string',
                description: 'The critical limit for this CCP'
              },
              corrective_action: {
                type: 'string',
                description: 'Corrective action taken if any'
              },
              monitored_by: {
                type: 'string',
                description: 'Name or ID of the person performing the monitoring'
              },
              observations: {
                type: 'string',
                description: 'Additional observations or notes'
              }
            },
            required: ['ccp_name', 'measurement_value', 'measurement_unit', 'monitored_by']
          },
          execute: async (args) => {
            console.log('🔧 Tool called: record_ccp_monitoring', args);
            const result = await handleToolCall({
              name: 'record_ccp_monitoring',
              arguments: args
            });
            return result.message;
          }
        })
      ]
    });

    return agent;
  };

  const startConversation = async () => {
    try {
      console.log('🔌 Starting conversation with OpenAI Agents SDK...');

      // Reset session state
      sessionStateRef.current = {
        id: generateSessionId(),
        startTime: Date.now(),
        messageCount: 0,
        toolCallCount: 0,
        errorCount: 0,
        lastMessageTime: Date.now()
      };

      // Initialize audit logging for this session
      auditLogger.initSession(sessionStateRef.current.id);

      // CRITICAL: Request microphone permissions BEFORE connecting
      // The WebRTC SDK needs mic access for audio input
      console.log('🎤 Requesting microphone permissions...');
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        console.log('✅ Microphone access granted');
        // Stop the test stream - SDK will request its own
        stream.getTracks().forEach(track => track.stop());
      } catch (micError) {
        console.error('❌ Microphone permission denied:', micError);
        throw new Error('Microphone access is required for voice chat. Please allow microphone access in your browser settings.');
      }

      // Get ephemeral key
      console.log('🔑 Getting ephemeral key...');
      const ephemeralKey = await getEphemeralKey();
      console.log('✅ Ephemeral key obtained');

      // Create agent and session
      console.log('🤖 Creating agent and session...');
      agentRef.current = createAgent();
      sessionRef.current = new RealtimeSession(agentRef.current, {
        model: 'gpt-4o-realtime-preview', // Use stable model name
        transport: 'webrtc', // Explicitly specify WebRTC transport
        config: {
          // Voice Activity Detection (VAD) configuration
          turn_detection: useManualMode ? null : {
            // Automatic VAD mode (detects when you stop speaking)
            type: 'server_vad',
            threshold: 0.6, // Less sensitive to reduce false positives
            prefix_padding_ms: 300, // Audio before speech starts
            silence_duration_ms: 700, // Longer silence required (700ms)
            create_response: true // Automatically create response when speech detected
          },
          // Enable input audio transcription for debugging
          input_audio_transcription: {
            model: 'whisper-1'
          }
        }
      });

      console.log('✅ Session created with config:', {
        model: 'gpt-4o-realtime-preview',
        transport: 'webrtc',
        mode: useManualMode ? 'MANUAL' : 'AUTO_VAD',
        vadEnabled: !useManualMode,
        vadThreshold: useManualMode ? 'N/A' : 0.6,
        vadSilenceDuration: useManualMode ? 'N/A' : '700ms',
        transcriptionEnabled: true
      });

      // Set up event listeners
      setupSessionEventListeners();

      // Connect to the session
      console.log('🔌 Connecting to OpenAI Realtime API via WebRTC...');
      console.log('⏱️ Connection timeout set to 30 seconds');

      const connectionPromise = sessionRef.current.connect({ apiKey: ephemeralKey });
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout after 30 seconds. Please check your network connection and try again.')), 30000)
      );

      await Promise.race([connectionPromise, timeoutPromise]);
      console.log('✅ Connected to OpenAI Realtime API via WebRTC');

      // Log connection details
      const transport = (sessionRef.current as any).transport;
      if (transport) {
        console.log('📊 Connection Details:', {
          status: transport.status,
          callId: transport.callId,
          connectionState: transport.connectionState,
          muted: transport.muted,
          currentModel: transport.currentModel
        });

        // Check if peer connection is established
        const pc = transport.connectionState?.peerConnection;
        if (pc) {
          console.log('📊 WebRTC Peer Connection:', {
            connectionState: pc.connectionState,
            iceConnectionState: pc.iceConnectionState,
            signalingState: pc.signalingState,
            localDescription: pc.localDescription?.type,
            remoteDescription: pc.remoteDescription?.type
          });

          // List all tracks
          const receivers = pc.getReceivers();
          const senders = pc.getSenders();
          console.log('📊 WebRTC Tracks:', {
            receivers: receivers.length,
            senders: senders.length,
            audioInputs: senders.filter((s: RTCRtpSender) => s.track?.kind === 'audio').length,
            audioOutputs: receivers.filter((r: RTCRtpReceiver) => r.track?.kind === 'audio').length
          });
        }
      }

      setIsConnected(true);
      setIsRecording(true);
      setRetryCount(0);
      setIsReconnecting(false);

      toast({
        title: "Connected",
        description: "Voice agent ready. Start speaking!",
      });

      // Give user a test prompt
      console.log('💡 Try saying: "Hello, can you hear me?" or "What is HACCP?"');

    } catch (error) {
      console.error('❌ Failed to start conversation:', error);
      sessionStateRef.current.errorCount++;

      const errorMessage = error instanceof Error ? error.message : 'Failed to start conversation';

      auditLogger.log(
        AuditEventType.SESSION_ENDED,
        AuditSeverity.ERROR,
        'Failed to start voice session',
        { error: errorMessage }
      );

      // Determine error type for better user guidance
      let errorType = ConnectionErrorType.UNKNOWN;
      if (errorMessage.includes('Microphone')) {
        errorType = ConnectionErrorType.MICROPHONE_DENIED;
      } else if (errorMessage.includes('timeout')) {
        errorType = ConnectionErrorType.NETWORK_ERROR;
      }

      const errorInfo = getErrorMessage(errorType);

      toast({
        title: errorInfo.title,
        description: `${errorInfo.description}\n\n${errorInfo.suggestions.join('\n')}`,
        variant: "destructive",
        duration: 10000,
      });
    }
  };

  /**
   * Set up event listeners for the RealtimeSession
   */
  const setupSessionEventListeners = () => {
    if (!sessionRef.current) return;

    const session = sessionRef.current;

    try {
      // Listen for ALL possible events to understand what's happening
      console.log('🔌 Setting up comprehensive event listeners...');
      console.log('📊 Session object:', session);
      console.log('📊 Session keys:', Object.keys(session));

      // Listen for transport layer events which are more reliable
      // The OpenAI Agents SDK uses a transport layer that emits these events

      // Try to access the transport layer events
      const transport = (session as any).transport;
      if (transport) {
        console.log('✅ Transport layer accessible');
        console.log('📊 Transport object:', transport);
        console.log('📊 Transport keys:', Object.keys(transport));
        console.log('📊 Transport status:', (transport as any).status);

        // Listen for ALL events to debug what's actually happening
        // This will help us understand what events the SDK is emitting
        const allEventTypes = [
          'connected', 'disconnected', 'error',
          'conversation.item.created', 'conversation.item.deleted',
          'input_audio_buffer.committed', 'input_audio_buffer.cleared', 'input_audio_buffer.speech_started', 'input_audio_buffer.speech_stopped',
          'response.created', 'response.done', 'response.output_item.added', 'response.output_item.done',
          'response.content_part.added', 'response.content_part.done',
          'response.text.delta', 'response.text.done',
          'response.audio.delta', 'response.audio.done', 'response.audio_transcript.delta', 'response.audio_transcript.done',
          'response.function_call_arguments.delta', 'response.function_call_arguments.done',
          'rate_limits.updated', 'session.created', 'session.updated'
        ];

        console.log('🎧 Registering listeners for', allEventTypes.length, 'event types');

        allEventTypes.forEach(eventType => {
          try {
            transport.on(eventType, (event: any) => {
              console.log(`🔔 [${eventType}]`, event);
              
              // Track important events with detailed logging
              if (eventType === 'input_audio_buffer.speech_started') {
                console.log('🎤 User started speaking!');
              } else if (eventType === 'input_audio_buffer.speech_stopped') {
                console.log('🎤 User stopped speaking');
              } else if (eventType === 'input_audio_buffer.committed') {
                console.log('✅ Audio committed - requesting AI response...');

                // Request AI response after audio is committed
                try {
                  const transport = (sessionRef.current as any).transport;
                  if (transport) {
                    transport.send(JSON.stringify({
                      type: 'response.create',
                      response: { modalities: ['text', 'audio'] }
                    }));
                    console.log('🤖 Response requested from AI');
                  }
                } catch (error) {
                  console.error('❌ Failed to request AI response:', error);
                }

                // After commit, temporarily mute to prevent echo/noise triggering new speech
                setTimeout(() => {
                  console.log('⏸️ Temporarily pausing input to allow AI response...');
                }, 100);
              } else if (eventType === 'response.created') {
                console.log('🤖 AI started generating response!', event);
                toast({
                  title: "AI Responding",
                  description: "Generating response...",
                });
              } else if (eventType === 'response.audio.delta') {
                console.log('🔊 AI audio chunk received');
              } else if (eventType === 'response.audio_transcript.delta') {
                console.log('📝 AI transcript:', event.delta);
              } else if (eventType === 'response.done') {
                console.log('✅ AI response complete!');
              }
            });
          } catch (e) {
            // Event type not supported, skip silently
          }
        });

        // Listen for response events from the transport
        transport.on('response.text.delta', (event: any) => {
          console.log('📝 AI text delta:', event);

          // Accumulate text deltas for the current response
          if (!currentResponseRef.current) {
            currentResponseRef.current = {
              id: event.response_id || 'unknown',
              text: '',
              isComplete: false
            };
          }

          if (event.delta) {
            currentResponseRef.current.text += event.delta;
            console.log('📝 Accumulated text so far:', currentResponseRef.current.text);
          }
        });

        transport.on('response.text.done', (event: any) => {
          console.log('✅ AI text complete:', event);

          if (currentResponseRef.current && currentResponseRef.current.text.trim()) {
            // Add complete AI response to messages
            setMessages(prev => [...prev.slice(-9), `AI: ${currentResponseRef.current!.text.trim()}`]);
            sessionStateRef.current.messageCount++;

            // Reset current response
            currentResponseRef.current = null;
          }
        });

        transport.on('response.audio.delta', (event: any) => {
          console.log('🔊 AI audio delta received:', {
            hasAudio: !!event.audio,
            audioLength: event.audio?.length,
            eventKeys: Object.keys(event),
            audioEnabled,
            audioVolume,
            timestamp: new Date().toISOString()
          });

          // Check if audio element exists
          const audioElements = document.querySelectorAll('audio');
          console.log('🔍 Audio elements on page:', audioElements.length);
          audioElements.forEach((audio, i) => {
            console.log(`🔍 Audio element ${i}:`, {
              paused: audio.paused,
              volume: audio.volume,
              muted: audio.muted,
              readyState: audio.readyState
            });
          });

          // Audio is handled automatically by the SDK
          // Update speaking state when audio starts (only if audio is enabled)
          if (audioEnabled && !isSpeaking) {
            setIsSpeaking(true);
            onSpeakingChange?.(true);
            console.log('🔊 Started speaking state');
          } else if (!audioEnabled) {
            console.log('🔇 Audio disabled - not updating speaking state');
          }
        });

        transport.on('response.audio.done', (event: any) => {
          console.log('🔇 AI audio complete:', event);
          // Update speaking state when audio ends
          setIsSpeaking(false);
          onSpeakingChange?.(false);
          console.log('🔇 Ended speaking state');
        });

        transport.on('response.done', (event: any) => {
          console.log('✅ AI response complete:', event);
          // Ensure speaking state is reset
          setIsSpeaking(false);
          onSpeakingChange?.(false);
        });

        transport.on('error', (event: any) => {
          console.error('❌ Transport error:', event);
          console.error('❌ Error details:', {
            type: event.type,
            error: event.error,
            errorMessage: event.error?.message,
            errorCode: event.error?.code,
            errorType: event.error?.type,
            fullError: JSON.stringify(event, null, 2)
          });
          sessionStateRef.current.errorCount++;

          // Show error toast with full details
          const errorMessage = event.error?.message || event.error?.type || "An error occurred during the conversation";
          const errorCode = event.error?.code || event.error?.error?.code || 'unknown';
          
          toast({
            title: `Voice Agent Error (${errorCode})`,
            description: errorMessage,
            variant: "destructive",
            duration: 15000, // Show for 15 seconds
          });
          
          // Log to console for debugging
          console.error('🔍 Full error object:', event.error);
          console.error('🔍 Error stringified:', JSON.stringify(event.error, null, 2));
        });

        // Listen for tool call events
        transport.on('response.function_call_delta', (event: any) => {
          console.log('🔧 Tool call delta:', event);
          if (!isExecutingTool) {
            setIsExecutingTool(true);
          }
        });

        transport.on('response.function_call_done', (event: any) => {
          console.log('✅ Tool call complete:', event);
          setIsExecutingTool(false);
        });

        console.log('✅ Transport layer event listeners configured');
      } else {
        console.warn('⚠️ Transport layer not accessible, using session-level events');

        // Fallback to session-level events
        // The RealtimeSession might emit events directly
        try {
          // Listen for any events that might be emitted by the session
          const eventTypes = [
            'message', 'response', 'audio', 'text', 'error', 'connected', 'disconnected',
            'response.created', 'response.done', 'response.text.delta', 'response.text.done',
            'response.audio.delta', 'response.audio.done', 'conversation.item.created'
          ];

          eventTypes.forEach(eventType => {
            try {
              (session as any).on?.(eventType, (event: any) => {
                console.log(`🔍 Session event [${eventType}]:`, event);

                // Handle specific event types
                if (eventType === 'response.text.delta' || eventType === 'text') {
                  if (event.delta || event.text) {
                    if (!currentResponseRef.current) {
                      currentResponseRef.current = {
                        id: event.response_id || event.id || 'unknown',
                        text: '',
                        isComplete: false
                      };
                    }
                    currentResponseRef.current.text += (event.delta || event.text || '');
                  }
                } else if (eventType === 'response.text.done' || eventType === 'response.done') {
                  if (currentResponseRef.current && currentResponseRef.current.text.trim()) {
                    setMessages(prev => [...prev.slice(-9), `AI: ${currentResponseRef.current!.text.trim()}`]);
                    sessionStateRef.current.messageCount++;
                    currentResponseRef.current = null;
                  }
                } else if (eventType === 'response.audio.delta' || eventType === 'audio') {
                  if (!isSpeaking) {
                    setIsSpeaking(true);
                    onSpeakingChange?.(true);
                  }
                } else if (eventType === 'response.audio.done') {
                  setIsSpeaking(false);
                  onSpeakingChange?.(false);
                } else if (eventType === 'error') {
                  sessionStateRef.current.errorCount++;
                  toast({
                    title: "Voice Agent Error",
                    description: event.error?.message || event.message || "An error occurred",
                    variant: "destructive",
                  });
                }
              });
            } catch (e) {
              // Silently ignore if event type is not supported
            }
          });

          console.log('🔍 Generic event listeners configured for session');
        } catch (error) {
          console.error('❌ Error setting up generic event listeners:', error);
        }
      }
    } catch (error) {
      console.error('❌ Error setting up event listeners:', error);
    }

    console.log('✅ Session event listeners configured - monitoring for AI responses');
  };

  /**
   * Note: WebRTC handles audio recording automatically via MediaStream
   * No separate startRecording function needed
   */

  const endConversation = () => {
    // Clear reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Clear network monitoring interval
    if (networkMonitoringIntervalRef.current) {
      clearInterval(networkMonitoringIntervalRef.current);
      networkMonitoringIntervalRef.current = null;
    }

    // Disconnect session (SDK handles cleanup automatically)
    sessionRef.current = null;
    agentRef.current = null;

    // Log session summary
    const duration = Date.now() - sessionStateRef.current.startTime;
    console.log('Session ended', {
      sessionId: sessionStateRef.current.id,
      duration: `${duration}ms`,
      messages: sessionStateRef.current.messageCount,
      toolCalls: sessionStateRef.current.toolCallCount,
      errors: sessionStateRef.current.errorCount
    });

    // Log session end to audit logger
    auditLogger.log(
      AuditEventType.SESSION_ENDED,
      sessionStateRef.current.errorCount > 5 ? AuditSeverity.WARNING : AuditSeverity.INFO,
      'Voice session ended',
      {
        duration,
        messages: sessionStateRef.current.messageCount,
        toolCalls: sessionStateRef.current.toolCallCount,
        errors: sessionStateRef.current.errorCount,
      }
    );

    setIsConnected(false);
    setIsRecording(false);
    setIsSpeaking(false);
    setRetryCount(0);
    setToolFeedback(null);
    onSpeakingChange?.(false);

    toast({
      title: "Disconnected",
      description: "Voice conversation ended",
    });
  };

  const dismissToolFeedback = () => {
    setToolFeedback(null);
  };

  const sendTextMessage = useCallback(async (text: string) => {
    if (!sessionRef.current || !isConnected) {
      toast({
        title: "Not Connected",
        description: "Please start the conversation first",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('📝 Sending text message:', text);

      // Add user message to UI immediately
      setMessages(prev => [...prev.slice(-9), `You: ${text}`]);
      sessionStateRef.current.messageCount++;

      // Send message through the RealtimeSession
      // Try different approaches to send the message
      try {
        // Method 1: Try sendMessage with correct format
        if (typeof sessionRef.current.sendMessage === 'function') {
          await sessionRef.current.sendMessage({
            type: 'message',
            role: 'user',
            content: [
              {
                type: 'input_text',
                text: text
              }
            ]
          });
          console.log('✅ Message sent via sendMessage method');
        } else {
          console.warn('⚠️ sendMessage method not available');
        }
      } catch (msgError) {
        console.warn('⚠️ sendMessage failed, trying alternative approach:', msgError);

        // Method 2: Try accessing transport layer directly
        try {
          const transport = (sessionRef.current as any).transport;
          if (transport && transport.send) {
            transport.send(JSON.stringify({
              type: 'conversation.item.create',
              item: {
                type: 'message',
                role: 'user',
                content: [{ type: 'input_text', text: text }]
              }
            }));

            // Request response
            transport.send(JSON.stringify({
              type: 'response.create',
              response: { modalities: ['text', 'audio'] }
            }));

            console.log('✅ Message sent via transport layer');
          } else {
            console.warn('⚠️ Transport layer not accessible for sending');
          }
        } catch (transportError) {
          console.error('❌ All message sending methods failed:', transportError);
          throw new Error('Unable to send message - no available methods');
        }
      }

      console.log('✅ Text message sent successfully');
    } catch (error) {
      console.error('Failed to send text message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  }, [isConnected, toast]);

  // Make sendTextMessage available globally for debugging
  React.useEffect(() => {
    (window as unknown as { sendTextMessage?: typeof sendTextMessage }).sendTextMessage = sendTextMessage;
    return () => {
      delete (window as unknown as { sendTextMessage?: typeof sendTextMessage }).sendTextMessage;
    };
  }, [sendTextMessage]);

  // Handle audio settings changes
  useEffect(() => {
    // If audio is disabled and we're currently speaking, stop the speaking state
    if (!audioEnabled && isSpeaking) {
      setIsSpeaking(false);
      onSpeakingChange?.(false);
      console.log('🔇 Audio disabled - stopped speaking state');
    }
  }, [audioEnabled, isSpeaking, onSpeakingChange]);

  // Handle volume changes
  useEffect(() => {
    // Note: The OpenAI Agents SDK handles audio output automatically
    // Volume control would need to be implemented at the browser level
    // or through the SDK's audio configuration if available
    console.log('🔊 Audio volume changed to:', Math.round(audioVolume * 100) + '%');

    // Try to set volume on any audio elements that might be created by the SDK
    const audioElements = document.querySelectorAll('audio');
    console.log('🔍 Found audio elements:', audioElements.length);
    audioElements.forEach((audio, index) => {
      console.log(`🔊 Setting volume on audio element ${index}:`, audioVolume);
      audio.volume = audioVolume;
    });

    // Also try to find and control WebRTC audio streams
    if (sessionRef.current) {
      try {
        const session = sessionRef.current as any;
        const transport = session.transport;
        if (transport && transport.peerConnection) {
          const pc = transport.peerConnection;
          const receivers = pc.getReceivers();
          console.log('🔍 Found WebRTC receivers:', receivers.length);

          receivers.forEach((receiver: RTCRtpReceiver, index: number) => {
            if (receiver.track && receiver.track.kind === 'audio') {
              console.log(`🔊 Found audio track ${index}:`, receiver.track);
              // Note: WebRTC audio tracks don't have direct volume control
              // Volume would need to be controlled through the audio context
            }
          });
        }
      } catch (error) {
        console.log('🔍 Could not access WebRTC audio controls:', error);
      }
    }
  }, [audioVolume]);

  /**
   * Monitor network online/offline status
   */
  useEffect(() => {
    const handleOnline = () => {
      console.log('Network came back online');
      setIsOnline(true);

      // Auto-reconnect if we were previously connected
      if (!isConnected && !sessionRef.current) {
        console.log('Auto-reconnecting after network restoration...');
        setIsReconnecting(true);
        toast({
          title: "Network Restored",
          description: "Reconnecting to voice service...",
        });

        // Reset retry count and attempt connection
        setRetryCount(0);
        startConversation();
      }
    };

    const handleOffline = () => {
      console.log('Network went offline');
      setIsOnline(false);

      // Show offline message with actionable suggestions
      const errorInfo = getErrorMessage(ConnectionErrorType.NETWORK_OFFLINE);
      toast({
        title: errorInfo.title,
        description: errorInfo.description + '\n\n' + errorInfo.suggestions.join('\n'),
        variant: "destructive",
        duration: 10000, // Show longer for offline state
      });

      // Close existing connection
      if (sessionRef.current) {
        sessionRef.current = null;
      }
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConnected, toast]);

  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (networkMonitoringIntervalRef.current) {
        clearInterval(networkMonitoringIntervalRef.current);
      }
      sessionRef.current = null;
    };
  }, []);

  return (
    <div className="fixed bottom-4 right-4 flex flex-col items-end gap-4 z-50">
      {/* Tool Feedback Display */}
      {toolFeedback && (
        <div className="max-w-sm">
          <VoiceToolFeedback
            toolCall={toolFeedback.toolCall}
            result={toolFeedback.result}
            isExecuting={toolFeedback.isExecuting}
            error={toolFeedback.error}
            onDismiss={dismissToolFeedback}
          />
        </div>
      )}

      {/* Messages display */}
      {messages.length > 0 && (
        <div className="bg-background/90 backdrop-blur-sm border rounded-lg p-4 max-w-sm max-h-48 overflow-y-auto">
          <h3 className="text-sm font-medium mb-2">Voice Chat</h3>
          <div className="space-y-1 text-xs">
            {messages.map((message, index) => (
              <div key={index} className={message.startsWith('You:') ? 'text-primary' : 'text-muted-foreground'}>
                {message}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Control buttons */}
      <div className="flex flex-col gap-2">
        {!isConnected ? (
          <div className="flex flex-col gap-2">
            <Button
              onClick={startConversation}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              size="lg"
              disabled={retryCount > 0}
            >
              <Mic className="w-5 h-5 mr-2" />
              {retryCount > 0 ? `Connecting... (${retryCount})` : 'Start Voice Chat'}
            </Button>
            {retryCount > 0 && (
              <div className="text-xs text-amber-600 text-center">
                {isReconnecting ? 'Reconnecting...' : 'Connecting...'} Attempt {retryCount}/5
              </div>
            )}
            {!isOnline && (
              <div className="text-xs text-red-600 text-center font-medium">
                Network Offline - Will auto-reconnect when online
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <div className="flex gap-2">
              <Button
                onClick={endConversation}
                variant="outline"
                size="lg"
              >
                <MicOff className="w-5 h-5 mr-2" />
                End Chat
              </Button>

              {/* Connection quality indicator */}
              <div className={`flex items-center gap-2 rounded-lg px-3 py-2 text-xs font-medium ${
                connectionQuality === 'excellent' ? 'bg-green-100 text-green-800' :
                connectionQuality === 'good' ? 'bg-blue-100 text-blue-800' :
                connectionQuality === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  connectionQuality === 'excellent' ? 'bg-green-600' :
                  connectionQuality === 'good' ? 'bg-blue-600' :
                  connectionQuality === 'fair' ? 'bg-yellow-600' :
                  'bg-red-600'
                }`} />
                {connectionQuality}
              </div>
            </div>

            {(isRecording || isSpeaking || isExecutingTool) && (
              <div className="flex items-center gap-2 bg-background/90 backdrop-blur-sm border rounded-lg px-3 py-2">
                {isRecording && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-destructive rounded-full animate-pulse" />
                    <span className="text-xs">Listening</span>
                  </div>
                )}
                {isSpeaking && (
                  <div className="flex items-center gap-1">
                    <Volume2 className="w-4 h-4 text-primary" />
                    <span className="text-xs">Speaking</span>
                  </div>
                )}
                {isExecutingTool && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-spin" />
                    <span className="text-xs">Processing</span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Quick action buttons */}
        {isConnected && (
          <div className="flex flex-col gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => sendTextMessage("What is HACCP?")}
            >
              Ask about HACCP
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => sendTextMessage("Help me with food safety")}
            >
              Food Safety Help
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Manual commit - useful if VAD doesn't detect speech end
                const transport = (sessionRef.current as any)?.transport;
                if (transport) {
                  console.log('🎤 Manually committing audio buffer...');
                  transport.sendEvent({
                    type: 'input_audio_buffer.commit'
                  });
                  
                  // Also request a response
                  transport.sendEvent({
                    type: 'response.create'
                  });
                  
                  toast({
                    title: "Audio Committed",
                    description: "Processing your speech...",
                  });
                }
              }}
            >
              📤 Commit Audio (Manual)
            </Button>
            <Button
              variant={useManualMode ? "default" : "outline"}
              size="sm"
              onClick={() => setUseManualMode(!useManualMode)}
            >
              {useManualMode ? '🎯 Manual Mode ON' : '🤖 Auto Mode (VAD)'}
            </Button>
          </div>
        )}

        {/* Audio controls */}
        {isConnected && (
          <div className="bg-background/90 backdrop-blur-sm border rounded-lg p-3">
            <h4 className="text-xs font-medium mb-2">Audio Settings</h4>
            <div className="flex items-center gap-2 mb-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAudioEnabled(!audioEnabled)}
                className="h-6 px-2"
              >
                {audioEnabled ? (
                  <Volume2 className="w-3 h-3" />
                ) : (
                  <div className="w-3 h-3 relative">
                    <Volume2 className="w-3 h-3" />
                    <div className="absolute inset-0 bg-destructive/20 rounded" />
                  </div>
                )}
              </Button>
              <span className="text-xs">{audioEnabled ? 'On' : 'Off'}</span>
            </div>
            {audioEnabled && (
              <div className="flex items-center gap-2">
                <span className="text-xs">Volume:</span>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={audioVolume}
                  onChange={(e) => setAudioVolume(parseFloat(e.target.value))}
                  className="flex-1 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  aria-label="Audio volume control"
                />
                <span className="text-xs w-8">{Math.round(audioVolume * 100)}%</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default VoiceAgent;