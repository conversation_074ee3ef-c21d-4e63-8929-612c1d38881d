import React, { useC<PERSON>back, useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Mi<PERSON>, MicOff, Volume2 } from 'lucide-react';
import { playAudioData } from '@/utils/RealtimeAudio';
import VoiceToolExecutor, { Tool<PERSON><PERSON>, ToolResult } from '@/lib/voice-tool-executor';
import VoiceToolFeedback from '@/components/VoiceToolFeedback';
import { auditLogger, AuditEventType, AuditSeverity } from '@/lib/voice-audit-logger';
import { RealtimeWebRTCClient, WebRTCClientConfig } from '@/lib/realtime-webrtc-client';
import { getEphemeralKey } from '@/lib/openai-ephemeral-keys';

interface VoiceAgentProps {
  onSpeakingChange?: (speaking: boolean) => void;
}

interface ToolFeedbackState {
  toolCall: ToolCall;
  result?: ToolResult;
  isExecuting: boolean;
  error?: string;
  timestamp: number;
}

interface SessionState {
  id: string;
  startTime: number;
  messageCount: number;
  toolCallCount: number;
  errorCount: number;
  lastMessageTime: number;
}

/**
 * Generate a unique session ID for tracking
 */
function generateSessionId(): string {
  return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Calculate exponential backoff for retry
 */
function getExponentialBackoff(retryCount: number): number {
  const baseDelay = 1000; // 1 second
  const maxDelay = 30000; // 30 seconds
  return Math.min(baseDelay * Math.pow(2, retryCount), maxDelay);
}

/**
 * Determine connection quality based on latency
 */
function getConnectionQuality(latencies: number[]): 'excellent' | 'good' | 'fair' | 'poor' {
  if (latencies.length === 0) return 'excellent';
  const avgLatency = latencies.reduce((a, b) => a + b) / latencies.length;
  if (avgLatency < 50) return 'excellent';
  if (avgLatency < 100) return 'good';
  if (avgLatency < 200) return 'fair';
  return 'poor';
}

/**
 * Error types for better error messaging
 */
enum ConnectionErrorType {
  API_KEY_MISSING = 'API_KEY_MISSING',
  API_KEY_INVALID = 'API_KEY_INVALID',
  NETWORK_OFFLINE = 'NETWORK_OFFLINE',
  NETWORK_ERROR = 'NETWORK_ERROR',
  WEBSOCKET_FAILED = 'WEBSOCKET_FAILED',
  MICROPHONE_DENIED = 'MICROPHONE_DENIED',
  MAX_RETRIES_REACHED = 'MAX_RETRIES_REACHED',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Get user-friendly error message and suggestions
 */
function getErrorMessage(errorType: ConnectionErrorType, retryCount?: number, backoffMs?: number): {
  title: string;
  description: string;
  suggestions: string[];
} {
  switch (errorType) {
    case ConnectionErrorType.API_KEY_MISSING:
      return {
        title: "Configuration Error",
        description: "OpenAI API key not configured",
        suggestions: [
          "Add VITE_OPENAI_API_KEY to your .env file",
          "Get your API key from https://platform.openai.com/api-keys",
          "Restart the development server after adding the key"
        ]
      };

    case ConnectionErrorType.API_KEY_INVALID:
      return {
        title: "Authentication Failed",
        description: "Invalid OpenAI API key",
        suggestions: [
          "Verify your API key is correct in .env file",
          "Check that the key hasn't been revoked or expired",
          "Ensure you have access to GPT-4 Realtime API"
        ]
      };

    case ConnectionErrorType.NETWORK_OFFLINE:
      return {
        title: "Network Offline",
        description: "No internet connection detected",
        suggestions: [
          "Check your internet connection",
          "Verify WiFi or ethernet is connected",
          "Connection will retry automatically when network is available"
        ]
      };

    case ConnectionErrorType.NETWORK_ERROR: {
      const retryMsg = retryCount !== undefined && backoffMs !== undefined
        ? `Retrying in ${Math.ceil(backoffMs / 1000)}s (attempt ${retryCount + 1}/5)`
        : "Connection will retry automatically";
      return {
        title: "Connection Error",
        description: retryMsg,
        suggestions: [
          "Check your internet connection stability",
          "Verify firewall isn't blocking WebSocket connections",
          "Try disabling VPN if active"
        ]
      };
    }

    case ConnectionErrorType.WEBSOCKET_FAILED:
      return {
        title: "WebSocket Connection Failed",
        description: "Unable to establish real-time connection",
        suggestions: [
          "Check if api.openai.com is accessible",
          "Verify network allows WebSocket connections",
          "Check browser console for detailed error logs"
        ]
      };

    case ConnectionErrorType.MICROPHONE_DENIED:
      return {
        title: "Microphone Access Denied",
        description: "Cannot access microphone",
        suggestions: [
          "Allow microphone access in browser settings",
          "Check system microphone permissions",
          "Reload the page and accept microphone prompt"
        ]
      };

    case ConnectionErrorType.MAX_RETRIES_REACHED:
      return {
        title: "Connection Failed",
        description: "Maximum reconnection attempts reached",
        suggestions: [
          "Click the microphone button to try again",
          "Check your API key and internet connection",
          "View browser console for detailed error logs",
          "Contact support if issue persists"
        ]
      };

    default:
      return {
        title: "Unexpected Error",
        description: "An unknown error occurred",
        suggestions: [
          "Try refreshing the page",
          "Check browser console for error details",
          "Contact support if issue persists"
        ]
      };
  }
}

const VoiceAgent: React.FC<VoiceAgentProps> = ({ onSpeakingChange }) => {
  const { toast } = useToast();
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState<string[]>([]);
  const [isExecutingTool, setIsExecutingTool] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [connectionQuality, setConnectionQuality] = useState<'excellent' | 'good' | 'fair' | 'poor'>('excellent');
  const [toolFeedback, setToolFeedback] = useState<ToolFeedbackState | null>(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isReconnecting, setIsReconnecting] = useState(false);

  const webrtcClientRef = useRef<RealtimeWebRTCClient | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const toolCallBufferRef = useRef<{ name: string; arguments: string }>({ name: '', arguments: '' });
  const sessionStateRef = useRef<SessionState>({
    id: generateSessionId(),
    startTime: Date.now(),
    messageCount: 0,
    toolCallCount: 0,
    errorCount: 0,
    lastMessageTime: Date.now()
  });
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const latencyTrackingRef = useRef<number[]>([]);
  const networkMonitoringIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastPingTimeRef = useRef<number>(0);

  /**
   * Monitor network conditions (adapted for WebRTC)
   * WebRTC provides its own connection quality metrics
   */
  const monitorNetworkConditions = () => {
    if (!webrtcClientRef.current) return;

    // Calculate average latency from tracked values
    if (latencyTrackingRef.current.length > 0) {
      const avgLatency = latencyTrackingRef.current.reduce((a, b) => a + b) / latencyTrackingRef.current.length;

      // Estimate packet loss from message success/failure ratio
      const recentMessages = sessionStateRef.current.messageCount;
      const recentErrors = sessionStateRef.current.errorCount;
      const packetLoss = recentMessages > 0
        ? Math.min(recentErrors / recentMessages, 0.3)
        : 0;

      // Estimate bandwidth based on connection quality
      const quality = getConnectionQuality(latencyTrackingRef.current);
      let bandwidth = 1000000;
      if (quality === 'excellent') bandwidth = 4000000;
      else if (quality === 'good') bandwidth = 2000000;
      else if (quality === 'fair') bandwidth = 1000000;
      else bandwidth = 600000;

      // Log metrics
      console.log('Network Monitoring:', {
        avgLatency: avgLatency.toFixed(2) + 'ms',
        packetLoss: (packetLoss * 100).toFixed(1) + '%',
        bandwidth: (bandwidth / 1000).toFixed(0) + 'kbps',
        quality
      });

      setConnectionQuality(quality);
    }
  };

  const handleToolCall = async (toolCall: ToolCall) => {
    setIsExecutingTool(true);

    // Log tool request
    auditLogger.log(
      AuditEventType.TOOL_REQUESTED,
      AuditSeverity.INFO,
      `Tool requested: ${toolCall.name}`,
      { toolName: toolCall.name, arguments: toolCall.arguments }
    );

    // Initialize feedback state (executing)
    setToolFeedback({
      toolCall,
      isExecuting: true,
      timestamp: Date.now()
    });

    try {
      console.log('Executing tool:', toolCall.name, toolCall.arguments);
      sessionStateRef.current.toolCallCount++;

      const result = await VoiceToolExecutor.executeTool(toolCall);
      const feedbackMessage = VoiceToolExecutor.formatResultForVoice(result);

      // Log tool execution result
      auditLogger.logToolExecution(
        toolCall.name,
        toolCall.arguments as Record<string, unknown>,
        result.success,
        result.data as Record<string, unknown> | undefined,
        result.error
      );

      // Update feedback state with result
      setToolFeedback({
        toolCall,
        result,
        isExecuting: false,
        timestamp: Date.now()
      });

      // Add tool result message
      setMessages(prev => [...prev.slice(-9), `Tool: ${feedbackMessage}`]);

      // Send tool result back to OpenAI via WebRTC
      if (webrtcClientRef.current && isConnected) {
        try {
          if (toolCall.call_id) {
            const toolResultEvent = {
              type: 'conversation.item.create',
              item: {
                type: 'function_call_output',
                call_id: toolCall.call_id,
                output: JSON.stringify({
                  success: result.success,
                  message: result.message,
                  data: result.success ? result.data ?? null : undefined,
                  error: result.success ? undefined : result.error ?? result.message
                })
              }
            };
            webrtcClientRef.current.sendMessage(toolResultEvent);
          } else {
            // Fallback when call_id is missing – send as assistant message so conversation can continue
            webrtcClientRef.current.sendMessage({
              type: 'conversation.item.create',
              item: {
                type: 'message',
                role: 'assistant',
                content: [
                  {
                    type: 'output_text',
                    text: result.message
                  }
                ]
              }
            });
          }

          // Trigger next response from the model
          webrtcClientRef.current.sendMessage({ type: 'response.create' });
        } catch (sendError) {
          console.error('Failed to send tool result to OpenAI:', sendError);
          sessionStateRef.current.errorCount++;
        }
      }

      // Show success toast
      if (result.success) {
        toast({
          title: 'Tool Executed',
          description: feedbackMessage,
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to execute tool';
      console.error('Tool execution error:', error);

      // Log tool execution error
      auditLogger.log(
        AuditEventType.TOOL_FAILED,
        AuditSeverity.ERROR,
        `Tool execution failed: ${toolCall.name}`,
        {
          toolName: toolCall.name,
          error: errorMessage,
        }
      );

      // Update feedback state with error
      setToolFeedback({
        toolCall,
        isExecuting: false,
        error: errorMessage,
        timestamp: Date.now()
      });

      toast({
        title: 'Tool Error',
        description: errorMessage,
        variant: 'destructive',
      });
      sessionStateRef.current.errorCount++;
    } finally {
      setIsExecutingTool(false);

      // Auto-clear feedback after 5 seconds if successful
      if (toolFeedback?.result?.success) {
        setTimeout(() => {
          setToolFeedback(null);
        }, 5000);
      }
    }
  };

  const handleMessage = (event: Record<string, unknown>) => {
    console.log('Received message:', event.type);

    // Handle error messages from OpenAI
    if (event.type === 'error') {
      const errorData = event.error as Record<string, unknown>;
      console.error('❌ OpenAI Error Details:', {
        type: errorData?.type,
        code: errorData?.code,
        message: errorData?.message,
        param: errorData?.param,
        fullError: errorData
      });

      toast({
        title: "Voice Agent Error",
        description: `${errorData?.type || 'Unknown'}: ${errorData?.message || 'No details'}\n\nCode: ${errorData?.code || 'N/A'}`,
        variant: "destructive",
        duration: 15000,
      });
      sessionStateRef.current.errorCount++;
      return;
    }

    if (event.type === 'response.audio.delta') {
      // Convert base64 to Uint8Array and play
      try {
        const delta = event.delta as string;
        const binaryString = atob(delta);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        if (audioContextRef.current) {
          playAudioData(audioContextRef.current, bytes);
        }

        if (!isSpeaking) {
          setIsSpeaking(true);
          onSpeakingChange?.(true);
        }
      } catch (error) {
        console.error('Error playing audio:', error);
      }
    } else if (event.type === 'response.audio.done') {
      setIsSpeaking(false);
      onSpeakingChange?.(false);
    } else if (event.type === 'response.audio_transcript.delta') {
      // Handle text transcripts
      const transcript = event.delta as string;
      setMessages(prev => {
        const newMessages = [...prev];
        if (newMessages.length > 0 && newMessages[newMessages.length - 1].startsWith('AI: ')) {
          newMessages[newMessages.length - 1] += transcript;
        } else {
          newMessages.push('AI: ' + transcript);
        }
        return newMessages.slice(-10); // Keep last 10 messages
      });
    } else if (event.type === 'response.function_call_arguments.delta') {
      // Handle tool call arguments being streamed
      if (event.name) {
        toolCallBufferRef.current.name = event.name as string;
      }
      if (event.delta) {
        toolCallBufferRef.current.arguments += event.delta as string;
      }
    } else if (event.type === 'response.function_call_arguments.done') {
      // Tool call is complete, execute it
      try {
        const toolCall: ToolCall = {
          name: toolCallBufferRef.current.name,
          arguments: JSON.parse(toolCallBufferRef.current.arguments || '{}'),
          call_id: event.call_id as string | undefined
        };

        // Clear buffer
        toolCallBufferRef.current = { name: '', arguments: '' };

        // Execute tool asynchronously
        handleToolCall(toolCall);
      } catch (error) {
        console.error('Failed to parse tool arguments:', error);
      }
    } else if (event.type === 'input_audio_buffer.speech_started') {
      console.log('User started speaking');
    } else if (event.type === 'input_audio_buffer.speech_stopped') {
      console.log('User stopped speaking');
    }
  };

  const startConversation = async () => {
    try {
      // Initialize audio context for playback
      audioContextRef.current = new AudioContext({ sampleRate: 24000 });

      // Reset session state
      sessionStateRef.current = {
        id: generateSessionId(),
        startTime: Date.now(),
        messageCount: 0,
        toolCallCount: 0,
        errorCount: 0,
        lastMessageTime: Date.now()
      };

      // Initialize audit logging for this session
      auditLogger.initSession(sessionStateRef.current.id);

      // Connect via WebRTC
      await connectWebRTC(0);
    } catch (error) {
      console.error('Error starting conversation:', error);
      sessionStateRef.current.errorCount++;

      auditLogger.log(
        AuditEventType.SESSION_ENDED,
        AuditSeverity.ERROR,
        'Failed to start voice session',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to start conversation',
        variant: "destructive",
      });
    }
  };

  /**
   * Connect to OpenAI via WebRTC with retry logic
   */
  const connectWebRTC = async (retryAttempt: number) => {
    try {
      console.log('🔌 Starting WebRTC connection (attempt ' + (retryAttempt + 1) + ')');

      // 1. Get ephemeral key from backend
      const ephemeralKey = await getEphemeralKey();
      console.log('✅ Ephemeral key obtained');

      // 2. Track connection start time for latency measurement
      const connectionStartTime = Date.now();

      // 3. Create WebRTC client
      const webrtcConfig: WebRTCClientConfig = {
        ephemeralKey,
        model: 'gpt-4o-realtime-preview-2024-12-17',
        voice: 'alloy',
        onMessage: handleMessage,
        onAudioData: (audioData) => {
          if (audioContextRef.current) {
            playAudioData(audioContextRef.current, audioData);
          }
        },
        onError: (error) => {
          console.error('❌ WebRTC error:', error);
          sessionStateRef.current.errorCount++;

          // Handle reconnection
          if (retryAttempt < 5 && navigator.onLine) {
            const backoffDelay = getExponentialBackoff(retryAttempt);
            setRetryCount(retryAttempt + 1);
            setIsReconnecting(true);

            reconnectTimeoutRef.current = setTimeout(() => {
              connectWebRTC(retryAttempt + 1);
            }, backoffDelay);
          }
        },
        onConnectionStateChange: (state) => {
          console.log('🔌 Connection state:', state);

          if (state === 'connected') {
            // Track connection latency
            const latency = Date.now() - connectionStartTime;
            latencyTrackingRef.current.push(latency);
            if (latencyTrackingRef.current.length > 100) {
              latencyTrackingRef.current.shift();
            }

            const quality = getConnectionQuality(latencyTrackingRef.current);
            setConnectionQuality(quality);

            setIsConnected(true);
            setIsRecording(true);  // WebRTC handles audio automatically
            setRetryCount(0);
            setIsReconnecting(false);

            toast({
              title: "Connected",
              description: `Voice agent ready via WebRTC (${quality} connection)`,
            });

            // Start network monitoring
            if (networkMonitoringIntervalRef.current) {
              clearInterval(networkMonitoringIntervalRef.current);
            }
            networkMonitoringIntervalRef.current = setInterval(monitorNetworkConditions, 2000);
            console.log('✅ Network monitoring started');
          } else if (state === 'disconnected' || state === 'failed') {
            setIsConnected(false);
            setIsRecording(false);

            // Stop network monitoring
            if (networkMonitoringIntervalRef.current) {
              clearInterval(networkMonitoringIntervalRef.current);
              networkMonitoringIntervalRef.current = null;
            }
          }
        }
      };

      webrtcClientRef.current = new RealtimeWebRTCClient(webrtcConfig);

      // 4. Connect
      await webrtcClientRef.current.connect();

    } catch (error) {
      console.error('❌ WebRTC connection failed:', error);
      sessionStateRef.current.errorCount++;

      // Show user-friendly error
      let errorType = ConnectionErrorType.UNKNOWN;
      if (error instanceof Error) {
        if (error.message.includes('ephemeral key')) {
          errorType = ConnectionErrorType.API_KEY_INVALID;
        } else if (error.message.includes('microphone') || error.message.includes('getUserMedia')) {
          errorType = ConnectionErrorType.MICROPHONE_DENIED;
        } else if (!navigator.onLine) {
          errorType = ConnectionErrorType.NETWORK_OFFLINE;
        }
      }

      const errorInfo = getErrorMessage(errorType, retryAttempt);
      toast({
        title: errorInfo.title,
        description: errorInfo.description + '\n\nSuggestions:\n' + errorInfo.suggestions.join('\n'),
        variant: "destructive",
        duration: 10000,
      });

      // Retry logic
      if (retryAttempt < 5 && navigator.onLine) {
        const backoffDelay = getExponentialBackoff(retryAttempt);
        setRetryCount(retryAttempt + 1);
        setIsReconnecting(true);

        reconnectTimeoutRef.current = setTimeout(() => {
          connectWebRTC(retryAttempt + 1);
        }, backoffDelay);
      }
    }
  };

  /**
   * Note: WebRTC handles audio recording automatically via MediaStream
   * No separate startRecording function needed
   */

  const endConversation = () => {
    // Clear reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Clear network monitoring interval
    if (networkMonitoringIntervalRef.current) {
      clearInterval(networkMonitoringIntervalRef.current);
      networkMonitoringIntervalRef.current = null;
    }

    // Disconnect WebRTC client
    webrtcClientRef.current?.disconnect();
    webrtcClientRef.current = null;

    // Close audio context
    audioContextRef.current?.close();

    // Log session summary
    const duration = Date.now() - sessionStateRef.current.startTime;
    console.log('Session ended', {
      sessionId: sessionStateRef.current.id,
      duration: `${duration}ms`,
      messages: sessionStateRef.current.messageCount,
      toolCalls: sessionStateRef.current.toolCallCount,
      errors: sessionStateRef.current.errorCount
    });

    // Log session end to audit logger
    auditLogger.log(
      AuditEventType.SESSION_ENDED,
      sessionStateRef.current.errorCount > 5 ? AuditSeverity.WARNING : AuditSeverity.INFO,
      'Voice session ended',
      {
        duration,
        messages: sessionStateRef.current.messageCount,
        toolCalls: sessionStateRef.current.toolCallCount,
        errors: sessionStateRef.current.errorCount,
      }
    );

    setIsConnected(false);
    setIsRecording(false);
    setIsSpeaking(false);
    setRetryCount(0);
    setToolFeedback(null);
    onSpeakingChange?.(false);

    toast({
      title: "Disconnected",
      description: "Voice conversation ended",
    });
  };

  const dismissToolFeedback = () => {
    setToolFeedback(null);
  };

  const sendTextMessage = (text: string) => {
    if (!webrtcClientRef.current || !isConnected) {
      toast({
        title: "Not Connected",
        description: "Please start the conversation first",
        variant: "destructive",
      });
      return;
    }

    try {
      const event = {
        type: 'conversation.item.create',
        item: {
          type: 'message',
          role: 'user',
          content: [
            {
              type: 'input_text',
              text
            }
          ]
        }
      };

      webrtcClientRef.current.sendMessage(event);
      webrtcClientRef.current.sendMessage({ type: 'response.create' });

      setMessages(prev => [...prev.slice(-9), `You: ${text}`]);
      sessionStateRef.current.messageCount++;
    } catch (error) {
      console.error('Failed to send text message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
    }
  };

  /**
   * Monitor network online/offline status
   */
  useEffect(() => {
    const handleOnline = () => {
      console.log('Network came back online');
      setIsOnline(true);

      // Auto-reconnect if we were previously connected
      if (!isConnected && !webrtcClientRef.current) {
        console.log('Auto-reconnecting after network restoration...');
        setIsReconnecting(true);
        toast({
          title: "Network Restored",
          description: "Reconnecting to voice service...",
        });

        // Reset retry count and attempt connection
        setRetryCount(0);
        startConversation();
      }
    };

    const handleOffline = () => {
      console.log('Network went offline');
      setIsOnline(false);

      // Show offline message with actionable suggestions
      const errorInfo = getErrorMessage(ConnectionErrorType.NETWORK_OFFLINE);
      toast({
        title: errorInfo.title,
        description: errorInfo.description + '\n\n' + errorInfo.suggestions.join('\n'),
        variant: "destructive",
        duration: 10000, // Show longer for offline state
      });

      // Close existing connection
      if (webrtcClientRef.current) {
        webrtcClientRef.current.disconnect();
      }
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConnected, toast]);

  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (networkMonitoringIntervalRef.current) {
        clearInterval(networkMonitoringIntervalRef.current);
      }
      webrtcClientRef.current?.disconnect();
      audioContextRef.current?.close();
    };
  }, []);

  return (
    <div className="fixed bottom-4 right-4 flex flex-col items-end gap-4 z-50">
      {/* Tool Feedback Display */}
      {toolFeedback && (
        <div className="max-w-sm">
          <VoiceToolFeedback
            toolCall={toolFeedback.toolCall}
            result={toolFeedback.result}
            isExecuting={toolFeedback.isExecuting}
            error={toolFeedback.error}
            onDismiss={dismissToolFeedback}
          />
        </div>
      )}

      {/* Messages display */}
      {messages.length > 0 && (
        <div className="bg-background/90 backdrop-blur-sm border rounded-lg p-4 max-w-sm max-h-48 overflow-y-auto">
          <h3 className="text-sm font-medium mb-2">Voice Chat</h3>
          <div className="space-y-1 text-xs">
            {messages.map((message, index) => (
              <div key={index} className={message.startsWith('You:') ? 'text-primary' : 'text-muted-foreground'}>
                {message}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Control buttons */}
      <div className="flex flex-col gap-2">
        {!isConnected ? (
          <div className="flex flex-col gap-2">
            <Button
              onClick={startConversation}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              size="lg"
              disabled={retryCount > 0}
            >
              <Mic className="w-5 h-5 mr-2" />
              {retryCount > 0 ? `Connecting... (${retryCount})` : 'Start Voice Chat'}
            </Button>
            {retryCount > 0 && (
              <div className="text-xs text-amber-600 text-center">
                {isReconnecting ? 'Reconnecting...' : 'Connecting...'} Attempt {retryCount}/5
              </div>
            )}
            {!isOnline && (
              <div className="text-xs text-red-600 text-center font-medium">
                Network Offline - Will auto-reconnect when online
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <div className="flex gap-2">
              <Button
                onClick={endConversation}
                variant="outline"
                size="lg"
              >
                <MicOff className="w-5 h-5 mr-2" />
                End Chat
              </Button>

              {/* Connection quality indicator */}
              <div className={`flex items-center gap-2 rounded-lg px-3 py-2 text-xs font-medium ${
                connectionQuality === 'excellent' ? 'bg-green-100 text-green-800' :
                connectionQuality === 'good' ? 'bg-blue-100 text-blue-800' :
                connectionQuality === 'fair' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  connectionQuality === 'excellent' ? 'bg-green-600' :
                  connectionQuality === 'good' ? 'bg-blue-600' :
                  connectionQuality === 'fair' ? 'bg-yellow-600' :
                  'bg-red-600'
                }`} />
                {connectionQuality}
              </div>
            </div>

            {(isRecording || isSpeaking || isExecutingTool) && (
              <div className="flex items-center gap-2 bg-background/90 backdrop-blur-sm border rounded-lg px-3 py-2">
                {isRecording && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-destructive rounded-full animate-pulse" />
                    <span className="text-xs">Listening</span>
                  </div>
                )}
                {isSpeaking && (
                  <div className="flex items-center gap-1">
                    <Volume2 className="w-4 h-4 text-primary" />
                    <span className="text-xs">Speaking</span>
                  </div>
                )}
                {isExecutingTool && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-spin" />
                    <span className="text-xs">Processing</span>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Quick action buttons */}
        {isConnected && (
          <div className="flex flex-col gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => sendTextMessage("What is HACCP?")}
            >
              Ask about HACCP
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => sendTextMessage("Help me with food safety")}
            >
              Food Safety Help
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default VoiceAgent;