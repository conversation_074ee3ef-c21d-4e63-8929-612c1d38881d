import { useEffect, useState } from 'react';
import { sqliteService, StagedInventory } from '@/lib/sqlite-service';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export function VoiceInventoryRecords() {
  const [records, setRecords] = useState<StagedInventory[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchRecords = async () => {
    setLoading(true);
    try {
      const voiceRecords = await sqliteService.getVoiceInventoryRecords(20);
      setRecords(voiceRecords);
    } catch (error) {
      console.error('Failed to fetch voice inventory records:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecords();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Voice Agent Inventory Records</CardTitle>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchRecords}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        {records.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No voice agent records found. Create some by using the voice assistant!
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product ID</TableHead>
                  <TableHead>Batch Number</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {records.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell className="font-mono text-sm">
                      {record.product_id}
                    </TableCell>
                    <TableCell className="font-medium">
                      {record.batch_number}
                    </TableCell>
                    <TableCell>
                      {record.quantity} {record.unit || 'units'}
                    </TableCell>
                    <TableCell>{record.location || '—'}</TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {record.created_at 
                        ? formatDistanceToNow(new Date(record.created_at), { addSuffix: true })
                        : '—'
                      }
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        record.synced_to_supabase 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {record.synced_to_supabase ? 'Synced' : 'Pending'}
                      </span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}