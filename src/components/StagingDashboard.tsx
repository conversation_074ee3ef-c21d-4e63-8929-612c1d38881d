import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Database,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  TrendingUp,
  Package,
  Thermometer,
  FileText,
  Bug,
  BarChart3
} from 'lucide-react';
import { useSQLiteStaging } from '@/hooks/use-sqlite-staging';
import { StagingConfirmationDialog } from './StagingConfirmationDialog';

interface StagingDashboardProps {
  className?: string;
}

export function StagingDashboard({ className }: StagingDashboardProps) {
  const {
    getStats,
    startSync,
    stopSync,
    isSyncRunning,
    getSyncStatus
  } = useSQLiteStaging();

  const [stats, setStats] = useState<{
    products: number;
    inventory: number;
    ccp_monitoring: number;
    temperature_readings: number;
    quality_tests: number;
    allergen_tests: number;
    haccp_events: number;
    pending_sync: number;
    pending_confirmations: number;
  } | null>(null);
  const [syncStatus, setSyncStatus] = useState<{
    isRunning: boolean;
    pendingRecords: number;
    pendingConfirmations: number;
  } | null>(null);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  useEffect(() => {
    loadData();

    // Refresh data every 30 seconds
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      const [statsData, syncStatusData] = await Promise.all([
        getStats(),
        getSyncStatus()
      ]);

      setStats(statsData);
      setSyncStatus(syncStatusData);
    } catch (error) {
      console.error('Failed to load staging data:', error);
    }
  };

  const handleStartSync = async () => {
    try {
      await startSync({
        confirmBeforeSync: true,
        batchSize: 5
      });
      setLastSyncTime(new Date());
      await loadData(); // Refresh data after sync
    } catch (error) {
      console.error('Failed to start sync:', error);
      alert('Failed to start sync. Please try again.');
    }
  };

  const handleStopSync = async () => {
    try {
      await stopSync();
      await loadData(); // Refresh data after stopping sync
    } catch (error) {
      console.error('Failed to stop sync:', error);
    }
  };

  if (!stats || !syncStatus) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading staging data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalRecords = stats.products + stats.inventory + stats.ccp_monitoring +
                      stats.temperature_readings + stats.quality_tests +
                      stats.allergen_tests + stats.haccp_events;

  const syncProgress = totalRecords > 0 ? ((totalRecords - stats.pending_sync) / totalRecords) * 100 : 100;

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Data Staging Dashboard
          </CardTitle>
          <CardDescription>
            Manage your offline data staging and synchronization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Sync Status */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              {isSyncRunning ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="font-medium">Sync in Progress</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="font-medium">Ready</span>
                </div>
              )}
              {lastSyncTime && (
                <span className="text-sm text-gray-600">
                  Last sync: {lastSyncTime.toLocaleTimeString()}
                </span>
              )}
            </div>
            <div className="flex gap-2">
              {!isSyncRunning ? (
                <Button onClick={handleStartSync} size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Start Sync
                </Button>
              ) : (
                <Button onClick={handleStopSync} variant="outline" size="sm">
                  Stop Sync
                </Button>
              )}
              <Button
                onClick={() => setShowConfirmationDialog(true)}
                variant="outline"
                size="sm"
              >
                Review ({stats.pending_confirmations})
              </Button>
            </div>
          </div>

          {/* Overall Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Sync Progress</span>
              <span>{Math.round(syncProgress)}%</span>
            </div>
            <Progress value={syncProgress} className="h-2" />
          </div>

          {/* Statistics Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Package className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <div className="text-2xl font-bold text-blue-900">{stats.products}</div>
              <div className="text-sm text-blue-700">Products</div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <div className="text-2xl font-bold text-green-900">{stats.inventory}</div>
              <div className="text-sm text-green-700">Inventory Items</div>
            </div>

            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <Thermometer className="h-8 w-8 mx-auto mb-2 text-orange-600" />
              <div className="text-2xl font-bold text-orange-900">{stats.temperature_readings}</div>
              <div className="text-sm text-orange-700">Temp Readings</div>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <FileText className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <div className="text-2xl font-bold text-purple-900">{stats.ccp_monitoring}</div>
              <div className="text-sm text-purple-700">CCP Records</div>
            </div>
          </div>

          {/* Detailed Breakdown */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Detailed Breakdown
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Quality Tests</span>
                  <Badge variant="outline">{stats.quality_tests}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Allergen Tests</span>
                  <Badge variant="outline">{stats.allergen_tests}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">HACCP Events</span>
                  <Badge variant="outline">{stats.haccp_events}</Badge>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Pending Sync</span>
                  <Badge variant={stats.pending_sync > 0 ? "destructive" : "secondary"}>
                    {stats.pending_sync}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Need Confirmation</span>
                  <Badge variant={stats.pending_confirmations > 0 ? "destructive" : "secondary"}>
                    {stats.pending_confirmations}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total Records</span>
                  <Badge variant="outline">{totalRecords}</Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Alerts */}
          {(stats.pending_confirmations > 0 || stats.pending_sync > 0) && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h5 className="font-medium text-yellow-800">Attention Required</h5>
                  <p className="text-sm text-yellow-700 mt-1">
                    {stats.pending_confirmations > 0 && (
                      <span>You have {stats.pending_confirmations} items requiring confirmation. </span>
                    )}
                    {stats.pending_sync > 0 && (
                      <span>{stats.pending_sync} items are pending synchronization.</span>
                    )}
                  </p>
                  <Button
                    size="sm"
                    className="mt-2"
                    onClick={() => setShowConfirmationDialog(true)}
                  >
                    Review Items
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex gap-2 pt-4 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowConfirmationDialog(true)}
              className="flex-1"
            >
              <Clock className="h-4 w-4 mr-2" />
              Review Confirmations
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleStartSync}
              disabled={isSyncRunning}
              className="flex-1"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {isSyncRunning ? 'Syncing...' : 'Sync All'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <StagingConfirmationDialog
        open={showConfirmationDialog}
        onOpenChange={setShowConfirmationDialog}
      />
    </>
  );
}