# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/543c5360-1da4-4e5d-8c88-f2c28d2bd685

Contributor onboarding? See `AGENTS.md` for repository guidelines, coding standards, and release expectations.

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/543c5360-1da4-4e5d-8c88-f2c28d2bd685) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- SQLite (sql.js) - Browser-based database

## Features

### Voice Integration
- **Real-time voice commands** - Control the app using natural speech via OpenAI Realtime API
- **Voice data visibility** - Track and monitor voice agent activity
  - Real-time console logging with structured output
  - UI component for viewing voice-created records  
  - Filter records by voice agent source
  - Monitor sync status and timestamps
- **Tool calling** - Execute inventory operations, compliance checks, and monitoring tasks
- **Error handling** - Comprehensive error recovery and validation


## Local Supabase (Seafood Manager)

The staging data in SQLite can be synchronized to the Seafood Manager Supabase instance that runs locally via Docker. The project assumes the local Supabase environment from `~/Dev/Seafood-Manager` is running.

1. **Start Supabase** (if it is not already running):
   ```bash
   cd ~/Dev/Seafood-Manager
   supabase start
   ```
   The local API URL is `http://127.0.0.1:55431` and uses the standard development anon key.

2. **Configure environment** for this app (one time):
   ```bash
   cp .env.example .env
   ```
   Adjust the values if your Supabase instance listens on different ports or uses different keys.

3. **Run the frontend** from this repository:
   ```bash
   npm install
   npm run dev
   ```

With Supabase running, the sync service (`src/lib/sync-service.ts`) can push staged records from the in-browser SQLite store into the Seafood Manager database.

## Database Migration to SQLite (October 2025)

This application has been migrated from Supabase to SQLite for local data storage and offline capability. 

### Key Changes:

**Database Architecture:**
- **From:** Supabase (PostgreSQL) cloud database
- **To:** SQLite running in browser via sql.js
- **Benefits:** Offline capability, no external dependencies, local data staging

**Implementation Details:**
- SQLite service (`src/lib/sqlite-service.ts`) handles all database operations
- Schema loaded from `/public/sqlite-schema.sql` on app initialization  
- App.tsx includes database initialization with loading states and error handling
- Components updated to use SQLite instead of Supabase client

**Database Schema:**
- Staged tables for products, inventory, CCP monitoring, temperature readings
- Quality tests, allergen tests, and HACCP events
- Sync status tracking for future cloud synchronization
- Confirmation queue for data validation workflow

**Files Modified:**
- `src/App.tsx` - Added SQLite initialization
- `src/components/HaccpEventForm.tsx` - Updated to use SQLite service
- `src/lib/sqlite-service.ts` - Complete SQLite interface
- `public/sqlite-schema.sql` - Database schema definition

**Testing:**
- Development server: ✅ Working
- Production build: ✅ Working  
- Database operations: ✅ Functional
- Type safety: ✅ Improved

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/543c5360-1da4-4e5d-8c88-f2c28d2bd685) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
