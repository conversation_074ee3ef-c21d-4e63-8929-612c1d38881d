-- SQLite Staging Database Schema for Safe Catch Flow
-- This schema mirrors key tables from Supabase for offline data staging

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Core Products table (staging version)
CREATE TABLE IF NOT EXISTS staged_products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT,
    subcategory TEXT,
    origin_country TEXT,
    supplier_id TEXT,
    unit_price REAL DEFAULT 0,
    min_stock REAL DEFAULT 0,
    current_stock REAL DEFAULT 0,
    storage_temp_min REAL,
    storage_temp_max REAL,
    handling_instructions TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    sync_confirmed BOOLEAN DEFAULT FALSE,
    created_by TEXT,
    UNIQUE(id)
);

-- Inventory/Batch tracking (staging version)
CREATE TABLE IF NOT EXISTS staged_inventory (
    id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    batch_number TEXT NOT NULL,
    quantity REAL NOT NULL DEFAULT 0,
    unit TEXT,
    expiry_date DATE,
    received_date DATE NOT NULL,
    vendor_id TEXT,
    quality_status TEXT DEFAULT 'pending',
    temperature REAL,
    location TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    sync_confirmed BOOLEAN DEFAULT FALSE,
    created_by TEXT,
    FOREIGN KEY (product_id) REFERENCES staged_products(id) ON DELETE CASCADE,
    UNIQUE(product_id, batch_number)
);

-- Critical Control Points monitoring (staging version)
CREATE TABLE IF NOT EXISTS staged_ccp_monitoring (
    id TEXT PRIMARY KEY,
    ccp_name TEXT NOT NULL,
    product_id TEXT,
    batch_number TEXT,
    measurement_value REAL NOT NULL,
    measurement_unit TEXT NOT NULL,
    critical_limit_min REAL,
    critical_limit_max REAL,
    is_within_limits BOOLEAN,
    monitoring_datetime DATETIME NOT NULL,
    monitored_by TEXT NOT NULL,
    equipment_used TEXT,
    observations TEXT,
    corrective_action_needed BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    sync_confirmed BOOLEAN DEFAULT FALSE,
    created_by TEXT,
    FOREIGN KEY (product_id) REFERENCES staged_products(id) ON DELETE CASCADE,
    UNIQUE(id)
);

-- Temperature monitoring (staging version)
CREATE TABLE IF NOT EXISTS staged_temperature_readings (
    id TEXT PRIMARY KEY,
    sensor_id TEXT NOT NULL,
    storage_area_id TEXT,
    temp_celsius REAL NOT NULL,
    temp_fahrenheit REAL NOT NULL,
    humidity REAL,
    recorded_at DATETIME NOT NULL,
    within_safe_range BOOLEAN NOT NULL,
    violation_type TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    sync_confirmed BOOLEAN DEFAULT FALSE,
    created_by TEXT,
    UNIQUE(id)
);

-- Quality testing results (staging version)
CREATE TABLE IF NOT EXISTS staged_quality_tests (
    id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    batch_number TEXT,
    test_type TEXT NOT NULL,
    test_date DATE NOT NULL,
    laboratory TEXT,
    result TEXT NOT NULL,
    quantitative_result REAL,
    detection_limit REAL,
    compliant BOOLEAN NOT NULL,
    certificate_number TEXT,
    test_method TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    sync_confirmed BOOLEAN DEFAULT FALSE,
    created_by TEXT,
    FOREIGN KEY (product_id) REFERENCES staged_products(id) ON DELETE CASCADE,
    UNIQUE(id)
);

-- Allergen testing (staging version)
CREATE TABLE IF NOT EXISTS staged_allergen_tests (
    id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    lot_id TEXT NOT NULL,
    allergen_type TEXT NOT NULL,
    test_date DATE NOT NULL,
    laboratory TEXT NOT NULL,
    test_method TEXT NOT NULL,
    result TEXT NOT NULL,
    quantitative_result REAL,
    detection_limit REAL,
    compliant BOOLEAN NOT NULL,
    certificate_number TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    sync_confirmed BOOLEAN DEFAULT FALSE,
    created_by TEXT,
    FOREIGN KEY (product_id) REFERENCES staged_products(id) ON DELETE CASCADE,
    UNIQUE(id)
);

-- HACCP Events/Non-conformances (staging version)
CREATE TABLE IF NOT EXISTS staged_haccp_events (
    id TEXT PRIMARY KEY,
    event_type TEXT NOT NULL,
    product_id TEXT,
    batch_number TEXT,
    event_datetime DATETIME NOT NULL,
    description TEXT NOT NULL,
    severity TEXT NOT NULL,
    reported_by TEXT NOT NULL,
    immediate_action TEXT,
    root_cause TEXT,
    corrective_action TEXT,
    preventive_measures TEXT,
    status TEXT DEFAULT 'open',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_to_supabase BOOLEAN DEFAULT FALSE,
    sync_confirmed BOOLEAN DEFAULT FALSE,
    created_by TEXT,
    FOREIGN KEY (product_id) REFERENCES staged_products(id) ON DELETE CASCADE,
    UNIQUE(id)
);

-- Sync status tracking
CREATE TABLE IF NOT EXISTS sync_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT NOT NULL,
    record_id TEXT NOT NULL,
    operation TEXT NOT NULL, -- 'insert', 'update', 'delete'
    status TEXT NOT NULL, -- 'pending', 'syncing', 'completed', 'failed'
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    UNIQUE(table_name, record_id, operation)
);

-- Data confirmation queue
CREATE TABLE IF NOT EXISTS confirmation_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    record_type TEXT NOT NULL, -- 'product', 'inventory', 'ccp_monitoring', etc.
    record_id TEXT NOT NULL,
    data JSON NOT NULL, -- Complete record data as JSON
    requires_confirmation BOOLEAN DEFAULT TRUE,
    confirmed BOOLEAN DEFAULT FALSE,
    confirmed_by TEXT,
    confirmed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(record_type, record_id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_staged_products_supplier ON staged_products(supplier_id);
CREATE INDEX IF NOT EXISTS idx_staged_inventory_product ON staged_inventory(product_id);
CREATE INDEX IF NOT EXISTS idx_staged_inventory_batch ON staged_inventory(batch_number);
CREATE INDEX IF NOT EXISTS idx_staged_ccp_product ON staged_ccp_monitoring(product_id);
CREATE INDEX IF NOT EXISTS idx_staged_ccp_datetime ON staged_ccp_monitoring(monitoring_datetime);
CREATE INDEX IF NOT EXISTS idx_staged_temp_sensor ON staged_temperature_readings(sensor_id);
CREATE INDEX IF NOT EXISTS idx_staged_temp_datetime ON staged_temperature_readings(recorded_at);
CREATE INDEX IF NOT EXISTS idx_staged_quality_product ON staged_quality_tests(product_id);
CREATE INDEX IF NOT EXISTS idx_staged_quality_batch ON staged_quality_tests(batch_number);
CREATE INDEX IF NOT EXISTS idx_staged_allergen_product ON staged_allergen_tests(product_id);
CREATE INDEX IF NOT EXISTS idx_staged_allergen_lot ON staged_allergen_tests(lot_id);
CREATE INDEX IF NOT EXISTS idx_staged_haccp_product ON staged_haccp_events(product_id);
CREATE INDEX IF NOT EXISTS idx_staged_haccp_datetime ON staged_haccp_events(event_datetime);
CREATE INDEX IF NOT EXISTS idx_sync_status_table ON sync_status(table_name, status);
CREATE INDEX IF NOT EXISTS idx_confirmation_queue_type ON confirmation_queue(record_type, confirmed);