# Repository Guidelines

## Project Structure & Module Organization
- `src/` contains the Vite React app. Organize cross-cutting UI in `src/components`, hooks in `src/hooks`, and domain helpers under `src/lib` or `src/utils`.
- Route-level views live in `src/pages`; keep entry files slim and delegate logic to hooks/helpers.
- Static assets (favicons, manifest, images) belong in `public/`. Tailwind and Vite configs stay at the root next to `tailwind.config.ts` and `vite.config.ts`.
- Supabase edge functions and configuration live under `supabase/`; keep backend logic there instead of the client bundle.

## Build, Test, and Development Commands
- `npm run dev` — launch the Vite dev server at http://localhost:5173 with hot reload.
- `npm run build` — generate an optimized production bundle inside `dist/`.
- `npm run build:dev` — build with development toggles for staging verification.
- `npm run preview` — serve the latest build output for QA checks.
- `npm run lint` — run ESLint via `eslint.config.js` across TS/JSX/config files.

## Coding Style & Naming Conventions
- Use TypeScript and functional React patterns; favor hooks over class components.
- Stick to 2-space indentation. Exported components use PascalCase (`CustomerList.tsx`); hooks use camelCase (`useInventory`).
- Compose Tailwind classes from layout → spacing → color. Prefer `class-variance-authority` with `tailwind-merge` for complex variants.

## Testing Guidelines
- Automated tests are not yet wired. Add colocated specs as `<Component>.test.tsx` when introducing coverage.
- Manually exercise critical flows through `npm run dev`. Validate Supabase functions locally with `supabase functions serve` before shipping.

## Commit & Pull Request Guidelines
- Write compact, imperative commit subjects under ~70 chars (e.g., `Add voice agent integration`).
- PRs should summarize the change, include screenshots or GIFs if the UI shifts, and link any tracked issues.
- Before requesting review, run `npm run lint` and complete a fresh `npm run build`.

## Supabase & Environment Notes
- Load secrets from `.env` files consumed by `src/integrations`; never commit real keys. Update `.env.example` when adding variables.
- Keep database or third-party keys out of client code. Prefer server-only usage via Supabase edge functions.
