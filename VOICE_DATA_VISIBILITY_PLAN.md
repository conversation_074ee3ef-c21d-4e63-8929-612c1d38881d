# Voice Data Visibility Implementation Plan

**Project:** Voice Integration Connection Fix  
**Project ID:** 0dc3bec8-c768-4671-83b8-764e9d0b782e  
**Feature:** voice-data-visibility  
**Status:** Ready for Implementation

---

## Executive Summary

This document provides detailed implementation specifications for three voice data visibility enhancements requested by the user:

1. **Database Query Method** - Add `getVoiceInventoryRecords()` to sqlite-service.ts
2. **Enhanced Logging** - Add console output when voice agent creates records
3. **UI Component** - Create VoiceInventoryRecords component for viewing voice-created data

All changes follow the existing codebase patterns and maintain compatibility with the SQLite staging workflow.

---

## Architecture Overview

### Current System
- **Voice Agent** → Creates records via `voice-tool-executor.ts`
- **SQLite Service** → Manages staging tables
- **Staging Dashboard** → Shows all staged records
- **Identifier** → Records tagged with `created_by: 'voice-agent'`

### Enhanced System
```
Voice Agent (creates) → SQLite Service (stores) → Enhanced Logging (displays)
                                                          ↓
                                                   New Query Method
                                                          ↓
                                            VoiceInventoryRecords Component
```

---

## Phase 1: Backend Enhancements

### Task 1: Add getVoiceInventoryRecords() Method
**Archon Task ID:** 067229ac-ebfb-46fd-b8c4-37d550da6942  
**Order:** 114  
**File:** `src/lib/sqlite-service.ts`  
**Location:** After line 768 (after `getAllCCPMonitoring()`)

#### Implementation Specification

```typescript
/**
 * Get inventory records created by voice agent
 * @param limit Maximum number of records to return (default: 20)
 * @returns Array of StagedInventory records created by voice agent
 */
async getVoiceInventoryRecords(limit: number = 20): Promise<StagedInventory[]> {
  const { db } = this.ensureDb();
  
  const results = db.exec(
    `SELECT * FROM staged_inventory 
     WHERE created_by = 'voice-agent' 
     ORDER BY created_at DESC 
     LIMIT ?`,
    [limit]
  );
  
  if (results.length > 0) {
    const columns = results[0].columns;
    return results[0].values.map((row: any[]) => {
      const inventory: any = {};
      columns.forEach((col: string, index: number) => {
        inventory[col] = row[index];
      });
      return inventory as StagedInventory;
    });
  }
  return [];
}

/**
 * Get CCP monitoring records created by voice agent
 * @param limit Maximum number of records to return (default: 20)
 * @returns Array of StagedCCPMonitoring records created by voice agent
 */
async getVoiceCCPRecords(limit: number = 20): Promise<StagedCCPMonitoring[]> {
  const { db } = this.ensureDb();
  
  const results = db.exec(
    `SELECT * FROM staged_ccp_monitoring 
     WHERE created_by = 'voice-agent' 
     ORDER BY created_at DESC 
     LIMIT ?`,
    [limit]
  );
  
  if (results.length > 0) {
    const columns = results[0].columns;
    return results[0].values.map((row: any[]) => {
      const ccp: any = {};
      columns.forEach((col: string, index: number) => {
        ccp[col] = row[index];
      });
      return ccp as StagedCCPMonitoring;
    });
  }
  return [];
}
```

#### Key Features
- Filters by `created_by = 'voice-agent'`
- Sorted by most recent first (`created_at DESC`)
- Configurable limit (default 20)
- Returns typed arrays matching existing patterns
- Includes both inventory and CCP monitoring methods

---

### Task 2: Enhance Voice Tool Executor Logging
**Archon Task ID:** b48fdd2e-c939-4464-92d6-e1c358fbd57f  
**Order:** 112  
**File:** `src/lib/voice-tool-executor.ts`

#### Implementation Specification

##### Location 1: `addInventoryEvent()` method (after line 158)

```typescript
// After: const inventoryId = await sqliteService.createInventory(inventoryData);
// Add:

console.log('✅ Voice Agent: Inventory Record Created');
console.table({
  'Record ID': inventoryId,
  'Product ID': product_id,
  'Batch Number': batch_number,
  'Quantity': `${quantity} ${unit}`,
  'Location': location || 'Not specified',
  'Created By': 'voice-agent',
  'Timestamp': new Date().toISOString()
});
```

##### Location 2: `recordCCPMonitoring()` method (after line 357)

```typescript
// After: const ccpId = await sqliteService.createCCPMonitoring(ccpData);
// Add:

console.log('✅ Voice Agent: CCP Monitoring Record Created');
console.table({
  'Record ID': ccpId,
  'CCP Name': ccp_name,
  'Measurement': `${measurement_value} ${measurement_unit}`,
  'Within Limits': isWithinLimits ? 'Yes' : 'No',
  'Monitored By': monitored_by,
  'Created By': 'voice-agent',
  'Timestamp': new Date().toISOString()
});
```

#### Key Features
- ✅ indicator for easy visual scanning
- `console.table()` for structured output
- All relevant fields displayed
- Consistent format across both methods
- Timestamps in ISO format

---

## Phase 2: Frontend Component

### Task 3: Create VoiceInventoryRecords Component
**Archon Task ID:** 7541daab-f244-4b96-8eeb-10cbd21c6bda  
**Order:** 110  
**File:** `src/components/VoiceInventoryRecords.tsx` (NEW FILE)

#### Component Specification

```typescript
import { useEffect, useState } from 'react';
import { sqliteService, StagedInventory } from '@/lib/sqlite-service';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export function VoiceInventoryRecords() {
  const [records, setRecords] = useState<StagedInventory[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchRecords = async () => {
    setLoading(true);
    try {
      const voiceRecords = await sqliteService.getVoiceInventoryRecords(20);
      setRecords(voiceRecords);
    } catch (error) {
      console.error('Failed to fetch voice inventory records:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecords();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Voice Agent Inventory Records</CardTitle>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchRecords}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        {records.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No voice agent records found. Create some by using the voice assistant!
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product ID</TableHead>
                  <TableHead>Batch Number</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {records.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell className="font-mono text-sm">
                      {record.product_id}
                    </TableCell>
                    <TableCell className="font-medium">
                      {record.batch_number}
                    </TableCell>
                    <TableCell>
                      {record.quantity} {record.unit || 'units'}
                    </TableCell>
                    <TableCell>{record.location || '—'}</TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      {record.created_at 
                        ? formatDistanceToNow(new Date(record.created_at), { addSuffix: true })
                        : '—'
                      }
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        record.synced_to_supabase 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {record.synced_to_supabase ? 'Synced' : 'Pending'}
                      </span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

#### Key Features
- Uses shadcn/ui components (Table, Card, Button)
- Refresh button with loading state
- Empty state message
- Formatted timestamps with `date-fns`
- Sync status badges
- Responsive table layout
- Consistent with existing UI patterns

#### Integration Points
Add to `src/pages/Index.tsx` or create new route:
```typescript
import { VoiceInventoryRecords } from '@/components/VoiceInventoryRecords';

// In component:
<VoiceInventoryRecords />
```

---

## Phase 3: Testing

### Task 4: Unit Tests
**Archon Task ID:** 1e8812a3-6909-45cd-8547-7f0f1a5209cd  
**Order:** 108  
**File:** `src/test/voice-data-visibility.test.ts` (NEW FILE)

#### Test Specification

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { sqliteService } from '@/lib/sqlite-service';
import { VoiceToolExecutor } from '@/lib/voice-tool-executor';

describe('Voice Data Visibility', () => {
  beforeEach(async () => {
    await sqliteService.initialize();
  });

  describe('getVoiceInventoryRecords', () => {
    it('should filter records by created_by = voice-agent', async () => {
      // Create voice-agent record
      await sqliteService.createInventory({
        product_id: 'test-1',
        batch_number: 'B001',
        quantity: 100,
        received_date: '2024-01-01',
        created_by: 'voice-agent'
      });

      // Create non-voice record
      await sqliteService.createInventory({
        product_id: 'test-2',
        batch_number: 'B002',
        quantity: 50,
        received_date: '2024-01-01',
        created_by: 'manual'
      });

      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records).toHaveLength(1);
      expect(records[0].created_by).toBe('voice-agent');
    });

    it('should sort by created_at DESC', async () => {
      // Create multiple records
      const id1 = await sqliteService.createInventory({
        product_id: 'test-1',
        batch_number: 'B001',
        quantity: 100,
        received_date: '2024-01-01',
        created_by: 'voice-agent'
      });

      // Wait to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));

      const id2 = await sqliteService.createInventory({
        product_id: 'test-2',
        batch_number: 'B002',
        quantity: 50,
        received_date: '2024-01-01',
        created_by: 'voice-agent'
      });

      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records[0].id).toBe(id2); // Most recent first
      expect(records[1].id).toBe(id1);
    });

    it('should respect limit parameter', async () => {
      // Create 5 records
      for (let i = 0; i < 5; i++) {
        await sqliteService.createInventory({
          product_id: `test-${i}`,
          batch_number: `B00${i}`,
          quantity: 100,
          received_date: '2024-01-01',
          created_by: 'voice-agent'
        });
      }

      const records = await sqliteService.getVoiceInventoryRecords(3);
      expect(records).toHaveLength(3);
    });

    it('should return empty array when no voice records exist', async () => {
      const records = await sqliteService.getVoiceInventoryRecords();
      expect(records).toEqual([]);
    });
  });

  describe('Voice Tool Executor Logging', () => {
    it('should log inventory creation', async () => {
      const consoleSpy = vi.spyOn(console, 'log');
      const tableableSpy = vi.spyOn(console, 'table');

      const result = await VoiceToolExecutor.executeTool({
        name: 'add_inventory_event',
        arguments: {
          product_id: 'test-1',
          batch_number: 'B001',
          quantity: 100,
          unit: 'kg'
        }
      });

      expect(result.success).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('✅ Voice Agent: Inventory Record Created')
      );
      expect(tableableSpy).toHaveBeenCalled();
    });
  });
});
```

#### Test Coverage
- ✅ Filtering by created_by
- ✅ Sorting by created_at DESC
- ✅ Limit parameter
- ✅ Empty state handling
- ✅ Console logging verification

---

## Phase 4: Documentation

### Task 5: Update Documentation
**Archon Task ID:** d168d182-5018-4ab5-8012-16a54a57a012  
**Order:** 106  
**Files:** Multiple documentation files

#### Updates Required

##### 1. `docs/VOICE_INTEGRATION.md`
Add new section after "Voice Tool Executor":

```markdown
## Voice Data Visibility

The voice agent automatically tracks all records it creates for easy monitoring and debugging.

### Viewing Voice-Created Records

#### Option 1: UI Component
Access the Voice Inventory Records component to see all voice-created entries:

```typescript
import { VoiceInventoryRecords } from '@/components/VoiceInventoryRecords';

<VoiceInventoryRecords />
```

#### Option 2: Console Logs
Check browser DevTools console for real-time logging:

```
✅ Voice Agent: Inventory Record Created
┌─────────────┬────────────────────────────────┐
│  (index)    │            Values              │
├─────────────┼────────────────────────────────┤
│ Record ID   │ '550e8400-e29b-41d4-a716...'  │
│ Product ID  │ 'salmon-001'                   │
│ Batch Number│ 'B2024-001'                    │
│ Quantity    │ '100 kg'                       │
│ Location    │ 'Cooler-A'                     │
│ Created By  │ 'voice-agent'                  │
│ Timestamp   │ '2024-01-15T10:30:00.000Z'    │
└─────────────┴────────────────────────────────┘
```

#### Option 3: Programmatic Access
Query voice records directly:

```typescript
import { sqliteService } from '@/lib/sqlite-service';

// Get last 20 voice-created inventory records
const records = await sqliteService.getVoiceInventoryRecords(20);

// Get voice-created CCP monitoring records
const ccpRecords = await sqliteService.getVoiceCCPRecords(20);
```

### Data Structure

All voice-created records include:
- `created_by: 'voice-agent'` - Identifier tag
- `created_at` - Timestamp
- `synced_to_supabase` - Sync status
- Standard table fields (product_id, batch_number, etc.)
```

##### 2. JSDoc Comments
Add to `src/lib/sqlite-service.ts`:

```typescript
/**
 * Retrieves inventory records created by the voice agent.
 * Useful for monitoring voice assistant activity and debugging.
 * 
 * @param limit - Maximum number of records to return (default: 20)
 * @returns Array of StagedInventory records with created_by='voice-agent'
 * @example
 * const recent = await sqliteService.getVoiceInventoryRecords(10);
 * console.log(`Found ${recent.length} voice-created records`);
 */
```

##### 3. `README.md` Update
Add to Features section:

```markdown
- **Voice Data Visibility** - Track and monitor voice agent activity
  - Real-time console logging with structured output
  - UI component for viewing voice-created records  
  - Filter records by voice agent source
  - Monitor sync status and timestamps
```

---

## Implementation Checklist

### Phase 1: Backend (Code Mode)
- [ ] Add `getVoiceInventoryRecords()` to sqlite-service.ts (line ~770)
- [ ] Add `getVoiceCCPRecords()` to sqlite-service.ts (line ~800)
- [ ] Add logging to `addInventoryEvent()` in voice-tool-executor.ts (line ~159)
- [ ] Add logging to `recordCCPMonitoring()` in voice-tool-executor.ts (line ~358)
- [ ] Test database methods manually
- [ ] Verify console output format

### Phase 2: Frontend (Code Mode)
- [ ] Create `src/components/VoiceInventoryRecords.tsx`
- [ ] Add date-fns dependency if needed: `npm install date-fns`
- [ ] Import and integrate component in appropriate page
- [ ] Test component rendering with mock data
- [ ] Test refresh functionality
- [ ] Test empty state display

### Phase 3: Testing (Code Mode)
- [ ] Create `src/test/voice-data-visibility.test.ts`
- [ ] Implement all test cases (8 tests total)
- [ ] Run test suite: `npm run test`
- [ ] Verify >90% coverage for new code
- [ ] Fix any failing tests

### Phase 4: Documentation (Architect Mode)
- [ ] Update `docs/VOICE_INTEGRATION.md`
- [ ] Add JSDoc comments to new methods
- [ ] Update `README.md` features list
- [ ] Create usage examples
- [ ] Add screenshots to docs (optional)

### Phase 5: Archon Updates
- [ ] Mark Task 114 as completed (database method)
- [ ] Mark Task 112 as completed (logging)
- [ ] Mark Task 110 as completed (component)
- [ ] Mark Task 108 as completed (tests)
- [ ] Mark Task 106 as completed (documentation)

---

## Dependencies

### Existing
- `sql.js` - Already in use
- `shadcn/ui` components - Already installed
- React hooks - Built-in

### New (if needed)
```bash
npm install date-fns
```

---

## Testing Strategy

### Manual Testing
1. Start dev server: `npm run dev`
2. Open browser console (F12)
3. Use voice agent to create inventory record
4. Verify console output shows ✅ and table
5. Open VoiceInventoryRecords component
6. Verify records display correctly
7. Test refresh button
8. Test empty state

### Automated Testing
```bash
npm run test src/test/voice-data-visibility.test.ts
```

Expected: 8/8 tests passing

---

## Error Handling

### Database Query Failures
```typescript
try {
  const records = await sqliteService.getVoiceInventoryRecords();
} catch (error) {
  console.error('Failed to fetch voice records:', error);
  // Show error toast or fallback UI
}
```

### Component Mounting Issues
- Check if sqliteService is initialized
- Verify database schema includes created_by column
- Check for TypeScript errors in component props

---

## Rollback Plan

If issues arise:
1. Revert sqlite-service.ts changes (remove new methods)
2. Revert voice-tool-executor.ts changes (remove logging)
3. Remove VoiceInventoryRecords.tsx component
4. Remove test file
5. Revert documentation updates

All changes are additive - no breaking changes to existing functionality.

---

## Success Criteria

- ✅ New database methods return filtered voice records
- ✅ Console logging displays structured output with ✅ indicator
- ✅ UI component renders records in table format
- ✅ All tests pass (8/8)
- ✅ Documentation updated with examples
- ✅ No breaking changes to existing features
- ✅ All Archon tasks marked completed

---

## Next Steps

1. **Review this plan** - Ensure all specifications are clear
2. **Switch to Code mode** - Begin implementation
3. **Follow checklist** - Complete tasks in order (Phase 1 → 2 → 3 → 4)
4. **Update Archon** - Mark tasks complete as you progress
5. **Test thoroughly** - Manual + automated testing
6. **Update user** - Present completed features

---

## Questions & Clarifications

**Q: Should we also add voice filtering for other tables?**  
A: Start with inventory and CCP monitoring as specified. Can extend later.

**Q: What about pagination in the UI component?**  
A: Current spec uses fixed limit (20). Add pagination if needed later.

**Q: Should we expose these methods as voice tools?**  
A: No - these are read-only methods for visibility, not voice actions.

---

**Document Version:** 1.0  
**Created:** 2024-10-24  
**Status:** Ready for Code Mode Implementation