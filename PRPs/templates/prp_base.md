# [Feature Name] - Product Requirements Prompt (PRP)

## Goal
[Clear, concise description of what this feature accomplishes]

**Success Criteria:**
- [Specific, measurable outcome 1]
- [Specific, measurable outcome 2]
- [Specific, measurable outcome 3]

---

## All Needed Context

### External Documentation
```yaml
- url: [Official library/framework documentation URL]
  why: [Specific sections or methods needed]
  critical: [Key insights that prevent common errors]

- url: [React Hook Form docs - if form-related]
  why: Form validation and error handling patterns

- url: [Zod documentation - if validation needed]
  why: Schema definition and custom validators
```

### Local Code Examples
```yaml
- file: src/lib/sqlite-service.ts
  why: Database operation patterns, error handling, parameterized queries

- file: src/components/HaccpEventForm.tsx
  why: Form structure, React Hook Form usage, Zod schema, toast notifications

- file: src/lib/sync-service.ts
  why: Supabase sync patterns (if sync needed)

- file: src/components/ui/[relevant-component].tsx
  why: UI component usage patterns

- file: [other relevant example files]
  why: [specific pattern to follow]
```

---

## Data Models and Structure

### TypeScript Interfaces
```typescript
// Define core data structures
interface YourEntity {
  id: number;
  field1: string;
  field2: number;
  created_by: string;
  created_at: string;
  synced_to_supabase: number;
  sync_confirmed: number;
}

interface YourEntityInsert {
  field1: string;
  field2: number;
  created_by: string;
}

// Add additional interfaces as needed
```

### Zod Validation Schema
```typescript
import { z } from 'zod';

const yourEntitySchema = z.object({
  field1: z.string().min(1, 'Field 1 is required'),
  field2: z.number().min(0, 'Field 2 must be positive'),
  // Add validation rules matching business requirements
});

type YourEntityFormData = z.infer<typeof yourEntitySchema>;
```

### SQLite Schema (if new table needed)
```sql
-- Add to public/sqlite-schema.sql
CREATE TABLE IF NOT EXISTS your_table_staged (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  field1 TEXT NOT NULL,
  field2 INTEGER NOT NULL,
  created_by TEXT NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  synced_to_supabase INTEGER DEFAULT 0,
  sync_confirmed INTEGER DEFAULT 0
);
```

---

## Known Gotchas and Critical Details

### SQLite Service Patterns
```typescript
// CRITICAL: Always check database initialization
if (!this.db) throw new Error('Database not initialized');

// PATTERN: Use parameterized queries to prevent SQL injection
const stmt = this.db.prepare(`INSERT INTO table (col1, col2) VALUES (?, ?)`);
stmt.run([value1, value2]);
stmt.free(); // CRITICAL: Always free prepared statements

// GOTCHA: SQLite returns results as arrays, not objects by default
const result = this.db.exec(`SELECT * FROM table`);
if (result.length > 0) {
  const columns = result[0].columns; // Column names
  const values = result[0].values;   // Array of row arrays
}

// PATTERN: Always wrap in try/catch
try {
  // database operations
} catch (error) {
  console.error('Error message:', error);
  throw error; // Re-throw for caller to handle
}
```

### Form Handling Patterns
```typescript
// PATTERN: useForm with Zod resolver
const form = useForm<z.infer<typeof formSchema>>({
  resolver: zodResolver(formSchema),
  defaultValues: { /* initial values */ },
});

// PATTERN: Async onSubmit with error handling
async function onSubmit(values: z.infer<typeof formSchema>) {
  try {
    await sqliteService.insertYourEntity({
      ...values,
      created_by: 'current-user', // TODO: Replace with actual user from auth
    });
    toast.success('Success message');
    form.reset();
  } catch (error) {
    console.error('Error:', error);
    toast.error('Error message');
  }
}

// GOTCHA: React Hook Form requires specific field structure
<FormField
  control={form.control}
  name="field1"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Field Label</FormLabel>
      <FormControl>
        <Input {...field} />
      </FormControl>
      <FormMessage />
    </FormItem>
  )}
/>
```

### UI Component Patterns
```typescript
// PATTERN: Import from shadcn/ui
import { Button } from "@/components/ui/button";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// PATTERN: Consistent spacing with Tailwind
<div className="space-y-4"> {/* Vertical spacing */}
  <div className="grid gap-4"> {/* Grid layout */}
    {/* Components */}
  </div>
</div>
```

---

## Implementation Blueprint

### Task 1: [Update Database Schema (if needed)]
**File:** `public/sqlite-schema.sql`

```sql
-- Add new table or modify existing schema
CREATE TABLE IF NOT EXISTS your_table_staged (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  field1 TEXT NOT NULL,
  field2 INTEGER NOT NULL,
  created_by TEXT NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  synced_to_supabase INTEGER DEFAULT 0,
  sync_confirmed INTEGER DEFAULT 0
);
```

**Critical Details:**
- Always include `synced_to_supabase` and `sync_confirmed` for staging
- Use AUTOINCREMENT for primary keys
- Include `created_at` with DEFAULT CURRENT_TIMESTAMP
- Use TEXT for strings, INTEGER for numbers, REAL for decimals

---

### Task 2: [Add TypeScript Interfaces to SQLite Service]
**File:** `src/lib/sqlite-service.ts`

```typescript
// MODIFY: Add interfaces at top of file
interface YourEntity {
  id: number;
  field1: string;
  field2: number;
  created_by: string;
  created_at: string;
  synced_to_supabase: number;
  sync_confirmed: number;
}

interface YourEntityInsert {
  field1: string;
  field2: number;
  created_by: string;
}
```

---

### Task 3: [Implement Database Service Methods]
**File:** `src/lib/sqlite-service.ts`

```typescript
// INJECT after existing insert methods
async insertYourEntity(data: YourEntityInsert): Promise<void> {
  if (!this.db) throw new Error('Database not initialized');

  try {
    const stmt = this.db.prepare(`
      INSERT INTO your_table_staged (
        field1, field2, created_by, synced_to_supabase, sync_confirmed
      ) VALUES (?, ?, ?, 0, 0)
    `);

    stmt.run([data.field1, data.field2, data.created_by]);
    stmt.free();

    console.log('Your entity inserted successfully');
  } catch (error) {
    console.error('Error inserting your entity:', error);
    throw error;
  }
}

// INJECT: Get methods
async getYourEntities(): Promise<YourEntity[]> {
  if (!this.db) throw new Error('Database not initialized');

  try {
    const result = this.db.exec(`
      SELECT * FROM your_table_staged
      ORDER BY created_at DESC
    `);

    if (result.length === 0) return [];

    const columns = result[0].columns;
    const values = result[0].values;

    return values.map(row => {
      const obj: any = {};
      columns.forEach((col, idx) => {
        obj[col] = row[idx];
      });
      return obj as YourEntity;
    });
  } catch (error) {
    console.error('Error getting your entities:', error);
    throw error;
  }
}
```

**Pattern to Follow:** See `insertProduct()` and `getProducts()` methods

---

### Task 4: [Create Form Component]
**File:** `src/components/YourFeatureForm.tsx` (CREATE NEW FILE)

```typescript
// MIRROR pattern from: src/components/HaccpEventForm.tsx

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { sqliteService } from '@/lib/sqlite-service';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

// STEP 1: Define Zod schema
const formSchema = z.object({
  field1: z.string().min(1, 'Field 1 is required'),
  field2: z.number().min(0, 'Field 2 must be positive'),
});

// STEP 2: Component definition
export function YourFeatureForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      field1: '',
      field2: 0,
    },
  });

  // STEP 3: Submit handler with error handling
  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await sqliteService.insertYourEntity({
        ...values,
        created_by: 'current-user', // TODO: Replace with actual user
      });
      toast.success('Record created successfully');
      form.reset();
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to create record');
    }
  }

  // STEP 4: Form JSX
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="field1"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Field 1</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="field2"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Field 2</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  {...field}
                  onChange={e => field.onChange(Number(e.target.value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={form.formState.isSubmitting}>
          {form.formState.isSubmitting ? 'Saving...' : 'Save'}
        </Button>
      </form>
    </Form>
  );
}
```

**Critical Details:**
- Use `zodResolver` for validation
- Handle number inputs with `onChange` converter
- Show loading state during submission
- Display success/error toasts
- Reset form after success

---

### Task 5: [Create Page Component (if new page)]
**File:** `src/pages/YourFeaturePage.tsx` (CREATE NEW FILE)

```typescript
import { YourFeatureForm } from '@/components/YourFeatureForm';

export default function YourFeaturePage() {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Your Feature</h1>
      <YourFeatureForm />
    </div>
  );
}
```

---

### Task 6: [Add Route (if new page)]
**File:** `src/App.tsx`

```typescript
// FIND: Routes section
// INJECT: Before the catch-all * route
import YourFeaturePage from '@/pages/YourFeaturePage';

// Inside <Routes>
<Route path="/your-feature" element={<YourFeaturePage />} />
```

---

### Task 7: [Update Sync Service (if sync needed)]
**File:** `src/lib/sync-service.ts`

```typescript
// FIND: syncToSupabase method
// ADD: New sync logic for your entity

const yourEntities = await sqliteService.getYourEntities();
const unsynced = yourEntities.filter(e => !e.synced_to_supabase);

for (const entity of unsynced) {
  const { error } = await supabase
    .from('your_table')
    .insert({
      field1: entity.field1,
      field2: entity.field2,
      created_by: entity.created_by,
    });

  if (!error) {
    // Mark as synced in SQLite
    await sqliteService.updateYourEntitySync(entity.id, true);
  }
}
```

---

## Integration Points

### Database
```yaml
SCHEMA:
  - file: public/sqlite-schema.sql
  - action: Add new table or modify existing

SERVICE:
  - file: src/lib/sqlite-service.ts
  - actions:
    - Add interfaces
    - Add insert/get/update methods
    - Follow existing method patterns
```

### Routes (if new page)
```yaml
ROUTING:
  - file: src/App.tsx
  - action: Add <Route> before catch-all route
  - pattern: <Route path="/your-feature" element={<YourFeaturePage />} />
```

### Components
```yaml
COMPONENTS:
  - create: src/components/YourFeatureForm.tsx
  - create: src/pages/YourFeaturePage.tsx (if new page)
  - import: UI components from src/components/ui/
```

### Sync (if applicable)
```yaml
SYNC:
  - file: src/lib/sync-service.ts
  - action: Add sync logic for new entity
  - pattern: Follow existing sync methods
```

---

## Validation Loop

### Level 1: Syntax and Type Checking
```bash
# Run ESLint
npm run lint

# Expected: No errors or warnings
# If errors: Fix linting issues before proceeding

# Run TypeScript compilation
npm run build:dev

# Expected: Build succeeds with no type errors
# If errors: Fix type issues, add missing imports
```

### Level 2: Manual Testing
```bash
# Start development server
npm run dev

# Expected: Server starts on http://localhost:8080
# If errors: Check console for initialization errors
```

**Browser Testing:**
1. Navigate to feature in browser
2. Open DevTools Console (check for errors)
3. Test form submission (happy path)
4. Test form validation (empty fields, invalid values)
5. Verify success toasts appear
6. Check SQLite data persisted (via staging dashboard or console)
7. Test error handling (disconnect network, etc.)

### Level 3: Data Integrity
```bash
# Verify data in SQLite
# Open browser console and run:
sqliteService.getYourEntities().then(console.log)

# Expected: Array of entities with correct structure
# If issues: Check database schema and insert method
```

**Sync Verification (if applicable):**
1. Start local Supabase (`cd ~/Dev/Seafood-Manager && supabase start`)
2. Submit form data
3. Click "Confirm" in staging dashboard
4. Verify data appears in Supabase Studio
5. Check `synced_to_supabase` flag updated in SQLite

---

## Success Criteria

### Functional Requirements
- [ ] Feature works as specified in Goal section
- [ ] All user interactions work smoothly
- [ ] Data persists correctly to SQLite
- [ ] Sync to Supabase works (if applicable)
- [ ] Edge cases handled gracefully

### Code Quality
- [ ] No TypeScript errors (`npm run build:dev`)
- [ ] No ESLint warnings (`npm run lint`)
- [ ] All methods properly typed
- [ ] Error handling implemented
- [ ] Console logs for debugging added

### User Experience
- [ ] UI renders correctly and is responsive
- [ ] Form validation provides clear feedback
- [ ] Success/error toasts display appropriately
- [ ] Loading states show during async operations
- [ ] No console errors in browser

### Integration
- [ ] Database schema updated (if needed)
- [ ] Service methods work correctly
- [ ] Routes configured (if new page)
- [ ] Sync service updated (if applicable)
- [ ] No regressions in existing features

### Documentation
- [ ] Code comments added for complex logic
- [ ] TypeScript interfaces documented
- [ ] Console logs help with debugging

---

## Confidence Score: [1-10]
[Rate your confidence in this implementation plan]

**Confidence Factors:**
- Clarity of requirements
- Availability of similar examples in codebase
- Understanding of technology stack
- Completeness of documentation references
- Complexity of feature

---

## Additional Notes
[Any other considerations, risks, or dependencies to be aware of]
