# Product Requirements Prompt: Voice Agent WebRTC Migration

**Feature**: Migrate voice agent from WebSocket to WebRTC for OpenAI Realtime API
**Status**: Ready for Implementation
**Estimated Time**: 6-8 hours (5 phases)
**Priority**: HIGH - Fixes non-responsive agent + improves performance

---

## 🎯 Goal

Migrate the voice agent from WebSocket to WebRTC transport to:
1. **Fix critical bug**: Voice agent currently connects but doesn't respond to speech
2. **Reduce latency**: Achieve 50-100ms improvement through direct P2P connection
3. **Simplify architecture**: Remove server-side WebSocket bridge
4. **Follow best practices**: Implement OpenAI's recommended approach for browsers
5. **Maintain features**: Preserve all tool execution, monitoring, and error handling

### Success Criteria
- ✅ Voice agent responds to spoken prompts
- ✅ Latency reduced by at least 50ms
- ✅ All existing features work (tool calls, network monitoring, error recovery)
- ✅ Tests pass (lint, build, manual validation)
- ✅ Documentation updated

---

## 📚 All Needed Context

### External Documentation

**OpenAI Realtime API:**
- [WebRTC Connection Guide](https://platform.openai.com/docs/guides/realtime#webrtc-connection) - Official recommendation for browser apps
- [Ephemeral Keys](https://platform.openai.com/docs/api-reference/realtime/createSession) - Secure client-side authentication
- [Realtime API Reference](https://platform.openai.com/docs/api-reference/realtime) - Event types and message formats
- [Audio Format Specs](https://platform.openai.com/docs/guides/realtime#audio-formats) - PCM16, 24kHz requirements

**WebRTC:**
- [MDN WebRTC API](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API) - Complete WebRTC fundamentals
- [RTCPeerConnection](https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection) - Peer connection lifecycle
- [RTCDataChannel](https://developer.mozilla.org/en-US/docs/Web/API/RTCDataChannel) - Data channel for events
- [getUserMedia](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia) - Audio constraints

**React Patterns:**
- [useRef for instances](https://react.dev/reference/react/useRef) - Storing non-reactive values
- [useEffect cleanup](https://react.dev/reference/react/useEffect#cleaning-up) - Proper cleanup

### Local File Paths

**Current Implementation (To Replace):**
- `src/components/VoiceAgent.tsx` - Main voice component (1051 lines)
  - Lines 538-717: `connectWebSocket()` function (REPLACE with WebRTC)
  - Lines 405-495: `handleMessage()` event handler (KEEP, adapt)
  - Lines 269-403: `handleToolCall()` tool execution (KEEP)
  - Lines 210-267: Network monitoring (KEEP, integrate)
  - Lines 920-1048: UI components (KEEP)

**Audio Processing (Keep and Adapt):**
- `src/utils/RealtimeAudio.ts` - Audio optimization (466 lines)
  - Lines 24-105: `AudioRecorder` class (ADAPT for WebRTC streams)
  - Lines 50-73: Audio constraints (REUSE exactly)
  - Lines 332-349: `encodeAudioForAPI()` (MAY NOT NEED - WebRTC handles)
  - Lines 460-465: `playAudioData()` (KEEP for response audio)

**Supporting Services:**
- `src/lib/voice-tool-executor.ts` - Tool execution logic (KEEP unchanged)
- `src/lib/voice-audit-logger.ts` - Audit logging (KEEP)
- `src/lib/voice-error-handler.ts` - Error handling (KEEP, add WebRTC errors)
- `src/components/VoiceToolFeedback.tsx` - Tool feedback UI (KEEP)

**Project Documentation:**
- `WEBRTC_MIGRATION_STRATEGY.md` - Full migration strategy and rationale
- `VOICE_WEBRTC_TROUBLESHOOTING_PLAN.md` - Phase-by-phase implementation guide
- `docs/VOICE_INTEGRATION.md` - Voice integration documentation (UPDATE)
- `docs/VOICE_TROUBLESHOOTING.md` - Error reference (UPDATE)

---

## 🗂️ Data Models

### TypeScript Interfaces

```typescript
// File: src/lib/realtime-webrtc-client.ts (NEW)

/**
 * Configuration for WebRTC client
 */
export interface WebRTCClientConfig {
  ephemeralKey: string;         // Short-lived API key from backend
  model: string;                 // e.g., 'gpt-4o-realtime-preview-2024-12-17'
  voice: string;                 // e.g., 'alloy', 'echo', 'fable'
  onMessage: (message: RealtimeEvent) => void;  // Event handler
  onAudioData?: (audioData: Uint8Array) => void; // Audio playback handler
  onError: (error: Error) => void;              // Error handler
  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
}

/**
 * OpenAI Realtime API event types
 */
export interface RealtimeEvent {
  type: string;
  [key: string]: unknown;
}

/**
 * Session configuration for OpenAI
 */
export interface SessionConfig {
  modalities: string[];          // ['text', 'audio']
  instructions: string;          // System prompt
  voice: string;                 // Voice ID
  input_audio_format: 'pcm16';   // Required format
  output_audio_format: 'pcm16';  // Required format
  turn_detection: {              // Voice Activity Detection
    type: 'server_vad';
  };
  temperature: number;           // 0-1
  max_response_output_tokens: number;
}

/**
 * Ephemeral key response from backend
 */
export interface EphemeralKeyResponse {
  client_secret: {
    value: string;               // Actual ephemeral key
    expires_at: number;          // Unix timestamp
  };
}
```

### WebRTC Client Class Structure

```typescript
// File: src/lib/realtime-webrtc-client.ts (NEW)

export class RealtimeWebRTCClient {
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private audioStream: MediaStream | null = null;
  private config: WebRTCClientConfig;

  constructor(config: WebRTCClientConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    // 1. Create peer connection
    // 2. Set up data channel
    // 3. Get user media (microphone)
    // 4. Add audio tracks
    // 5. Handle incoming audio
    // 6. Create and send offer
    // 7. Receive and set answer
    // 8. Send session config
  }

  private setupDataChannel(): void {
    // Handle data channel events (onopen, onmessage, onerror)
  }

  private async sendOfferToOpenAI(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    // Send SDP offer to OpenAI, receive SDP answer
  }

  sendMessage(message: RealtimeEvent): void {
    // Send events via data channel
  }

  disconnect(): void {
    // Clean up all resources
  }
}
```

### Ephemeral Key Service

```typescript
// File: src/lib/openai-ephemeral-keys.ts (NEW)

/**
 * Generate ephemeral key for browser-safe authentication
 * @returns Short-lived API key safe for client use
 */
export async function getEphemeralKey(): Promise<string> {
  // Call backend endpoint
  // Backend generates key via OpenAI API
  // Return ephemeral key value
}
```

---

## 🏗️ Implementation Blueprint

### Phase 1: Debug Current Implementation (Optional but Recommended)

**Time**: 1-2 hours
**Purpose**: Understand why WebSocket agent doesn't respond

#### Task 1.1: Add Enhanced Logging
**File**: `src/components/VoiceAgent.tsx`

```typescript
// In handleMessage function (line 405)
const handleMessage = (event: Record<string, unknown>) => {
  // ADD COMPREHENSIVE LOGGING
  console.log('🔍 [VoiceAgent] Received Event:', {
    type: event.type,
    fullEvent: event,
    timestamp: new Date().toISOString(),
    connectionState: wsRef.current?.readyState,
    isSpeaking: isSpeaking,
    audioContextState: audioContextRef.current?.state
  });

  // Special logging for audio events
  if (event.type === 'response.audio.delta') {
    console.log('🔊 [Audio Delta] Received', {
      deltaLength: (event.delta as string)?.length || 0,
      base64Sample: (event.delta as string)?.substring(0, 50)
    });
  }

  if (event.type === 'response.audio_transcript.delta') {
    console.log('📝 [Transcript]:', event.delta);
  }

  // Existing code continues...
}
```

#### Task 1.2: Verify Audio Playback
**File**: `src/utils/RealtimeAudio.ts`

```typescript
// In playAudioData function (line 460)
export const playAudioData = async (audioContext: AudioContext, audioData: Uint8Array) => {
  console.log('🎵 [playAudioData] Attempting playback', {
    audioDataLength: audioData.length,
    audioContextState: audioContext.state,
    queueExists: !!audioQueueInstance,
    queueLength: audioQueueInstance?.queue?.length || 0
  });

  // Existing code...
}

// In AudioQueue.playNext (line 414)
private async playNext() {
  console.log('▶️ [AudioQueue] Playing next segment', {
    queueLength: this.queue.length,
    isPlaying: this.isPlaying,
    audioContextState: this.audioContext.state
  });

  // Existing code...
}
```

#### Task 1.3: Test Latest Model
**File**: `src/components/VoiceAgent.tsx` (line 568)

```typescript
// CHANGE:
// const wsUrlWithAuth = `${wsUrl}?model=gpt-4o-realtime-preview-2024-10-01`;

// TO:
const wsUrlWithAuth = `${wsUrl}?model=gpt-4o-realtime-preview-2024-12-17`;
```

**Testing**: Start voice agent, speak a prompt, check console for:
- Are events arriving? (Look for 🔍 logs)
- Is audio data in `response.audio.delta`? (Look for 🔊 logs)
- Is playback being called? (Look for 🎵 logs)
- Is audio queue working? (Look for ▶️ logs)

**Decision Point**: If simple fix found (e.g., model issue, timing issue), apply it. Otherwise proceed to WebRTC which likely solves the transport issue.

---

### Phase 2: Setup WebRTC Infrastructure

**Time**: 1-2 hours
**Purpose**: Create backend and client infrastructure

#### Task 2.1: Create Ephemeral Key Service (Backend)

**Option A: Supabase Edge Function** (Recommended if using Supabase)

```typescript
// File: supabase/functions/openai-ephemeral-key/index.ts (NEW)

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const apiKey = Deno.env.get('OPENAI_API_KEY')
    if (!apiKey) {
      throw new Error('OpenAI API key not configured')
    }

    // Generate ephemeral key from OpenAI
    const response = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-realtime-preview-2024-12-17',
        voice: 'alloy'
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`)
    }

    const data = await response.json()

    return new Response(
      JSON.stringify(data),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
```

**Deploy**:
```bash
supabase functions deploy openai-ephemeral-key
```

**Option B: Express Endpoint** (If using custom backend)

```typescript
// File: server/routes/voice.ts (NEW or ADD TO EXISTING)

app.post('/api/voice/ephemeral-key', async (req, res) => {
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return res.status(500).json({ error: 'API key not configured' });
    }

    const response = await fetch('https://api.openai.com/v1/realtime/sessions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-realtime-preview-2024-12-17',
        voice: 'alloy'
      })
    });

    const data = await response.json();
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

#### Task 2.2: Create Frontend Ephemeral Key Service

```typescript
// File: src/lib/openai-ephemeral-keys.ts (NEW)

/**
 * Get ephemeral API key for WebRTC connection
 * Ephemeral keys are short-lived (~60s) and safe for browser use
 *
 * @returns {Promise<string>} Ephemeral key value
 * @throws {Error} If key generation fails
 */
export async function getEphemeralKey(): Promise<string> {
  try {
    // Option A: Supabase Edge Function
    const response = await fetch(
      `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/openai-ephemeral-key`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // Option B: Custom Backend
    // const response = await fetch('/api/voice/ephemeral-key', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' }
    // });

    if (!response.ok) {
      throw new Error(`Failed to get ephemeral key: ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.client_secret?.value) {
      throw new Error('Invalid ephemeral key response');
    }

    console.log('✅ Ephemeral key generated', {
      expiresAt: new Date(data.client_secret.expires_at * 1000).toISOString()
    });

    return data.client_secret.value;
  } catch (error) {
    console.error('❌ Failed to get ephemeral key:', error);
    throw error;
  }
}
```

#### Task 2.3: Create WebRTC Client Class

```typescript
// File: src/lib/realtime-webrtc-client.ts (NEW)

/**
 * WebRTC client for OpenAI Realtime API
 * Handles peer connection, data channels, and media streams
 */

export interface WebRTCClientConfig {
  ephemeralKey: string;
  model: string;
  voice: string;
  onMessage: (message: any) => void;
  onAudioData?: (audioData: Uint8Array) => void;
  onError: (error: Error) => void;
  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
}

export class RealtimeWebRTCClient {
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private audioStream: MediaStream | null = null;
  private config: WebRTCClientConfig;

  constructor(config: WebRTCClientConfig) {
    this.config = config;
  }

  /**
   * Establish WebRTC connection to OpenAI Realtime API
   */
  async connect(): Promise<void> {
    try {
      console.log('🔌 [WebRTC] Starting connection...');

      // 1. Create peer connection with STUN server
      this.peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      // Monitor connection state
      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection!.connectionState;
        console.log(`🔌 [WebRTC] Connection state: ${state}`);
        this.config.onConnectionStateChange?.(state);

        if (state === 'failed' || state === 'closed') {
          this.config.onError(new Error(`Connection ${state}`));
        }
      };

      // 2. Set up data channel for events
      this.dataChannel = this.peerConnection.createDataChannel('oai-events', {
        ordered: true
      });
      this.setupDataChannel();

      // 3. Get user media (microphone) with same constraints as before
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 24000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      console.log('🎤 [WebRTC] Microphone access granted');

      // 4. Add audio tracks to peer connection
      this.audioStream.getTracks().forEach(track => {
        console.log('➕ [WebRTC] Adding local audio track');
        this.peerConnection!.addTrack(track, this.audioStream!);
      });

      // 5. Handle incoming audio tracks from OpenAI
      this.peerConnection.ontrack = (event) => {
        console.log('📡 [WebRTC] Received remote audio track');

        // Play received audio through speakers
        const audioElement = new Audio();
        audioElement.srcObject = event.streams[0];
        audioElement.play().catch(err => {
          console.error('Failed to play audio:', err);
        });
      };

      // 6. Create SDP offer
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);
      console.log('📤 [WebRTC] Created and set local description (offer)');

      // 7. Send offer to OpenAI and get answer
      const answer = await this.sendOfferToOpenAI(offer);
      await this.peerConnection.setRemoteDescription(answer);
      console.log('📥 [WebRTC] Set remote description (answer)');

      // 8. Wait for connection to establish
      await this.waitForConnection();

      // 9. Send session configuration via data channel
      this.sendSessionConfig();

      console.log('✅ [WebRTC] Connection established successfully');
    } catch (error) {
      console.error('❌ [WebRTC] Connection failed:', error);
      this.config.onError(error as Error);
      throw error;
    }
  }

  /**
   * Set up data channel event handlers
   */
  private setupDataChannel(): void {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      console.log('✅ [DataChannel] Opened - ready to send/receive events');
    };

    this.dataChannel.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        // Forward to message handler
        this.config.onMessage(message);

        // If audio data included, forward to audio handler
        if (message.type === 'response.audio.delta' && message.delta) {
          const audioData = this.base64ToUint8Array(message.delta);
          this.config.onAudioData?.(audioData);
        }
      } catch (error) {
        console.error('[DataChannel] Failed to parse message:', error);
      }
    };

    this.dataChannel.onerror = (error) => {
      console.error('[DataChannel] Error:', error);
      this.config.onError(new Error('Data channel error'));
    };

    this.dataChannel.onclose = () => {
      console.log('[DataChannel] Closed');
    };
  }

  /**
   * Send SDP offer to OpenAI and receive answer
   */
  private async sendOfferToOpenAI(
    offer: RTCSessionDescriptionInit
  ): Promise<RTCSessionDescriptionInit> {
    const response = await fetch('https://api.openai.com/v1/realtime', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.ephemeralKey}`,
        'Content-Type': 'application/sdp'
      },
      body: offer.sdp
    });

    if (!response.ok) {
      throw new Error(`OpenAI SDP exchange failed: ${response.statusText}`);
    }

    const answerSdp = await response.text();
    return {
      type: 'answer',
      sdp: answerSdp
    };
  }

  /**
   * Wait for peer connection to reach 'connected' state
   */
  private async waitForConnection(timeoutMs = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, timeoutMs);

      const checkState = () => {
        const state = this.peerConnection?.connectionState;
        if (state === 'connected') {
          clearTimeout(timeout);
          resolve();
        } else if (state === 'failed' || state === 'closed') {
          clearTimeout(timeout);
          reject(new Error(`Connection ${state}`));
        }
      };

      this.peerConnection!.addEventListener('connectionstatechange', checkState);
      checkState(); // Check immediately in case already connected
    });
  }

  /**
   * Send session configuration to OpenAI
   */
  private sendSessionConfig(): void {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      console.warn('[WebRTC] Data channel not ready, cannot send session config');
      return;
    }

    const sessionConfig = {
      type: 'session.update',
      session: {
        modalities: ['text', 'audio'],
        instructions: 'You are a helpful voice assistant for managing seafood inventory and food safety compliance.',
        voice: this.config.voice,
        input_audio_format: 'pcm16',
        output_audio_format: 'pcm16',
        turn_detection: { type: 'server_vad' },
        temperature: 0.8,
        max_response_output_tokens: 4096
      }
    };

    this.dataChannel.send(JSON.stringify(sessionConfig));
    console.log('📤 [WebRTC] Sent session configuration');

    // Send initial response.create to start listening
    setTimeout(() => {
      this.sendMessage({ type: 'response.create' });
      console.log('📤 [WebRTC] Sent initial response.create');
    }, 100);
  }

  /**
   * Send message/event via data channel
   */
  sendMessage(message: any): void {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      throw new Error('Data channel not ready');
    }
    this.dataChannel.send(JSON.stringify(message));
  }

  /**
   * Convert base64 audio to Uint8Array
   */
  private base64ToUint8Array(base64: string): Uint8Array {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * Disconnect and cleanup all resources
   */
  disconnect(): void {
    console.log('🔌 [WebRTC] Disconnecting...');

    // Close data channel
    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }

    // Close peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Stop media streams
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => {
        track.stop();
        console.log('🛑 [WebRTC] Stopped audio track');
      });
      this.audioStream = null;
    }

    console.log('✅ [WebRTC] Disconnected successfully');
  }
}
```

---

### Phase 3: Refactor VoiceAgent Component

**Time**: 2-3 hours
**Purpose**: Replace WebSocket with WebRTC in main component

#### Task 3.1: Update Imports and Refs

```typescript
// File: src/components/VoiceAgent.tsx

// ADD NEW IMPORTS
import { RealtimeWebRTCClient, WebRTCClientConfig } from '@/lib/realtime-webrtc-client';
import { getEphemeralKey } from '@/lib/openai-ephemeral-keys';

// REPLACE wsRef with webrtcClientRef
// OLD: const wsRef = useRef<WebSocket | null>(null);
// NEW:
const webrtcClientRef = useRef<RealtimeWebRTCClient | null>(null);

// REMOVE recorderRef (WebRTC handles audio streaming)
// OLD: const recorderRef = useRef<AudioRecorder | null>(null);
// (WebRTC MediaStream replaces this)

// KEEP audioContextRef for playback
const audioContextRef = useRef<AudioContext | null>(null);
```

#### Task 3.2: Replace connectWebSocket with connectWebRTC

```typescript
// File: src/components/VoiceAgent.tsx

/**
 * Connect to OpenAI via WebRTC (replaces connectWebSocket)
 */
const connectWebRTC = async (retryAttempt: number) => {
  try {
    console.log('🔌 Starting WebRTC connection (attempt ' + (retryAttempt + 1) + ')');

    // 1. Get ephemeral key from backend
    const ephemeralKey = await getEphemeralKey();
    console.log('✅ Ephemeral key obtained');

    // 2. Track connection start time for latency measurement
    const connectionStartTime = Date.now();

    // 3. Create WebRTC client
    const webrtcConfig: WebRTCClientConfig = {
      ephemeralKey,
      model: 'gpt-4o-realtime-preview-2024-12-17',
      voice: 'alloy',
      onMessage: handleMessage,
      onAudioData: (audioData) => {
        if (audioContextRef.current) {
          playAudioData(audioContextRef.current, audioData);
        }
      },
      onError: (error) => {
        console.error('❌ WebRTC error:', error);
        sessionStateRef.current.errorCount++;

        // Handle reconnection
        if (retryAttempt < 5 && navigator.onLine) {
          const backoffDelay = getExponentialBackoff(retryAttempt);
          setRetryCount(retryAttempt + 1);
          setIsReconnecting(true);

          reconnectTimeoutRef.current = setTimeout(() => {
            connectWebRTC(retryAttempt + 1);
          }, backoffDelay);
        }
      },
      onConnectionStateChange: (state) => {
        console.log('🔌 Connection state:', state);

        if (state === 'connected') {
          // Track connection latency
          const latency = Date.now() - connectionStartTime;
          latencyTrackingRef.current.push(latency);
          if (latencyTrackingRef.current.length > 100) {
            latencyTrackingRef.current.shift();
          }

          const quality = getConnectionQuality(latencyTrackingRef.current);
          setConnectionQuality(quality);

          setIsConnected(true);
          setRetryCount(0);
          setIsReconnecting(false);

          toast({
            title: "Connected",
            description: `Voice agent ready via WebRTC (${quality} connection)`,
          });
        } else if (state === 'disconnected' || state === 'failed') {
          setIsConnected(false);
        }
      }
    };

    webrtcClientRef.current = new RealtimeWebRTCClient(webrtcConfig);

    // 4. Connect
    await webrtcClientRef.current.connect();

    // 5. Start network monitoring (every 2 seconds)
    if (networkMonitoringIntervalRef.current) {
      clearInterval(networkMonitoringIntervalRef.current);
    }
    networkMonitoringIntervalRef.current = setInterval(monitorNetworkConditions, 2000);
    console.log('✅ Network monitoring started');

  } catch (error) {
    console.error('❌ WebRTC connection failed:', error);
    sessionStateRef.current.errorCount++;

    // Show user-friendly error
    let errorType = ConnectionErrorType.UNKNOWN;
    if (error instanceof Error) {
      if (error.message.includes('ephemeral key')) {
        errorType = ConnectionErrorType.API_KEY_INVALID;
      } else if (error.message.includes('microphone')) {
        errorType = ConnectionErrorType.MICROPHONE_DENIED;
      } else if (!navigator.onLine) {
        errorType = ConnectionErrorType.NETWORK_OFFLINE;
      }
    }

    const errorInfo = getErrorMessage(errorType, retryAttempt);
    toast({
      title: errorInfo.title,
      description: errorInfo.description + '\n\nSuggestions:\n' + errorInfo.suggestions.join('\n'),
      variant: "destructive",
      duration: 10000,
    });

    // Retry logic
    if (retryAttempt < 5 && navigator.onLine) {
      const backoffDelay = getExponentialBackoff(retryAttempt);
      setRetryCount(retryAttempt + 1);

      reconnectTimeoutRef.current = setTimeout(() => {
        connectWebRTC(retryAttempt + 1);
      }, backoffDelay);
    }
  }
};
```

#### Task 3.3: Update startConversation

```typescript
// File: src/components/VoiceAgent.tsx

const startConversation = async () => {
  try {
    // Initialize audio context for playback
    audioContextRef.current = new AudioContext({ sampleRate: 24000 });

    // Reset session state
    sessionStateRef.current = {
      id: generateSessionId(),
      startTime: Date.now(),
      messageCount: 0,
      toolCallCount: 0,
      errorCount: 0,
      lastMessageTime: Date.now()
    };

    // Initialize audit logging
    auditLogger.initSession(sessionStateRef.current.id);

    // Connect via WebRTC (replaces connectWebSocket)
    await connectWebRTC(0);
  } catch (error) {
    console.error('Error starting conversation:', error);
    sessionStateRef.current.errorCount++;

    auditLogger.log(
      AuditEventType.SESSION_ENDED,
      AuditSeverity.ERROR,
      'Failed to start voice session',
      { error: error instanceof Error ? error.message : 'Unknown error' }
    );

    toast({
      title: "Error",
      description: error instanceof Error ? error.message : 'Failed to start conversation',
      variant: "destructive",
    });
  }
};
```

#### Task 3.4: Update endConversation

```typescript
// File: src/components/VoiceAgent.tsx

const endConversation = () => {
  // Clear reconnection timeout
  if (reconnectTimeoutRef.current) {
    clearTimeout(reconnectTimeoutRef.current);
    reconnectTimeoutRef.current = null;
  }

  // Clear network monitoring
  if (networkMonitoringIntervalRef.current) {
    clearInterval(networkMonitoringIntervalRef.current);
    networkMonitoringIntervalRef.current = null;
  }

  // Disconnect WebRTC client
  webrtcClientRef.current?.disconnect();
  webrtcClientRef.current = null;

  // Close audio context
  audioContextRef.current?.close();

  // Log session summary
  const duration = Date.now() - sessionStateRef.current.startTime;
  console.log('Session ended', {
    sessionId: sessionStateRef.current.id,
    duration: `${duration}ms`,
    messages: sessionStateRef.current.messageCount,
    toolCalls: sessionStateRef.current.toolCallCount,
    errors: sessionStateRef.current.errorCount
  });

  auditLogger.log(
    AuditEventType.SESSION_ENDED,
    sessionStateRef.current.errorCount > 5 ? AuditSeverity.WARNING : AuditSeverity.INFO,
    'Voice session ended',
    {
      duration,
      messages: sessionStateRef.current.messageCount,
      toolCalls: sessionStateRef.current.toolCallCount,
      errors: sessionStateRef.current.errorCount,
    }
  );

  setIsConnected(false);
  setIsRecording(false);
  setIsSpeaking(false);
  setRetryCount(0);
  setToolFeedback(null);
  onSpeakingChange?.(false);

  toast({
    title: "Disconnected",
    description: "Voice conversation ended",
  });
};
```

#### Task 3.5: Update sendTextMessage

```typescript
// File: src/components/VoiceAgent.tsx

const sendTextMessage = (text: string) => {
  if (!webrtcClientRef.current || !isConnected) {
    toast({
      title: "Not Connected",
      description: "Please start the conversation first",
      variant: "destructive",
    });
    return;
  }

  try {
    const event = {
      type: 'conversation.item.create',
      item: {
        type: 'message',
        role: 'user',
        content: [
          {
            type: 'input_text',
            text
          }
        ]
      }
    };

    webrtcClientRef.current.sendMessage(event);
    webrtcClientRef.current.sendMessage({ type: 'response.create' });

    setMessages(prev => [...prev.slice(-9), `You: ${text}`]);
    sessionStateRef.current.messageCount++;
  } catch (error) {
    console.error('Failed to send text message:', error);
    toast({
      title: "Error",
      description: "Failed to send message",
      variant: "destructive",
    });
  }
};
```

#### Task 3.6: Update Network Monitoring (Adapt for WebRTC)

```typescript
// File: src/components/VoiceAgent.tsx

/**
 * Monitor network conditions (still relevant for WebRTC)
 * Note: WebRTC provides its own connection quality metrics
 */
const monitorNetworkConditions = () => {
  if (!webrtcClientRef.current) return;

  // Use existing latency tracking
  if (latencyTrackingRef.current.length > 0) {
    const avgLatency = latencyTrackingRef.current.reduce((a, b) => a + b) / latencyTrackingRef.current.length;

    // Estimate packet loss from message success/failure ratio
    const recentMessages = sessionStateRef.current.messageCount;
    const recentErrors = sessionStateRef.current.errorCount;
    const packetLoss = recentMessages > 0
      ? Math.min(recentErrors / recentMessages, 0.3)
      : 0;

    // Estimate bandwidth based on connection quality
    const quality = getConnectionQuality(latencyTrackingRef.current);
    let bandwidth = 1000000;
    if (quality === 'excellent') bandwidth = 4000000;
    else if (quality === 'good') bandwidth = 2000000;
    else if (quality === 'fair') bandwidth = 1000000;
    else bandwidth = 600000;

    // Log metrics
    console.log('Network Monitoring:', {
      avgLatency: avgLatency.toFixed(2) + 'ms',
      packetLoss: (packetLoss * 100).toFixed(1) + '%',
      bandwidth: (bandwidth / 1000).toFixed(0) + 'kbps',
      quality
    });

    setConnectionQuality(quality);
  }
};
```

#### Task 3.7: Keep handleMessage (No Changes Needed)

The `handleMessage` function (lines 405-495) needs **NO CHANGES** - it already handles OpenAI events correctly. WebRTC will deliver the same event types via data channel.

#### Task 3.8: Keep handleToolCall (No Changes Needed)

The `handleToolCall` function (lines 269-403) needs **NO CHANGES** - tool execution works identically with WebRTC.

#### Task 3.9: Remove startRecording Function

**REMOVE** the `startRecording` function (lines 722-762) - WebRTC handles audio streaming automatically via MediaStream.

```typescript
// DELETE: const startRecording = async () => { ... }
// WebRTC MediaStream replaces this entirely
```

---

### Phase 4: Testing & Validation

**Time**: 2-3 hours
**Purpose**: Comprehensive testing and performance measurement

#### Task 4.1: Unit Tests

Create `src/test/voice-webrtc.test.ts`:

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { RealtimeWebRTCClient } from '@/lib/realtime-webrtc-client';
import { getEphemeralKey } from '@/lib/openai-ephemeral-keys';

describe('Voice WebRTC Integration', () => {
  describe('Ephemeral Key Generation', () => {
    it('should generate valid ephemeral key', async () => {
      // Mock fetch
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: async () => ({
          client_secret: {
            value: 'ek_test_key_123',
            expires_at: Date.now() / 1000 + 60
          }
        })
      });

      const key = await getEphemeralKey();
      expect(key).toBe('ek_test_key_123');
    });

    it('should handle key generation failure', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        statusText: 'Unauthorized'
      });

      await expect(getEphemeralKey()).rejects.toThrow();
    });
  });

  describe('WebRTC Client', () => {
    it('should create client with config', () => {
      const client = new RealtimeWebRTCClient({
        ephemeralKey: 'test_key',
        model: 'gpt-4o-realtime-preview-2024-12-17',
        voice: 'alloy',
        onMessage: vi.fn(),
        onError: vi.fn()
      });

      expect(client).toBeDefined();
    });

    // Add more tests for connection, disconnect, message sending
  });
});
```

Run tests:
```bash
npm run test src/test/voice-webrtc.test.ts
```

#### Task 4.2: Manual Testing Checklist

**Connection Tests:**
- [ ] Voice agent connects successfully
- [ ] Connection quality indicator shows status
- [ ] Network monitoring displays metrics
- [ ] Console shows WebRTC connection logs

**Audio Tests:**
- [ ] Microphone permissions requested
- [ ] User speech is detected (console logs)
- [ ] Agent responds with audio
- [ ] Audio plays through speakers
- [ ] No audio gaps or stuttering

**Tool Execution Tests:**
- [ ] "Add 100 kg of salmon to cooler A" executes correctly
- [ ] Tool feedback UI displays
- [ ] Database record created
- [ ] Success toast shown

**Error Handling Tests:**
- [ ] Network disconnect triggers reconnection
- [ ] Microphone denial shows error message
- [ ] Ephemeral key failure shows error
- [ ] Retry logic works with backoff

**Performance Tests:**
- [ ] Measure latency (before/after migration)
- [ ] Test with network throttling (3G, 4G)
- [ ] Verify 50-100ms improvement

#### Task 4.3: Latency Measurement

Add to `VoiceAgent.tsx`:

```typescript
// Track message latency
let messageStartTime = 0;

// Before sending message (in connectWebRTC or when user speaks)
messageStartTime = performance.now();

// In handleMessage when response arrives
if (event.type === 'response.audio.delta' && messageStartTime > 0) {
  const latency = performance.now() - messageStartTime;
  console.log(`⚡ Response latency: ${latency.toFixed(2)}ms`);

  // Compare with WebSocket latency
  // Expected: 50-100ms improvement
}
```

#### Task 4.4: Browser Compatibility

Test on:
- [ ] Chrome (primary)
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers (if applicable)

---

### Phase 5: Documentation

**Time**: 1 hour
**Purpose**: Update all documentation

#### Task 5.1: Update VOICE_INTEGRATION.md

Add WebRTC section:

```markdown
## WebRTC Architecture

The voice agent uses WebRTC for real-time communication with OpenAI:

### Connection Flow
1. Browser requests ephemeral key from backend
2. Backend generates key via OpenAI API
3. Browser creates RTCPeerConnection
4. SDP offer/answer negotiation
5. Data channel for events
6. Media streams for audio
7. Direct peer-to-peer connection

### Benefits
- 50-100ms lower latency vs WebSocket
- Native audio streaming
- Better connection quality
- Simpler architecture
```

#### Task 5.2: Update VOICE_SETUP.md

Add ephemeral key setup:

```markdown
## Backend Setup (Ephemeral Keys)

### Option 1: Supabase Edge Function

1. Create function:
```bash
supabase functions new openai-ephemeral-key
```

2. Add code (see implementation above)

3. Deploy:
```bash
supabase functions deploy openai-ephemeral-key
```

4. Set secret:
```bash
supabase secrets set OPENAI_API_KEY=sk-your-key-here
```

### Option 2: Custom Backend

Add Express endpoint for `/api/voice/ephemeral-key`
```

#### Task 5.3: Update VOICE_TROUBLESHOOTING.md

Add WebRTC errors:

```markdown
### WebRTC Connection Failed

**Causes:**
- Firewall blocking WebRTC
- STUN server unreachable
- SDP negotiation failed

**Solutions:**
- Check browser WebRTC support
- Disable VPN/firewall temporarily
- Check console for detailed errors
```

#### Task 5.4: Create WEBRTC_MIGRATION_COMPLETE.md

Document the migration:

```markdown
# WebRTC Migration Complete

## Summary
Successfully migrated from WebSocket to WebRTC

## Changes
- Replaced WebSocket with RTCPeerConnection
- Added ephemeral key service
- Updated VoiceAgent component
- All features working

## Performance
- Latency: Reduced by [X]ms
- Connection quality: Improved
- Audio quality: Excellent

## Breaking Changes
- None - all features maintained
```

---

## 🔄 Validation Loop

### Level 1: Syntax and Types
```bash
npm run lint           # ESLint checks
npm run build:dev      # TypeScript compilation
```

**Expected**: No errors, no warnings

### Level 2: Manual Testing
```bash
npm run dev            # Start dev server

# In browser:
# 1. Click "Start Voice Chat"
# 2. Speak: "Hello, can you hear me?"
# 3. Verify: Agent responds with audio
# 4. Test tool: "Add 100 kg of salmon"
# 5. Verify: Tool executes, database updated
# 6. Check console: WebRTC connection logs
```

**Expected**: All features work, console shows WebRTC logs

### Level 3: Network Testing
```bash
# Chrome DevTools:
# Network tab → Throttling → Fast 3G
# Test voice agent works with throttling
# Verify reconnection after disconnect
```

**Expected**: Graceful degradation, auto-reconnection

### Level 4: Performance Testing
```bash
# Measure latency (see Task 4.3)
# Compare: WebSocket vs WebRTC
# Expected: 50-100ms improvement
```

**Expected**: Measurable latency reduction

---

## ✅ Success Criteria Checklist

### Critical (Must Have)
- [ ] WebRTC connection establishes successfully
- [ ] Voice agent responds to spoken prompts
- [ ] Audio flows bidirectionally (mic → OpenAI → speakers)
- [ ] Tool execution works (addInventoryEvent, recordCCP, etc.)
- [ ] Ephemeral keys generate correctly
- [ ] No regressions in existing features
- [ ] Error handling works (network, microphone, API key)
- [ ] Reconnection works after disconnect

### Performance
- [ ] Latency reduced by at least 50ms
- [ ] Audio quality good or better
- [ ] No audio gaps or stuttering
- [ ] Connection quality indicator accurate

### Code Quality
- [ ] No TypeScript errors
- [ ] No ESLint warnings
- [ ] Code follows existing patterns
- [ ] Comprehensive error handling
- [ ] JSDoc documentation added
- [ ] Unit tests pass

### Documentation
- [ ] VOICE_INTEGRATION.md updated with WebRTC
- [ ] VOICE_SETUP.md includes ephemeral key setup
- [ ] VOICE_TROUBLESHOOTING.md covers WebRTC errors
- [ ] Migration completion document created
- [ ] Inline code comments explain WebRTC flow

### Browser Compatibility
- [ ] Works on Chrome
- [ ] Works on Firefox
- [ ] Works on Safari
- [ ] Works on Edge

---

## 🚨 Common Gotchas

### 1. Ephemeral Key Expiration
**Problem**: Keys expire after ~60 seconds
**Solution**: Regenerate key on connection failure, don't cache keys

### 2. ICE Candidates
**Problem**: Connection fails behind strict firewall
**Solution**: STUN server usually sufficient, TURN server for strict NAT

### 3. Data Channel Ordering
**Problem**: Messages sent before channel open are lost
**Solution**: Wait for `dataChannel.onopen` before sending events

### 4. Audio Format Mismatch
**Problem**: OpenAI expects PCM16, 24kHz, mono
**Solution**: Use exact getUserMedia constraints from code above

### 5. SDP Negotiation Timeout
**Problem**: Offer/answer exchange takes too long
**Solution**: Set reasonable timeout (10s), handle failure gracefully

### 6. Media Stream Cleanup
**Problem**: Microphone stays active after disconnect
**Solution**: Call `track.stop()` on all media stream tracks

### 7. Browser Permissions
**Problem**: WebRTC requires microphone permissions
**Solution**: Same as WebSocket - request permissions, show clear error if denied

### 8. Connection State Monitoring
**Problem**: Connection failures not detected
**Solution**: Monitor `RTCPeerConnection.connectionState`, handle 'failed' state

---

## 🔄 Rollback Plan

If WebRTC migration fails:

1. **Immediate Rollback**:
   ```bash
   git revert HEAD~5  # Or specific commit
   ```

2. **Keep Debug Improvements**:
   - Enhanced logging from Phase 1
   - Latest model update (if it helped)

3. **Fix WebSocket Issues**:
   - Use Phase 1 findings to fix WebSocket
   - Consider WebRTC for future iteration

4. **Document Lessons Learned**:
   - What went wrong?
   - What would we do differently?

---

## 📚 References

### OpenAI Documentation
- [Realtime API Guide](https://platform.openai.com/docs/guides/realtime)
- [WebRTC Connection](https://platform.openai.com/docs/guides/realtime#webrtc-connection)
- [Ephemeral Keys](https://platform.openai.com/docs/api-reference/realtime/createSession)

### WebRTC Documentation
- [MDN WebRTC API](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API)
- [RTCPeerConnection](https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection)
- [RTCDataChannel](https://developer.mozilla.org/en-US/docs/Web/API/RTCDataChannel)

### Project Documentation
- `WEBRTC_MIGRATION_STRATEGY.md` - Migration rationale
- `VOICE_WEBRTC_TROUBLESHOOTING_PLAN.md` - Phase-by-phase guide
- `docs/VOICE_INTEGRATION.md` - Voice integration docs

### Archon Tasks
- Project: Safe-catch-flow (a7206f24-59d5-4873-8a6a-01717ce3c95f)
- 5 tasks created (orders 100-80)
- Track progress in Archon

---

## 🎯 Next Steps

1. **Review this PRP** - Ensure all context is clear
2. **Run `/execute-prp PRPs/voice-webrtc-migration.md`** - Begin implementation
3. **Track progress in Archon** - Update task status
4. **Test thoroughly** - Follow validation loop
5. **Document results** - Update docs, create completion report

---

**Estimated Total Time**: 6-8 hours
- Phase 1: Debug (1-2 hours) - Optional but recommended
- Phase 2: Setup (1-2 hours) - Backend + client infrastructure
- Phase 3: Refactor (2-3 hours) - VoiceAgent component
- Phase 4: Testing (2-3 hours) - Comprehensive validation
- Phase 5: Docs (1 hour) - Update documentation

**Risk Level**: LOW
- Small, focused changes
- WebRTC is well-supported
- Fallback to WebSocket available
- All features preserved

**Ready to execute?** Run `/execute-prp PRPs/voice-webrtc-migration.md` to begin!
