# Implementation Plans Directory

This directory contains generated implementation plans from the `/create-plan` command.

## What are Plans?

Plans are comprehensive implementation blueprints that include:
- Feature requirements and goals
- Research findings from knowledge base
- Data models (TypeScript, Zod, SQL schemas)
- Step-by-step implementation blueprint
- Archon project and tasks
- Success criteria checklist
- Gotchas and considerations

## How Plans are Created

```bash
# 1. Create INITIAL.md with requirements
# 2. Generate plan
/create-plan INITIAL.md

# Output: plans/[feature-name]-plan.md
```

## How Plans are Used

```bash
# Execute the plan with task-driven development
/execute-plan plans/[feature-name]-plan.md
```

## Plan Naming Convention

Plans are automatically named based on the feature:
- `supplier-management-plan.md`
- `temperature-dashboard-plan.md`
- `inventory-tracking-plan.md`

## Plan Structure

Each plan includes:

### 1. Goal
Clear feature description and objectives

### 2. Context & Research
- Codebase patterns identified
- External documentation references
- Similar implementations

### 3. Data Models
- TypeScript interfaces
- Zod validation schemas
- SQLite schema changes

### 4. Implementation Blueprint
Detailed steps organized by phase:
- Phase 1: Data Layer
- Phase 2: Business Logic
- Phase 3: UI Layer
- Phase 4: Integration
- Phase 5: Testing & Validation

### 5. Archon Tasks
List of tasks created in Archon MCP server

### 6. Success Criteria
Measurable checklist for completion

### 7. Gotchas & Considerations
Technology-specific warnings and tips

### 8. Validation Commands
Commands to run for quality assurance

## Workflow

```
INITIAL.md → /create-plan → [feature-name]-plan.md → /execute-plan → Implemented Feature
```

## Best Practices

✅ **DO:**
- Review plan thoroughly before executing
- Verify Archon tasks were created
- Check success criteria make sense
- Confirm all referenced files exist

❌ **DON'T:**
- Execute plans without review
- Skip task verification in Archon
- Ignore gotchas section
- Modify plan during execution (stop, edit, re-run instead)

## Example

```bash
# Create requirements
cat > INITIAL.md <<EOF
## FEATURE
Add supplier delivery form with temperature tracking

## EXAMPLES
- src/components/HaccpEventForm.tsx
- src/lib/sqlite-service.ts

## DOCUMENTATION
- https://react-hook-form.com/docs

## OTHER CONSIDERATIONS
- Temperature range: 32-212°F
- Required fields: supplier, product, quantity, temp
EOF

# Generate plan
/create-plan INITIAL.md

# Review generated plan
cat plans/supplier-delivery-form-plan.md

# Execute plan
/execute-plan plans/supplier-delivery-form-plan.md
```

## Related Documentation

- **WORKFLOW.md** - Complete three-phase workflow guide
- **CLAUDE.md** - Project-specific implementation patterns
- **PRP_QUICK_START.md** - Quick start guide (legacy)
- **INITIAL.md** - Template for requirements
- **INITIAL_EXAMPLE.md** - Complete example

---

**Note:** Plans integrate with Archon MCP server for task management. Ensure Archon is configured before running `/create-plan`.
