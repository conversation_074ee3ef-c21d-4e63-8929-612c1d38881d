import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  const { headers } = req;
  const upgradeHeader = headers.get("upgrade") || "";

  if (upgradeHeader.toLowerCase() !== "websocket") {
    return new Response("Expected WebSocket connection", { status: 400 });
  }

  const { socket, response } = Deno.upgradeWebSocket(req);
  
  const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');
  if (!OPENAI_API_KEY) {
    console.error('OPENAI_API_KEY not set');
    socket.close(1011, 'Server error');
    return response;
  }

  let openAISocket: WebSocket | null = null;

  socket.onopen = () => {
    console.log('Client WebSocket connected');
    
    // Connect to OpenAI Realtime API
    openAISocket = new WebSocket(
      "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01",
      {
        headers: {
          "Authorization": `Bearer ${OPENAI_API_KEY}`,
          "OpenAI-Beta": "realtime=v1"
        }
      }
    );

    openAISocket.onopen = () => {
      console.log('Connected to OpenAI Realtime API');
    };

    openAISocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      console.log('Received from OpenAI:', data.type);

      // Send session.update after receiving session.created
      if (data.type === 'session.created') {
        const sessionUpdate = {
          type: 'session.update',
          session: {
            modalities: ["text", "audio"],
            instructions: "You are a helpful assistant for a food safety and traceability system. You can help users with HACCP events, product information, and food safety questions. When users ask about inventory operations, use the available tools to help them. Keep responses concise and relevant.",
            voice: "alloy",
            input_audio_format: "pcm16",
            output_audio_format: "pcm16",
            input_audio_transcription: {
              model: "whisper-1"
            },
            turn_detection: {
              type: "server_vad",
              threshold: 0.5,
              prefix_padding_ms: 300,
              silence_duration_ms: 1000
            },
            temperature: 0.8,
            max_response_output_tokens: "inf",
            tools: [
              {
                type: "function",
                name: "add_inventory_event",
                description: "Add a new inventory event or stock transaction. Use this when a user wants to record a new inventory addition, receipt, or stock adjustment.",
                parameters: {
                  type: "object",
                  properties: {
                    product_id: {
                      type: "string",
                      description: "The unique identifier of the product"
                    },
                    batch_number: {
                      type: "string",
                      description: "The batch or lot number for the inventory"
                    },
                    quantity: {
                      type: "number",
                      description: "The quantity being added"
                    },
                    unit: {
                      type: "string",
                      description: "The unit of measurement (e.g., kg, lbs, boxes)",
                      enum: ["kg", "lbs", "boxes", "units", "liters", "gallons"]
                    },
                    location: {
                      type: "string",
                      description: "Storage location for the inventory"
                    },
                    notes: {
                      type: "string",
                      description: "Additional notes about the inventory event"
                    }
                  },
                  required: ["product_id", "batch_number", "quantity", "unit"]
                }
              },
              {
                type: "function",
                name: "update_product_location",
                description: "Move or update the storage location of a product batch.",
                parameters: {
                  type: "object",
                  properties: {
                    product_id: {
                      type: "string",
                      description: "The unique identifier of the product"
                    },
                    batch_number: {
                      type: "string",
                      description: "The batch or lot number"
                    },
                    new_location: {
                      type: "string",
                      description: "The new storage location"
                    },
                    reason: {
                      type: "string",
                      description: "Reason for the location change (e.g., rotation, restocking, quarantine)",
                      enum: ["rotation", "restocking", "quarantine", "consolidation", "audit", "other"]
                    },
                    notes: {
                      type: "string",
                      description: "Additional notes about the move"
                    }
                  },
                  required: ["product_id", "batch_number", "new_location"]
                }
              },
              {
                type: "function",
                name: "check_product_status",
                description: "Query the current status and inventory details of a product.",
                parameters: {
                  type: "object",
                  properties: {
                    product_id: {
                      type: "string",
                      description: "The unique identifier of the product to check"
                    },
                    batch_number: {
                      type: "string",
                      description: "Optional: specific batch number to check"
                    }
                  },
                  required: ["product_id"]
                }
              },
              {
                type: "function",
                name: "get_haccp_events",
                description: "Retrieve HACCP event records for compliance monitoring and audit trails.",
                parameters: {
                  type: "object",
                  properties: {
                    product_id: {
                      type: "string",
                      description: "Optional: filter by product ID"
                    },
                    severity: {
                      type: "string",
                      description: "Filter by event severity level",
                      enum: ["critical", "major", "minor", "informational"]
                    },
                    limit: {
                      type: "integer",
                      description: "Maximum number of records to return",
                      default: 10,
                      minimum: 1,
                      maximum: 100
                    }
                  }
                }
              },
              {
                type: "function",
                name: "record_ccp_monitoring",
                description: "Record a Critical Control Point (CCP) monitoring measurement for HACCP compliance.",
                parameters: {
                  type: "object",
                  properties: {
                    ccp_name: {
                      type: "string",
                      description: "Name of the critical control point being monitored"
                    },
                    measurement_value: {
                      type: "number",
                      description: "The measured value"
                    },
                    measurement_unit: {
                      type: "string",
                      description: "Unit of the measurement (e.g., °C, pH, minutes)"
                    },
                    critical_limit_min: {
                      type: "number",
                      description: "Minimum acceptable value for this CCP"
                    },
                    critical_limit_max: {
                      type: "number",
                      description: "Maximum acceptable value for this CCP"
                    },
                    product_id: {
                      type: "string",
                      description: "Optional: product ID if monitoring is product-specific"
                    },
                    monitored_by: {
                      type: "string",
                      description: "Name or ID of the person performing the monitoring"
                    },
                    observations: {
                      type: "string",
                      description: "Additional observations or notes"
                    }
                  },
                  required: ["ccp_name", "measurement_value", "measurement_unit", "monitored_by"]
                }
              }
            ]
          }
        };
        openAISocket?.send(JSON.stringify(sessionUpdate));
        console.log('Sent session update with tool definitions');
      }

      // Handle tool calls from OpenAI
      if (data.type === 'response.function_call_arguments.delta') {
        console.log('Tool call in progress:', data.name);
      } else if (data.type === 'response.function_call_arguments.done') {
        console.log('Tool call complete:', data.name);
        // Tool execution will be handled in the client
      }

      // Forward all messages to client
      socket.send(event.data);
    };

    openAISocket.onerror = (error) => {
      console.error('OpenAI WebSocket error:', error);
      socket.send(JSON.stringify({ type: 'error', message: 'OpenAI connection error' }));
    };

    openAISocket.onclose = () => {
      console.log('OpenAI WebSocket closed');
      socket.close();
    };
  };

  socket.onmessage = (event) => {
    console.log('Received from client:', event.data);
    if (openAISocket && openAISocket.readyState === WebSocket.OPEN) {
      openAISocket.send(event.data);
    }
  };

  socket.onerror = (error) => {
    console.error('Client WebSocket error:', error);
  };

  socket.onclose = () => {
    console.log('Client WebSocket closed');
    openAISocket?.close();
  };

  return response;
});