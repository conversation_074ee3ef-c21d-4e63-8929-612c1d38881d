# Voice Integration Troubleshooting & WebRTC Migration

**Status:** Ready to Execute
**Project:** Safe-catch-flow Voice Integration
**Date:** October 24, 2025

---

## 🎯 Mission

Fix voice agent response issue and migrate from WebSocket to WebRTC for optimal performance per OpenAI recommendations.

---

## 📊 Current State Analysis

### Architecture
```
Browser → WebSocket → OpenAI Realtime API
         (wss://api.openai.com/v1/realtime)
```

### Components Status
| Component | Status | Notes |
|-----------|--------|-------|
| Audio Input | ✅ Working | Captured and sent to API |
| WebSocket Connection | ✅ Working | Establishes, heartbeats received |
| Network Monitoring | ✅ Working | Latency/packet loss tracked |
| Jitter Buffer | ✅ Working | Optimized for network conditions |
| **Audio Response** | ❌ **BROKEN** | Agent doesn't reply to speech |
| Audio Playback | ⚠️ Unknown | Queue exists but may not trigger |
| Tool Execution | ✅ Working | Confirmed from recent work |

### Key Files
- [src/components/VoiceAgent.tsx](src/components/VoiceAgent.tsx) - Main component (1051 lines)
- [src/utils/RealtimeAudio.ts](src/utils/RealtimeAudio.ts) - Audio optimization (466 lines)
- [src/lib/voice-tool-executor.ts](src/lib/voice-tool-executor.ts) - Tool execution

### Current Model
- **Model**: `gpt-4o-realtime-preview-2024-10-01` (VoiceAgent.tsx:568)
- **Endpoint**: `wss://api.openai.com/v1/realtime`
- **Format**: PCM16 audio, 24kHz sample rate

---

## 🔍 Phase 1: Debug Current Implementation

**Archon Task**: "Debug current WebSocket implementation audio response flow"
**Priority**: CRITICAL - Must understand issue before migrating
**Estimated Time**: 1-2 hours

### Investigation Steps

#### 1. Enhanced Logging (30 mins)
Add comprehensive logging to [VoiceAgent.tsx](src/components/VoiceAgent.tsx):

```typescript
// In handleMessage function (line 405)
const handleMessage = (event: Record<string, unknown>) => {
  // ADD THIS LOGGING
  console.log('🔍 [VoiceAgent] Message Type:', event.type, {
    fullEvent: event,
    timestamp: new Date().toISOString(),
    connectionState: wsRef.current?.readyState
  });

  if (event.type === 'response.audio.delta') {
    console.log('🔊 [Audio Delta] Received audio chunk', {
      deltaLength: (event.delta as string)?.length,
      isSpeaking: isSpeaking,
      audioContextState: audioContextRef.current?.state
    });
  }

  if (event.type === 'response.audio_transcript.delta') {
    console.log('📝 [Transcript Delta]:', event.delta);
  }

  // Existing code...
}
```

#### 2. Audio Playback Verification (30 mins)
Add logging to [RealtimeAudio.ts](src/utils/RealtimeAudio.ts):

```typescript
// In playAudioData function (line 460)
export const playAudioData = async (audioContext: AudioContext, audioData: Uint8Array) => {
  console.log('🎵 [playAudioData] Called', {
    audioDataLength: audioData.length,
    audioContextState: audioContext.state,
    queueLength: audioQueueInstance?.queue.length || 0
  });

  // Existing code...
}

// In AudioQueue.playNext (line 414)
private async playNext() {
  console.log('▶️ [AudioQueue] Playing next', {
    queueLength: this.queue.length,
    isPlaying: this.isPlaying
  });
  // Existing code...
}
```

#### 3. Test Text Responses (15 mins)
Use browser console to test text input (bypasses audio):

```javascript
// In browser DevTools console when VoiceAgent is connected:
// Find the VoiceAgent instance and call:
sendTextMessage("Hello, can you hear me?");

// This will help determine if:
// - OpenAI is responding at all
// - Text responses work (audio issue only)
// - Message handling is working
```

#### 4. Model Version Check (15 mins)
Try latest model in [VoiceAgent.tsx:568](src/components/VoiceAgent.tsx#L568):

```typescript
// Current:
const wsUrlWithAuth = `${wsUrl}?model=gpt-4o-realtime-preview-2024-10-01`;

// Try latest:
const wsUrlWithAuth = `${wsUrl}?model=gpt-4o-realtime-preview-2024-12-17`;
```

#### 5. Response.create Timing (15 mins)
Experiment with session initialization timing:

```typescript
// In connectWebSocket, line 615
// Current: 100ms delay
setTimeout(() => {
  if (wsRef.current?.readyState === WebSocket.OPEN) {
    wsRef.current.send(JSON.stringify({ type: 'response.create' }));
    console.log('✅ Sent initial response.create to start agent listening');
  }
}, 100);

// Try: Immediate
// Try: 500ms
// Try: Multiple response.create calls
```

### Expected Findings
After Phase 1, we should know:
- ✅ Are response events arriving from OpenAI?
- ✅ Is audio data being received in `response.audio.delta`?
- ✅ Is the audio playback queue working?
- ✅ Does text input/output work?
- ✅ Is it a model issue or transport issue?

**Decision Point**: If debugging reveals a simple fix, apply it. Otherwise, proceed to WebRTC migration which may solve the issue through better transport.

---

## 🚀 Phase 2: WebRTC Migration

**Archon Tasks**: 5 tasks created (orders 100-80)
**Priority**: HIGH - OpenAI recommended approach
**Estimated Time**: 6-8 hours total

### Why WebRTC?

Per [WEBRTC_MIGRATION_STRATEGY.md](WEBRTC_MIGRATION_STRATEGY.md):

> **OpenAI Official Recommendation:**
> "WebRTC connection: Ideal for browser and client-side interactions"
> "WebSocket connection: Ideal for middle tier server-side applications"

**Benefits:**
- ✅ **50-100ms latency reduction** (direct P2P connection)
- ✅ **Simpler architecture** (no server bridge)
- ✅ **Better audio transport** (designed for real-time media)
- ✅ **May fix response issue** (WebRTC handles audio better)

### Architecture Comparison

**Current (WebSocket)**:
```
Browser → WebSocket → OpenAI API
         (manual audio encoding/decoding)
```

**Proposed (WebRTC)**:
```
Browser ←→ OpenAI API (Direct P2P)
         (native audio streams)
```

### Implementation Tasks

#### Task 1: Research & Install Dependencies (Order: 95)
**Time**: 1-2 hours

1. **Research OpenAI WebRTC Implementation**
   ```bash
   # Check official docs
   # https://platform.openai.com/docs/guides/realtime-webrtc

   # Check if SDK exists
   npm info @openai/realtime-api-beta
   npm info @openai/agents
   ```

2. **Install Dependencies**
   ```bash
   # Option A: If official SDK exists
   npm install @openai/realtime-api-beta

   # Option B: Native WebRTC (likely needed)
   # No install required - use browser's RTCPeerConnection
   ```

3. **Create Ephemeral Key Service**
   ```typescript
   // File: src/lib/openai-ephemeral-keys.ts

   export async function getEphemeralKey(): Promise<string> {
     // Option A: Via Supabase Edge Function
     const response = await fetch('/functions/v1/openai-ephemeral-key', {
       method: 'POST',
       headers: { /* Supabase auth */ }
     });

     // Option B: Direct call (requires exposing OPENAI_API_KEY server-side)
     const response = await fetch('/api/voice/ephemeral-key');

     const { client_secret } = await response.json();
     return client_secret.value;
   }
   ```

4. **Backend Endpoint** (Supabase Edge Function or Express)
   ```typescript
   // Supabase: supabase/functions/openai-ephemeral-key/index.ts

   import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

   serve(async (req) => {
     const apiKey = Deno.env.get('OPENAI_API_KEY');

     const response = await fetch('https://api.openai.com/v1/realtime/sessions', {
       method: 'POST',
       headers: {
         'Authorization': `Bearer ${apiKey}`,
         'Content-Type': 'application/json',
       },
       body: JSON.stringify({
         model: 'gpt-4o-realtime-preview-2024-12-17',
         voice: 'alloy'
       })
     });

     const data = await response.json();
     return new Response(JSON.stringify(data), {
       headers: { 'Content-Type': 'application/json' }
     });
   });
   ```

#### Task 2: Create WebRTC Client (Order: 90)
**Time**: 3-4 hours

Create [src/lib/realtime-webrtc-client.ts](src/lib/realtime-webrtc-client.ts):

```typescript
/**
 * WebRTC client for OpenAI Realtime API
 * Handles peer connection, data channels, and media streams
 */

export interface WebRTCClientConfig {
  ephemeralKey: string;
  model: string;
  voice: string;
  onMessage: (message: any) => void;
  onAudioData: (audioData: Uint8Array) => void;
  onError: (error: Error) => void;
}

export class RealtimeWebRTCClient {
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private audioStream: MediaStream | null = null;
  private config: WebRTCClientConfig;

  constructor(config: WebRTCClientConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    try {
      // 1. Create peer connection
      this.peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      // 2. Set up data channel for events
      this.dataChannel = this.peerConnection.createDataChannel('oai-events');
      this.setupDataChannel();

      // 3. Get user media (microphone)
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 24000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      // 4. Add audio tracks to peer connection
      this.audioStream.getTracks().forEach(track => {
        this.peerConnection!.addTrack(track, this.audioStream!);
      });

      // 5. Handle incoming audio tracks
      this.peerConnection.ontrack = (event) => {
        console.log('📡 Received remote audio track');
        // Play received audio
        const audioElement = new Audio();
        audioElement.srcObject = event.streams[0];
        audioElement.play();
      };

      // 6. Create offer
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);

      // 7. Send offer to OpenAI and get answer
      const answer = await this.sendOfferToOpenAI(offer);
      await this.peerConnection.setRemoteDescription(answer);

      // 8. Send session configuration via data channel
      this.sendSessionConfig();

      console.log('✅ WebRTC connection established');
    } catch (error) {
      console.error('❌ WebRTC connection failed:', error);
      this.config.onError(error as Error);
    }
  }

  private setupDataChannel(): void {
    if (!this.dataChannel) return;

    this.dataChannel.onopen = () => {
      console.log('✅ Data channel opened');
    };

    this.dataChannel.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.config.onMessage(message);
      } catch (error) {
        console.error('Failed to parse data channel message:', error);
      }
    };

    this.dataChannel.onerror = (error) => {
      console.error('Data channel error:', error);
      this.config.onError(new Error('Data channel error'));
    };
  }

  private async sendOfferToOpenAI(offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    const response = await fetch('https://api.openai.com/v1/realtime', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.ephemeralKey}`,
        'Content-Type': 'application/sdp'
      },
      body: offer.sdp
    });

    const answerSdp = await response.text();
    return {
      type: 'answer',
      sdp: answerSdp
    };
  }

  private sendSessionConfig(): void {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') return;

    const sessionConfig = {
      type: 'session.update',
      session: {
        modalities: ['text', 'audio'],
        instructions: 'You are a helpful voice assistant for managing seafood inventory.',
        voice: this.config.voice,
        input_audio_format: 'pcm16',
        output_audio_format: 'pcm16',
        turn_detection: { type: 'server_vad' },
        temperature: 0.8,
        max_response_output_tokens: 4096
      }
    };

    this.dataChannel.send(JSON.stringify(sessionConfig));
  }

  sendMessage(message: any): void {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      throw new Error('Data channel not ready');
    }
    this.dataChannel.send(JSON.stringify(message));
  }

  disconnect(): void {
    this.dataChannel?.close();
    this.peerConnection?.close();
    this.audioStream?.getTracks().forEach(track => track.stop());

    this.dataChannel = null;
    this.peerConnection = null;
    this.audioStream = null;
  }
}
```

#### Task 3: Refactor VoiceAgent (Order: 90 continued)
**Time**: 2-3 hours

Update [src/components/VoiceAgent.tsx](src/components/VoiceAgent.tsx):

```typescript
// IMPORT
import { RealtimeWebRTCClient } from '@/lib/realtime-webrtc-client';
import { getEphemeralKey } from '@/lib/openai-ephemeral-keys';

// REPLACE wsRef with webrtcClientRef
const webrtcClientRef = useRef<RealtimeWebRTCClient | null>(null);

// REPLACE connectWebSocket with connectWebRTC
const connectWebRTC = async (retryAttempt: number) => {
  try {
    // Get ephemeral key
    const ephemeralKey = await getEphemeralKey();

    // Create WebRTC client
    webrtcClientRef.current = new RealtimeWebRTCClient({
      ephemeralKey,
      model: 'gpt-4o-realtime-preview-2024-12-17',
      voice: 'alloy',
      onMessage: handleMessage,
      onAudioData: (audioData) => {
        if (audioContextRef.current) {
          playAudioData(audioContextRef.current, audioData);
        }
      },
      onError: (error) => {
        console.error('WebRTC error:', error);
        // Handle retry logic
      }
    });

    // Connect
    await webrtcClientRef.current.connect();
    setIsConnected(true);

    toast({
      title: "Connected",
      description: "Voice agent ready (WebRTC)",
    });
  } catch (error) {
    console.error('WebRTC connection failed:', error);
    // Handle retry...
  }
};

// UPDATE startConversation to use connectWebRTC
const startConversation = async () => {
  try {
    audioContextRef.current = new AudioContext({ sampleRate: 24000 });

    sessionStateRef.current = {
      id: generateSessionId(),
      startTime: Date.now(),
      messageCount: 0,
      toolCallCount: 0,
      errorCount: 0,
      lastMessageTime: Date.now()
    };

    await connectWebRTC(0);
  } catch (error) {
    // Error handling...
  }
};

// UPDATE endConversation
const endConversation = () => {
  webrtcClientRef.current?.disconnect();
  audioContextRef.current?.close();

  setIsConnected(false);
  setIsRecording(false);
  setIsSpeaking(false);

  toast({
    title: "Disconnected",
    description: "Voice conversation ended",
  });
};

// REMOVE startRecording - WebRTC handles audio streaming
// Audio is automatically streamed via RTCPeerConnection
```

#### Task 4: Test & Measure (Order: 85)
**Time**: 2-3 hours

See [Archon task details](https://archon.ai) for comprehensive test plan.

**Key Metrics to Measure:**

```typescript
// Add latency measurement
let messageStartTime = 0;

// Before sending message
messageStartTime = performance.now();
webrtcClientRef.current?.sendMessage({ type: 'input_audio_buffer.append', audio: encodedAudio });

// In handleMessage for response
if (event.type === 'response.audio.delta') {
  const latency = performance.now() - messageStartTime;
  console.log(`🏃 Response latency: ${latency}ms`);
}

// Compare WebSocket vs WebRTC latencies
```

#### Task 5: Documentation (Order: 80)
**Time**: 1 hour

Update documentation per [Archon task](https://archon.ai).

---

## 📋 Execution Checklist

### Phase 1: Debug (1-2 hours)
- [ ] Add enhanced logging to VoiceAgent.tsx
- [ ] Add logging to RealtimeAudio.ts
- [ ] Test text input/output via console
- [ ] Try updated model (2024-12-17)
- [ ] Test different response.create timings
- [ ] Document findings
- [ ] Decide: Fix found OR proceed to WebRTC

### Phase 2: WebRTC Migration (6-8 hours)
- [ ] Research OpenAI WebRTC documentation
- [ ] Create ephemeral key service (backend)
- [ ] Create RealtimeWebRTCClient class
- [ ] Refactor VoiceAgent to use WebRTC
- [ ] Remove/update AudioRecorder for WebRTC
- [ ] Test connection establishment
- [ ] Test audio bidirectional flow
- [ ] Test tool execution
- [ ] Measure latency improvements
- [ ] Update all documentation

### Phase 3: Validation
- [ ] All voice features work
- [ ] Latency reduced by 50-100ms
- [ ] No regressions
- [ ] Tests pass
- [ ] Documentation complete

---

## 🎯 Success Criteria

### Must Have
- ✅ Voice agent responds to spoken prompts
- ✅ Audio flows bidirectionally
- ✅ Tool execution works (inventory, CCP, etc.)
- ✅ Latency improved (if WebRTC migration done)
- ✅ No regressions in existing features

### Nice to Have
- ✅ Latency reduced by 50-100ms (WebRTC)
- ✅ Simpler architecture (no WebSocket bridge)
- ✅ Better error messages
- ✅ Comprehensive tests

---

## 📞 Next Steps

1. **Start with Phase 1 Debugging** (1-2 hours)
   - May reveal simple fix
   - Provides understanding for WebRTC migration

2. **Proceed to WebRTC Migration** (6-8 hours)
   - Even if fix found, WebRTC provides benefits:
     - Lower latency
     - Simpler architecture
     - Better audio quality
     - OpenAI recommended approach

3. **Track Progress in Archon**
   - 5 tasks created (orders 100-80)
   - Update task status as you complete each step
   - Mark task "fa075fcf-94dd-4c89-8e98-913ae85abcfe" as complete when done

---

## 📚 References

### Documentation
- [WEBRTC_MIGRATION_STRATEGY.md](WEBRTC_MIGRATION_STRATEGY.md) - Full migration strategy
- [VOICE_FIX_PLAN.md](VOICE_FIX_PLAN.md) - Original fix plan
- [VOICE_TROUBLESHOOTING.md](docs/VOICE_TROUBLESHOOTING.md) - Error reference
- [OpenAI Realtime API Docs](https://platform.openai.com/docs/guides/realtime)

### Key Files
- [src/components/VoiceAgent.tsx](src/components/VoiceAgent.tsx) - Main component
- [src/utils/RealtimeAudio.ts](src/utils/RealtimeAudio.ts) - Audio optimization
- [src/lib/voice-tool-executor.ts](src/lib/voice-tool-executor.ts) - Tool execution

### Archon Project
- **Project ID**: a7206f24-59d5-4873-8a6a-01717ce3c95f
- **Project**: Safe-catch-flow Voice Integration
- **Active Tasks**: 6 (including new WebRTC tasks)

---

**Ready to start?** Begin with Phase 1 debugging, then proceed to WebRTC migration!
