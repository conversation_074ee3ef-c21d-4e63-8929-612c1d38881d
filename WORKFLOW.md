# Three-Phase Structured Development Workflow

Complete guide to the systematic development approach used in this project.

## 🎯 Overview

This project uses a three-phase structured approach that transforms ad-hoc AI interactions into a systematic, repeatable process for consistently producing high-quality code.

```
Phase 1: Vibe Planning     → Exploration & Research
Phase 2: Implementation     → Structured Planning & Execution
Phase 3: Validation        → Automated & Manual Review
```

## 📋 Quick Reference

| Phase | Command | Purpose | Output |
|-------|---------|---------|--------|
| **1. Planning** | `/primer [mode]` | Explore & research | Findings summary |
| **2A. Create Plan** | `/create-plan [INITIAL.md]` | Generate blueprint | Archon project + plan doc |
| **2B. Execute** | `/execute-plan [plan.md]` | Implement tasks | Working feature |
| **3. Validate** | Built into execute | Verify quality | Production-ready code |

## 🚀 Getting Started (5 Minutes)

### Option 1: Full Workflow (Recommended for New Features)

```bash
# 1. Explore the feature area (Phase 1)
/primer existing supplier-management

# 2. Create INITIAL.md based on findings
# [Edit INITIAL.md with requirements]

# 3. Generate implementation plan (Phase 2A)
/create-plan INITIAL.md

# 4. Execute the plan (Phase 2B + 3)
/execute-plan plans/supplier-management-plan.md
```

### Option 2: Quick Start (Skip Primer)

```bash
# 1. Create INITIAL.md directly
# [Define requirements in INITIAL.md]

# 2. Generate and execute
/create-plan INITIAL.md
/execute-plan plans/[feature-name]-plan.md
```

### Option 3: Legacy PRP Workflow

```bash
# For backward compatibility
/generate-prp INITIAL.md
/execute-prp PRPs/[feature-name].md
```

---

## Phase 1: 🎨 Vibe Planning & Exploration

### Purpose
Unstructured exploration to understand the landscape before formal planning.

### When to Use
- **New to codebase:** Understanding architecture and patterns
- **Complex feature:** Exploring multiple implementation approaches
- **Technology research:** Evaluating libraries and tools
- **Architecture decisions:** Designing data models and flows

### Command Usage

```bash
# Analyze current codebase
/primer analyze

# Explore existing feature area
/primer existing [feature-area]

# Research for new project
/primer new-project [project-name]
```

### What Happens

1. **Archon Check:** Searches for existing projects/tasks
2. **Codebase Analysis:** Reviews relevant files and patterns
3. **Knowledge Base Search:** Queries Archon RAG for documentation
4. **Research:** Fetches external documentation if needed
5. **Summary Generation:** Provides findings and recommendations

### Output

```markdown
# Primer Summary: [Feature Name]

## Codebase Analysis
[Key patterns, similar implementations]

## Research Findings
[Technology insights, best practices]

## Recommendations
[Suggested approach with rationale]

## Open Questions
[Decisions needed]

## Next Steps
1. Create INITIAL.md
2. Run /create-plan
```

### Example

```bash
/primer existing temperature-monitoring

# Output includes:
# - Existing temperature reading patterns
# - SQLite service methods for temperature data
# - UI components for data display
# - Recommended approach for dashboard
```

---

## Phase 2A: 📝 Create Plan

### Purpose
Generate comprehensive implementation plan from requirements.

### Prerequisites
- ✅ Completed `/primer` (optional but recommended)
- ✅ Created INITIAL.md with requirements

### INITIAL.md Structure

```markdown
## FEATURE
Clear description of what to build

## EXAMPLES
- Path/to/similar/file.tsx (pattern to follow)
- Another/example.ts (reference implementation)

## DOCUMENTATION
- https://library.com/docs (relevant section)
- https://api-docs.com (API reference)

## OTHER CONSIDERATIONS
- Validation rules
- Error handling requirements
- Performance constraints
- Security requirements
```

### Command Usage

```bash
/create-plan INITIAL.md
/create-plan features/auth/INITIAL.md
```

### What Happens

1. **Parse Requirements:** Extract from INITIAL.md
2. **Archon Setup:** Create/check project
3. **Research Phase:**
   - Search knowledge base
   - Analyze referenced examples
   - Review documentation
4. **Blueprint Generation:**
   - Data models (TypeScript, Zod, SQL)
   - Implementation steps with pseudocode
   - Integration points
5. **Task Creation:** Generate Archon tasks
6. **Success Criteria:** Define validation checklist

### Output

```markdown
# Plan Document: plans/[feature-name]-plan.md
- Goal and requirements
- Research findings
- Data models (TypeScript, Zod, SQL)
- Implementation blueprint
- Success criteria checklist
- Gotchas and considerations

# Archon Project
- Project created with ID
- Tasks created (todo status)
- Ready for execution
```

### Task Granularity

**Feature-Specific Projects:**
- Detailed implementation tasks (setup, implement, test, document)
- Each task: 30 minutes - 4 hours of work

**Codebase-Wide Projects:**
- Feature-level tasks
- Larger scope per task

**Default:** When in doubt, create more granular tasks.

---

## Phase 2B: ⚡ Execute Plan

### Purpose
Systematically implement plan using task-driven development.

### Prerequisites
- ✅ Plan document exists (from `/create-plan`)
- ✅ Archon project created with tasks

### Command Usage

```bash
/execute-plan plans/supplier-management-plan.md
/execute-plan PRPs/temperature-dashboard.md
```

### The Task-Driven Development Cycle

```
┌─────────────────────────────────────┐
│ 1. GET TASK                         │
│    find_tasks()                     │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│ 2. START WORK                       │
│    manage_task("update", "doing")   │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│ 3. RESEARCH (MANDATORY)             │
│    rag_search_knowledge_base()      │
│    rag_search_code_examples()       │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│ 4. IMPLEMENT                        │
│    Write/Edit code                  │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│ 5. MARK REVIEW                      │
│    manage_task("update", "review")  │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│ 6. VALIDATE                         │
│    Level 1: lint + build            │
│    Level 2: manual test             │
│    Level 3: data integrity          │
└──────┬──────────────┬───────────────┘
       ↓              ↓ (if fails)
    SUCCESS        FIX ERRORS
       ↓              ↓
┌─────────────────────────────────────┐
│ 7. COMPLETE                         │
│    manage_task("update", "done")    │
└──────────────┬──────────────────────┘
               ↓
┌─────────────────────────────────────┐
│ 8. NEXT TASK                        │
│    Back to step 1                   │
└─────────────────────────────────────┘
```

### Critical Rules

**MANDATORY Archon Workflow:**
1. ✅ **ALWAYS** get tasks from Archon (NOT TodoWrite)
2. ✅ **ALWAYS** mark "doing" before starting
3. ✅ **ALWAYS** research before coding
4. ✅ **ALWAYS** mark "review" after implementation
5. ✅ **ALWAYS** validate before marking "done"
6. ✅ **NEVER** skip research phase
7. ✅ **NEVER** have multiple tasks "doing" simultaneously

### Validation Levels

**Level 1: Syntax & Types**
```bash
npm run lint        # ESLint checks
npm run build:dev   # TypeScript compilation
```

**Level 2: Manual Testing**
```bash
npm run dev         # Start dev server
# Test in browser:
# - Navigate to feature
# - Test happy path & edge cases
# - Check browser console
# - Verify data persistence
```

**Level 3: Data Integrity**
- SQLite operations work
- Staging dashboard shows records
- Sync to Supabase works (if applicable)
- Error handling functions properly

### Research Best Practices

**Keep queries SHORT (2-5 keywords):**

✅ **GOOD:**
```bash
rag_search_knowledge_base(query="React Hook Form")
rag_search_code_examples(query="SQLite insert")
```

❌ **BAD:**
```bash
rag_search_knowledge_base(query="how to implement form validation with React Hook Form and Zod in TypeScript")
```

### Output

Throughout execution:
```markdown
## Task: [task-title] (doing)
### Research: [findings]
### Implementation: [what was done]
### Validation: ✅ All passed
### Status: Marked complete

## Task: [next-task] (doing)
[Continue cycle...]

---

## FINAL: All Tasks Complete ✓
✅ All validation passed
✅ Success criteria met
✅ Ready for deployment
```

---

## Phase 3: ✅ Validation & Review

### Purpose
Ensure code quality and feature completeness through automated and manual validation.

### Automated Validation (Built into `/execute-plan`)

**Runs after EVERY task:**
1. ESLint checks (`npm run lint`)
2. TypeScript compilation (`npm run build:dev`)
3. Manual browser testing
4. Data integrity verification

**Task is NOT marked complete until ALL validation passes.**

### Manual Validation (Human Oversight)

**Strategic review points:**
- After major milestones
- Before marking project complete
- Before deployment

**Review checklist:**
- [ ] Feature works as specified
- [ ] No regressions in existing features
- [ ] User experience is smooth
- [ ] Edge cases handled properly
- [ ] Error messages are user-friendly
- [ ] Performance is acceptable
- [ ] Code follows project patterns

### Success Criteria Verification

Before project completion, verify ALL criteria from plan:
- [ ] No TypeScript errors
- [ ] No ESLint warnings
- [ ] UI renders correctly
- [ ] Form validation works
- [ ] Data persists to SQLite
- [ ] Staging dashboard shows records
- [ ] Sync to Supabase works
- [ ] Error handling implemented
- [ ] User feedback (toasts) working
- [ ] Browser console clean
- [ ] Manual tests pass

---

## 🔧 Key Components

### Archon MCP Server (Task Management + Knowledge Base)

**Core Functions:**

**Projects:**
```bash
find_projects(query="...")
manage_project("create", title="...", description="...")
```

**Tasks:**
```bash
find_tasks(filter_by="status", filter_value="todo")
manage_task("create", project_id="...", title="...")
manage_task("update", task_id="...", status="doing")
```

**Knowledge Base:**
```bash
rag_get_available_sources()
rag_search_knowledge_base(query="...", source_id="...")
rag_search_code_examples(query="...")
rag_read_full_page(page_id="...")
```

### Task Status Flow

```
todo → doing → review → done
        ↑         ↓
        └─ fix ───┘
```

**Rules:**
- Only ONE task "doing" at a time
- Tasks go to "review" after implementation
- Fix failures before marking "done"
- Never mark "done" with failing validation

### Sub-Agents

**Codebase Analyst:** (Used by `/primer`)
- Analyzes existing codebases
- Identifies patterns and conventions
- Maps integration points
- Documents technical constraints

**Validator:** (Built into `/execute-plan`)
- Automated code review
- Runs validation loops
- Verifies success criteria
- Reports issues for fixing

---

## 🎓 Success Factors

### Structured Approach
- Each phase builds on previous
- Clear outputs and inputs
- Defined success criteria

### Context Preparation
- Thorough research upfront
- Knowledge base leveraged
- Patterns documented
- Gotchas identified

### Iterative Refinement
- Trust but verify
- Validate early and often
- Fix immediately
- No batching of completions

### Tool Integration
- Archon for task management
- RAG for knowledge retrieval
- Validation loops for quality
- Sub-agents for specialization

---

## 📊 Comparison: New vs Legacy Workflows

| Feature | New Workflow | Legacy PRP |
|---------|--------------|------------|
| **Phases** | 3 (Plan, Implement, Validate) | 2 (Generate, Execute) |
| **Exploration** | `/primer` command | Manual |
| **Planning** | `/create-plan` | `/generate-prp` |
| **Execution** | `/execute-plan` with Archon | `/execute-prp` with TodoWrite |
| **Task Management** | Archon MCP | TodoWrite |
| **Research** | Integrated RAG searches | Manual references |
| **Validation** | 3-level automated | Manual |
| **Sub-Agents** | Codebase Analyst, Validator | None |

**Recommendation:** Use new workflow for all new work. Legacy PRP commands maintained for backward compatibility.

---

## 💡 Tips & Best Practices

### Planning Phase
✅ **DO:**
- Explore thoroughly with `/primer`
- Reference existing code extensively
- Include specific documentation sections
- Define clear success criteria

❌ **DON'T:**
- Skip exploration for complex features
- Use vague requirements
- Omit validation rules
- Forget error handling

### Implementation Phase
✅ **DO:**
- Research before EVERY task
- Follow task cycle religiously
- Validate after each task
- Fix errors immediately
- Use short RAG queries (2-5 keywords)

❌ **DON'T:**
- Skip research phase
- Work on multiple tasks simultaneously
- Batch task completions
- Mark tasks done with failing tests
- Use TodoWrite (use Archon instead)

### Validation Phase
✅ **DO:**
- Run all validation levels
- Test edge cases thoroughly
- Check browser console
- Verify data integrity
- Ensure no regressions

❌ **DON'T:**
- Skip manual testing
- Ignore warnings
- Deploy with failing validation
- Skip success criteria verification

---

## 🚦 Common Scenarios

### Scenario 1: Adding a New Form

```bash
# 1. Explore existing form patterns
/primer analyze

# 2. Create INITIAL.md
# FEATURE: Add supplier delivery form
# EXAMPLES: src/components/HaccpEventForm.tsx
# ...

# 3. Generate plan
/create-plan INITIAL.md

# 4. Execute
/execute-plan plans/supplier-delivery-form-plan.md

# Result: Working form with validation, persistence, sync
```

### Scenario 2: Complex Feature with Multiple Components

```bash
# 1. Research thoroughly
/primer existing inventory-tracking

# 2. Create detailed INITIAL.md with all components

# 3. Generate comprehensive plan
/create-plan INITIAL.md

# 4. Execute task by task
/execute-plan plans/inventory-tracking-plan.md

# Result: Full feature with multiple integrated components
```

### Scenario 3: Bug Fix (Quick)

```bash
# Skip primer, go straight to plan
# Create INITIAL.md describing bug and fix

/create-plan INITIAL.md
/execute-plan plans/fix-temperature-validation-plan.md
```

### Scenario 4: Continue Incomplete Work

```bash
# Check existing project
find_projects(query="supplier")

# Get pending tasks
find_tasks(filter_by="status", filter_value="todo", project_id="proj-123")

# Continue with /execute-plan (it will pick up remaining tasks)
/execute-plan plans/supplier-management-plan.md
```

---

## 📚 Additional Resources

- **INITIAL.md** - Template for requirements
- **INITIAL_EXAMPLE.md** - Complete example
- **PRP_QUICK_START.md** - Quick start guide (legacy)
- **.claude/commands/** - Slash command implementations
- **plans/** - Generated plan documents
- **PRPs/** - Legacy PRP documents

---

## 🎉 Summary

The three-phase workflow transforms AI-assisted development from ad-hoc to systematic:

1. **Phase 1 (Primer):** Explore and understand
2. **Phase 2A (Create Plan):** Research and blueprint
3. **Phase 2B (Execute Plan):** Implement task-by-task
4. **Phase 3 (Validate):** Verify quality

**Key to Success:** Follow the process, trust the system, and validate continuously.

Happy building! 🚀
