# Voice Integration Enhancement - Feature Specification

## FEATURE

Enhance the existing voice agent integration to make it a **practical supplementary input method** for HACCP form entry and all other forms in the application. Currently, voice works as an isolated chat interface; this enhancement bridges voice input to actual form data entry with natural conversation support, intent clarification, automatic user attribution, and mixed voice+text error handling.

### Feature Goals

1. **Supplementary Input Method:** Voice input should augment (not replace) traditional form filling
2. **Natural Conversation:** Accept natural language input, then ask for clarification if intent is ambiguous
3. **Semi-Critical Offline:** Support voice when offline, with graceful degradation (text-only fallback)
4. **Confirmation Workflow:** Require user confirmation before submitting voice-sourced data
5. **User Attribution:** Automatically track who provided voice input (via current auth user)
6. **Mixed Error Handling:** Use combination of voice corrections and text-based inputs
7. **Universal Form Support:** Enable voice input on ALL forms, not just HACCP events

## EXAMPLES

### Pattern: HaccpEventForm (src/components/HaccpEventForm.tsx)
- Form structure with React Hook Form + Zod validation
- SQLite integration via `sqliteService.insertHaccpEvent()`
- Staging workflow with confirmation dialog
- Fields that should support voice: `species`, `supplier`, `temperature`, `weight`, `notes`, `eventType`, `batchNumber`
- Current: No voice connection

### Pattern: RealtimeAudio (src/utils/RealtimeAudio.ts)
- Audio capture and encoding working correctly (PCM16, 24kHz)
- Playback queue and WAV conversion implemented
- Should reuse this for voice input
- Handles: AudioRecorder, encodeAudioForAPI, playAudioData, AudioQueue

### Pattern: VoiceAgent (src/components/VoiceAgent.tsx)
- WebSocket connection to OpenAI Realtime API working correctly
- Message display and quick actions implemented
- Currently isolated from forms - needs callback interface
- Should expose methods to populate form fields and show confirmation

### Pattern: SQLite Service (src/lib/sqlite-service.ts)
- Staging workflow with `synced_to_supabase` and `sync_confirmed` flags
- All records include `created_by` field for attribution
- Pattern to follow: Insert → Display in staging dashboard → User confirms → Mark confirmed
- Methods to use: insertHaccpEvent, insertTemperatureReading, insertProduct, insertInventory

### Pattern: Supabase Edge Function (supabase/functions/realtime-chat/index.ts)
- WebSocket bridge to OpenAI Realtime API working
- Session configuration with Whisper transcription and VAD already implemented
- Can be enhanced with tool calling for structured output
- **Note:** Currently uses WebSocket (middletier server approach). Phase 4 will migrate to WebRTC (direct browser connection, OpenAI's recommended approach)

## DOCUMENTATION

### OpenAI Realtime API
- **Current Session Config:** supabase/functions/realtime-chat/index.ts
- **Tool Calling Docs:** https://platform.openai.com/docs/guides/realtime#tools
- **Function Schemas:** Define structured outputs for form data extraction
- **Voice to Text:** Whisper transcription already enabled
- **Voice Activity Detection:** VAD already enabled for better UX

### React Hook Form API
- **setValue():** Programmatically set form field values
- **getValues():** Retrieve current form values
- **trigger():** Validate specific fields before submission
- **watch():** Monitor form field changes
- **Docs:** https://react-hook-form.com/api/useform

### Entity Extraction Patterns
- **Temperature:** Numbers followed by °F, °C, F, C, degrees
- **Weight:** Numbers followed by kg, lb, lbs, pound, g, oz
- **Dates/Times:** Parse "today", "now", "MM/DD", "HH:MM"
- **Selections:** Fuzzy match against predefined lists (species, suppliers)
- **Alphanumeric:** Batch numbers, product codes

### Web Audio API
- **AudioContext:** Already initialized in VoiceAgent.ts (24kHz)
- **getUserMedia:** Already implemented in RealtimeAudio.ts with enhancements
- **ScriptProcessorNode:** Currently used for audio capture

## OTHER CONSIDERATIONS

### User Attribution & Permissions
- Current user available via Supabase auth context (or useState in App.tsx)
- Auto-populate `created_by` field from current auth user
- Voice inputs should be indistinguishable from form inputs in database
- Separate audit trail optional (could add `voice_sourced: boolean` field)

### Form Field Mapping Strategy
- Priority 1: HaccpEventForm (temperature, weight, supplier, species, notes, eventType)
- Priority 2: ProductForm (name, category, supplier, description)
- Priority 3: InventoryForm (quantity, location, product_id)
- Generic approach: Extract entities → Match to form schema → Validate → Confirm

### Validation & Confirmation Flow
1. Voice input captured
2. Entity extraction (temperature: "35 degrees" → 35)
3. Validate against form schema (Zod)
4. If valid: Show confirmation dialog with extracted fields
5. If invalid: Ask user to clarify specific field
6. User confirms → Form submission → Database insert
7. Data appears in staging dashboard with voice attribution

### Offline Support
- WebSocket (current): Requires internet for voice chat
- Graceful fallback: If WebSocket unavailable, show text input box
- Queue system: Buffer voice inputs offline, sync when online
- SQLite staging: Already fully offline-capable
- Confirmation: Already supports offline → online sync workflow

### Error Recovery & Correction
- If transcription fails: Show transcript, ask for text correction
- If entity extraction fails: Ask "Did you mean [value]?"
- If validation fails: "I didn't understand the [field]. Could you repeat?"
- If network drops: "Connection lost. Continuing with text input"
- Support voice + text mixed: "Set temperature to [voice], supplier is [text]"

### Multi-form Architecture
- Create `useVoiceForm` hook that works with any form
- Pass form configuration: field names, types, validation, options
- Reuse audio infrastructure from RealtimeAudio.ts
- Generic intent parser for common patterns: "record", "add", "update"

### Quick Actions & Voice Commands
- Current: "Ask about HACCP", "Food Safety Help"
- Enhance: "Record receiving event", "Add product", "Check inventory"
- Commands: "Submit", "Cancel", "Clear", "Repeat", "Confirm"

## IMPLEMENTATION APPROACH

### Phase 1: HACCP Form Integration (Core MVP)
**Goal:** Voice input successfully populates HACCP event form

1. Create callback interface in VoiceAgent
   - `onDataExtracted(data: Partial<HaccpEvent>)`
   - `onConfirmationNeeded(field: string)`

2. Implement entity extraction service
   - Extract temperature, weight, species, supplier from transcription
   - Use regex for numbers + units
   - Fuzzy match for species/supplier against predefined lists

3. Add form field population
   - Use React Hook Form `setValue()` to populate extracted fields
   - Show confirmation dialog before form submission
   - Track which fields came from voice

4. Integrate with staging workflow
   - Populate `created_by` with current user
   - Mark records for confirmation
   - Existing StagingDashboard shows voice-sourced entries

5. Testing: Record common voice inputs for HACCP events

### Phase 2: Confirmation & Error Handling
**Goal:** Robust error recovery and user control

1. Confirmation dialog
   - Show extracted fields before submission
   - Allow user to approve, modify, or reject

2. Mixed input fallback
   - If voice fails, provide text input for that field
   - Support "Actually, [correction]" pattern

3. Confidence scoring
   - Show confidence for ambiguous extractions
   - Ask user to confirm if below threshold

4. User feedback loop
   - Toast notifications for success/error
   - Clear messages for what went wrong

### Phase 3: Multi-form Support
**Goal:** Enable voice input on all forms

1. Generic form integration
   - Create `useVoiceForm` hook
   - Pass form metadata (fields, types, options)

2. Extend to 3+ form types
   - Test with ProductForm, InventoryForm
   - Ensure consistent UX across forms

3. Form-agnostic parsing
   - Common patterns: "record", "add", "update"
   - Field-specific: temperature, weight, date, selection

### Phase 4: WebRTC Migration & Advanced Features
**Goal:** Optimize to OpenAI best practices with advanced capabilities

**Phase 4A: WebRTC Migration (RECOMMENDED)**
Per OpenAI Realtime API documentation, WebRTC is the recommended approach for browser applications:
- Direct peer-to-peer connection (no server intermediary needed)
- ~50-100ms lower latency vs WebSocket
- Better for real-time voice UX in field operations
- OpenAI's official Agents SDK uses WebRTC by default

Migration tasks:
1. Migrate from custom WebSocket to OpenAI Agents SDK (`@openai/agents/realtime`)
2. Use ephemeral keys instead of edge function bridge
3. Remove/repurpose Supabase edge function (realtime-chat)
4. Direct browser-to-OpenAI connection with WebRTC
5. Maintain all form integration from Phase 1-3

**Phase 4B: Advanced Features (Optional)**
1. Conversation context
   - Multi-turn form filling: "Now enter temperature" → continue from context

2. Voice commands
   - Submit, cancel, clear, repeat, read-back

3. Offline support
   - Buffer inputs, sync when online
   - Graceful fallback if WebRTC unavailable

4. OpenAI Tool Calling integration
   - AI-powered entity extraction (no manual regex)
   - Structured form data from voice

5. Performance optimization
   - Monitor latency improvements from WebRTC
   - Cache entity extraction models

6. Documentation & testing
   - User guide for voice features
   - Comprehensive test coverage

## SUCCESS CRITERIA

### Functional Requirements
- [ ] Voice input successfully extracts and populates HACCP event form fields
- [ ] Temperature, weight, supplier, species extracted from natural language
- [ ] Extracted data displayed in confirmation dialog before submission
- [ ] User can confirm, reject, or modify extracted data
- [ ] Form submission includes automatic user attribution (created_by)
- [ ] Voice-sourced data appears in SQLite staging table
- [ ] Works with at least 3 different form types

### Error Handling
- [ ] Graceful fallback to text input if WebSocket unavailable
- [ ] Mixed voice+text input supported (some fields voice, some text)
- [ ] Clear error messages guide users on field requirements
- [ ] Transcription failures don't break form functionality
- [ ] Network disconnection doesn't lose form data

### User Experience
- [ ] Natural conversation flow (not robotic command structure)
- [ ] Intent clarification works: "Record receiving" → "For which product?"
- [ ] Users can correct voice input via voice or text
- [ ] Confirmation dialog shows exactly what will be submitted
- [ ] Accessible for users with different input capabilities

### Data Integrity
- [ ] Form validation (Zod) applies to voice-extracted data
- [ ] Correct user attribution in all voice-sourced records
- [ ] Timestamps accurate (voice input time, form submission time)
- [ ] No data loss on network interruption
- [ ] Staging workflow integrates seamlessly

### Code Quality
- [ ] Passes linting (`npm run lint`)
- [ ] TypeScript compilation succeeds (`npm run build:dev`)
- [ ] No regressions in existing form functionality
- [ ] Proper error handling with try-catch
- [ ] Code documented with comments and JSDoc

## TECHNICAL CONSTRAINTS

**Phases 1-3 (Core Implementation):**
- Must use existing WebSocket infrastructure (no new backend changes initially)
- Must integrate with current SQLite staging workflow
- Must work with React Hook Form validation (Zod schemas)
- Must preserve existing VoiceAgent audio handling (RealtimeAudio utilities)
- Must support 24kHz PCM16 audio format (current standard)
- Must not break offline-first architecture
- Must maintain existing authentication patterns

**Phase 4 (WebRTC Migration):**
- Will migrate from WebSocket to WebRTC (direct browser-to-OpenAI connection)
- Will adopt OpenAI Agents SDK for recommended best practice
- Supabase edge function (realtime-chat) can be deprecated/repurposed
- All form integration from Phase 1-3 maintained

## TESTING STRATEGY

### Manual Test Scenarios
1. **Basic entry:** "Record receiving event for salmon at 35 degrees"
2. **Clarification:** "Record receiving" → "For which product?" → "Salmon"
3. **Mixed input:** Voice for temperature + weight, text for supplier
4. **Confirmation:** Extract fields → Show dialog → User confirms → Submit
5. **Correction:** "Actually, 37 degrees" → Update field before submit
6. **Error recovery:** Network drops → Text input → Resume
7. **All forms:** Test voice input on HACCP, Product, Inventory forms
8. **Attribution:** Verify created_by matches logged-in user
9. **Staging:** Entries appear in dashboard with voice indicator

### Validation Points
- Extracted values match form schema (Zod validation passes)
- SQLite insert succeeds with correct data
- Timestamps accurate
- User attribution correct (created_by populated)
- No duplicate entries
- Staging dashboard shows voice-sourced entries
- Sync to Supabase includes voice attribution

## DEFINITION OF DONE

1. ✅ Feature fully implemented and tested
2. ✅ Passes all linting (`npm run lint`) and build (`npm run build:dev`)
3. ✅ TypeScript types complete and correct
4. ✅ Code documented with comments explaining voice-specific logic
5. ✅ Works with existing SQLite staging workflow
6. ✅ No regressions in HaccpEventForm or other forms
7. ✅ User attribution (created_by) tracked automatically
8. ✅ Confirmation workflow tested and working
9. ✅ Error handling covers: network issues, permissions, validation
10. ✅ README or CLAUDE.md updated with voice feature usage guide

## ACCEPTANCE TESTS

```gherkin
Feature: Voice Form Input
  Scenario: Record HACCP event via voice
    Given voice agent is connected
    When user says "Record receiving event for salmon at 35 degrees"
    Then extracted fields show:
      | Field    | Value  |
      | eventType| receiving |
      | species  | salmon |
      | temperature | 35 |
    And confirmation dialog appears
    When user confirms
    Then HACCP event created in SQLite with created_by = current_user

  Scenario: Mixed voice and text input
    Given voice agent is connected and HaccpEventForm is open
    When user provides temperature via voice and supplier via text
    Then both fields populated correctly
    And form can submit successfully

  Scenario: Natural conversation with clarification
    Given voice agent is connected
    When user says "Record receiving"
    Then agent asks "For which product?"
    When user says "Salmon"
    Then agent asks "At what temperature?"
    When user says "35 degrees"
    Then fields populated with eventType=receiving, species=salmon, temperature=35
```
