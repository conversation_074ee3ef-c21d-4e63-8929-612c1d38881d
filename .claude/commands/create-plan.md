# Create Plan - Generate Structured Plan of Attack

Generate a comprehensive, structured implementation plan from a Product Requirements Document (PRD).

## Purpose

The `/create-plan` command bridges the exploration phase (`/primer`) and execution phase (`/execute-plan`). It takes the requirements defined in INITIAL.md and generates a detailed, step-by-step plan of attack that leverages all context engineering components.

## Usage

```
/create-plan [path-to-INITIAL.md]
```

Example:
```
/create-plan INITIAL.md
/create-plan features/supplier-management/INITIAL.md
```

## Prerequisites

Before running `/create-plan`:
1. ✅ Completed `/primer` exploration (recommended)
2. ✅ Created INITIAL.md with detailed requirements
3. ✅ Archon project exists (or will be created)
4. ✅ Context engineering components available (Archon, RAG, etc.)

## Process

### 1. **Load Requirements from INITIAL.md**

   Parse the PRD structure:
   ```markdown
   ## FEATURE
   [Clear description of what to build]

   ## EXAMPLES
   [Pointers to similar code in codebase]

   ## DOCUMENTATION
   [External docs and API references]

   ## OTHER CONSIDERATIONS
   [Special requirements, patterns, constraints]
   ```

### 2. **Archon Project Setup (CRITICAL)**

   **MANDATORY first steps:**
   ```bash
   # Check for existing project
   find_projects(query="[feature-name]")

   # If not exists, create it
   manage_project(
     "create",
     title="[Feature Name from INITIAL.md]",
     description="[Feature description]",
     github_repo="[repo-url]"
   )

   # Store project_id for task creation
   ```

### 3. **Research & Context Gathering**

   **Use Archon Knowledge Base:**
   ```bash
   # Get available sources
   rag_get_available_sources()

   # Search for relevant patterns (keep queries short!)
   rag_search_knowledge_base(query="[tech keywords]", match_count=5)
   rag_search_code_examples(query="[pattern]", match_count=3)

   # Read full pages if needed
   rag_read_full_page(page_id="...")
   ```

   **Analyze Referenced Examples:**
   - Read files referenced in EXAMPLES section
   - Identify patterns and conventions
   - Extract reusable code structures
   - Note error handling approaches
   - Document validation patterns

   **Research Documentation:**
   - Fetch documentation URLs from DOCUMENTATION section
   - Extract relevant API references
   - Note best practices and gotchas
   - Identify security considerations

### 4. **Generate Implementation Blueprint**

   Create comprehensive plan with:

   **A. Data Layer Plan**
   - Database schema changes needed
   - TypeScript interfaces required
   - Zod validation schemas
   - SQLite service methods to create
   - Sync service integration (if needed)

   **B. Business Logic Plan**
   - Core functionality breakdown
   - Helper functions needed
   - State management approach
   - Data flow patterns
   - Error handling strategy

   **C. UI Layer Plan**
   - Components to create/modify
   - Form structure and validation
   - UI component selection (from shadcn/ui)
   - Styling approach
   - Responsive design considerations

   **D. Integration Plan**
   - Route additions (if new page)
   - Navigation updates
   - Component imports
   - Hook integrations
   - Context provider updates (if needed)

   **E. Testing Plan**
   - Manual test scenarios
   - Edge cases to validate
   - Error conditions to test
   - Browser compatibility checks

### 5. **Task Breakdown & Archon Integration**

   **Create tasks in Archon (NOT TodoWrite):**

   ```bash
   # Task 1: Database Schema
   manage_task(
     "create",
     project_id="[project-id]",
     title="Update SQLite schema",
     description="Add new tables/columns to sqlite-schema.sql",
     status="todo",
     assignee="User",
     task_order=100,
     feature="database"
   )

   # Task 2: TypeScript Interfaces
   manage_task(
     "create",
     project_id="[project-id]",
     title="Define TypeScript interfaces",
     description="Create interfaces in sqlite-service.ts",
     status="todo",
     assignee="User",
     task_order=90,
     feature="types"
   )

   # Task 3: Zod Schemas
   manage_task(
     "create",
     project_id="[project-id]",
     title="Create Zod validation schemas",
     description="Define validation rules for forms",
     status="todo",
     assignee="User",
     task_order=85,
     feature="validation"
   )

   # Task 4: Database Methods
   manage_task(
     "create",
     project_id="[project-id]",
     title="Implement database service methods",
     description="Add CRUD operations to sqlite-service.ts",
     status="todo",
     assignee="User",
     task_order=80,
     feature="database"
   )

   # Task 5: UI Components
   manage_task(
     "create",
     project_id="[project-id]",
     title="Create UI components",
     description="Build components using shadcn/ui",
     status="todo",
     assignee="User",
     task_order=70,
     feature="ui"
   )

   # Task 6: Form Component
   manage_task(
     "create",
     project_id="[project-id]",
     title="Implement form with validation",
     description="Create form using React Hook Form + Zod",
     status="todo",
     assignee="User",
     task_order=60,
     feature="forms"
   )

   # Task 7: Integration
   manage_task(
     "create",
     project_id="[project-id]",
     title="Add route and navigation",
     description="Update App.tsx with new route",
     status="todo",
     assignee="User",
     task_order=50,
     feature="routing"
   )

   # Task 8: Error Handling
   manage_task(
     "create",
     project_id="[project-id]",
     title="Implement error handling",
     description="Add try/catch and user feedback",
     status="todo",
     assignee="User",
     task_order=40,
     feature="error-handling"
   )

   # Task 9: Testing
   manage_task(
     "create",
     project_id="[project-id]",
     title="Manual testing and validation",
     description="Test all scenarios and edge cases",
     status="todo",
     assignee="User",
     task_order=30,
     feature="testing"
   )

   # Task 10: Validation Loop
   manage_task(
     "create",
     project_id="[project-id]",
     title="Run validation commands",
     description="Execute lint, build, and verify success criteria",
     status="todo",
     assignee="User",
     task_order=20,
     feature="validation"
   )
   ```

### 6. **Define Success Criteria**

   Create measurable "done" checklist:
   - [ ] Feature works as specified
   - [ ] No TypeScript errors (`npm run build:dev`)
   - [ ] No ESLint warnings (`npm run lint`)
   - [ ] UI renders correctly on desktop and mobile
   - [ ] Form validation works for all fields
   - [ ] Data persists to SQLite correctly
   - [ ] Staging dashboard shows records
   - [ ] Sync to Supabase works (if applicable)
   - [ ] Error handling covers edge cases
   - [ ] User feedback (toasts) working
   - [ ] No regressions in existing features
   - [ ] Browser console has no errors
   - [ ] Manual test scenarios pass

### 7. **Generate Plan Document**

   Create `plans/[feature-name]-plan.md` with:

   ```markdown
   # Implementation Plan: [Feature Name]

   **Created:** [date]
   **Archon Project ID:** [project-id]
   **Based on:** [path-to-INITIAL.md]

   ## Goal
   [Clear feature description from INITIAL.md]

   ## Context & Research
   ### Codebase Patterns
   [Relevant patterns from existing code]

   ### External Documentation
   [Key docs and API references]

   ### Similar Implementations
   [Files and patterns to reference]

   ## Data Models
   ### TypeScript Interfaces
   ```typescript
   [Interfaces needed]
   ```

   ### Zod Schemas
   ```typescript
   [Validation schemas]
   ```

   ### SQLite Schema
   ```sql
   [Table definitions]
   ```

   ## Implementation Blueprint
   ### Phase 1: Data Layer
   [Detailed steps with file paths]

   ### Phase 2: Business Logic
   [Detailed steps with pseudocode]

   ### Phase 3: UI Layer
   [Component structure and patterns]

   ### Phase 4: Integration
   [Routes, imports, connections]

   ### Phase 5: Testing & Validation
   [Test scenarios and validation commands]

   ## Archon Tasks
   [List of tasks created with IDs]

   ## Success Criteria
   [Checklist from step 6]

   ## Gotchas & Considerations
   [Technology-specific warnings and tips]

   ## Validation Commands
   ```bash
   npm run lint
   npm run build:dev
   npm run dev
   ```

   ## Next Steps
   1. Review this plan
   2. Run `/execute-plan plans/[feature-name]-plan.md`
   3. Follow task-driven development cycle
   ```

### 8. **Save Plan to Archon (Optional)**

   Store plan in Archon knowledge base:
   ```bash
   # If plan contains valuable patterns/decisions
   # (This is optional but recommended for complex features)
   ```

## Task Granularity Guidelines

**For Feature-Specific Projects:**
Create detailed implementation tasks:
- Setup and configuration
- Database schema updates
- Interface definitions
- Service method implementations
- UI component creation
- Form implementation
- Integration and routing
- Error handling
- Testing and validation

**For Codebase-Wide Projects:**
Create feature-level tasks:
- "Implement user authentication"
- "Add payment processing"
- "Create admin dashboard"

**Default:** When in doubt, create more granular tasks (30 min - 4 hours each).

## Archon Integration Rules

**CRITICAL - Follow these strictly:**
1. ✅ **ALWAYS** create/check Archon project FIRST
2. ✅ **ALWAYS** use `manage_task()` to create tasks (NOT TodoWrite)
3. ✅ **ALWAYS** search knowledge base before external research
4. ✅ **ALWAYS** set task_order (higher = more priority, 0-100)
5. ✅ **ALWAYS** assign tasks to "User" initially
6. ✅ **NEVER** use TodoWrite in this workflow

## Output Format

After running `/create-plan`, you should have:

1. **Archon Project** with all tasks created
2. **Plan Document** saved to `plans/[feature-name]-plan.md`
3. **Summary** displayed to user:

```markdown
# Plan Created: [Feature Name]

## Archon Project
- **ID:** [project-id]
- **Tasks Created:** [count]
- **View tasks:** `find_tasks(project_id="[project-id]")`

## Plan Document
- **Location:** plans/[feature-name]-plan.md
- **Sections:** Data Models, Blueprint, Success Criteria, Gotchas

## Research Completed
- ✅ Knowledge base searched
- ✅ Similar code analyzed
- ✅ Documentation reviewed
- ✅ Patterns identified

## Next Steps
1. Review the plan: `cat plans/[feature-name]-plan.md`
2. Execute: `/execute-plan plans/[feature-name]-plan.md`

## Quick Start
Run: `/execute-plan plans/[feature-name]-plan.md`
```

## Safe Catch Flow Specific Patterns

### Database Tasks:
- Update `public/sqlite-schema.sql`
- Add methods to `src/lib/sqlite-service.ts`
- Include staging fields: `synced_to_supabase`, `sync_confirmed`
- Add `created_by` field for audit trails

### Form Tasks:
- Follow `src/components/HaccpEventForm.tsx` pattern
- Use React Hook Form + Zod
- Include loading states
- Add success/error toasts
- Reset form after submission

### UI Tasks:
- Use components from `src/components/ui/`
- Follow Tailwind CSS patterns
- Ensure responsive design
- Maintain consistent spacing

### Integration Tasks:
- Add route to `src/App.tsx`
- Update navigation (if applicable)
- Add to staging dashboard (if applicable)
- Update sync service (if needed)

## Tips for Effective Planning

✅ **DO:**
- Research thoroughly before task creation
- Break tasks into 30 min - 4 hour chunks
- Order tasks by dependencies
- Reference existing code patterns
- Define clear success criteria
- Document gotchas and edge cases
- Use Archon knowledge base first

❌ **DON'T:**
- Create vague tasks
- Skip research phase
- Forget integration tasks
- Omit validation tasks
- Use TodoWrite instead of Archon
- Skip success criteria definition
- Create tasks without context

## Example Usage

```bash
# Create plan from INITIAL.md
/create-plan INITIAL.md

# Create plan from feature-specific PRD
/create-plan features/supplier-management/INITIAL.md

# Create plan after primer phase
/primer existing supplier-management
# ... review primer output ...
/create-plan INITIAL.md
```

## Success Criteria

After running `/create-plan`, you should have:
- ✅ Archon project created with all tasks
- ✅ Comprehensive plan document saved
- ✅ Research completed and documented
- ✅ Clear implementation blueprint
- ✅ Data models defined
- ✅ Success criteria established
- ✅ Gotchas documented
- ✅ Ready to execute with `/execute-plan`

---

**Remember:** The plan is your blueprint. Take time to review it, ask questions, and refine before executing. A good plan makes execution smooth and predictable.
