# Primer - Vibe Planning & Exploration

Kickstart the exploration phase for a new feature or project understanding.

## Purpose

The `/primer` command initiates Phase 1 of the structured development workflow - **Vibe Planning**. This is an unstructured exploration phase where you analyze the codebase, research technologies, explore architecture options, and gather ideas before formal planning.

## Usage

### For New Projects:
```
/primer new-project [project-name]
```

### For Existing Projects:
```
/primer existing [feature-area]
```

### For Current Codebase Analysis:
```
/primer analyze
```

## Process

### 1. **Check Archon Task Management (CRITICAL)**
   **BEFORE doing anything else:**
   ```bash
   # Check if there's already a project for this
   find_projects(query="[feature-area]")

   # Check for existing tasks
   find_tasks(query="[feature-area]")
   ```

### 2. **Codebase Analysis Mode (Existing Projects)**

   Launch Codebase Analyst sub-agent to:
   - Understand current architecture
   - Identify relevant patterns and conventions
   - Find similar implementations
   - Map integration points
   - Document technical constraints

   **Key Areas to Explore:**
   - `src/lib/sqlite-service.ts` - Database patterns
   - `src/components/` - UI patterns and component structure
   - `src/lib/sync-service.ts` - Sync patterns
   - `public/sqlite-schema.sql` - Data models
   - `src/App.tsx` - Routing and app structure
   - `src/hooks/` - Custom hook patterns

   **Analysis Questions:**
   - What similar features exist?
   - What patterns are used consistently?
   - What are the data flow patterns?
   - What are the error handling conventions?
   - What validation patterns are used?
   - How are forms structured?
   - How is state managed?

### 3. **Research Phase**

   **Use Archon Knowledge Base:**
   ```bash
   # Get available documentation sources
   rag_get_available_sources()

   # Search for relevant patterns
   rag_search_knowledge_base(query="[tech-stack]", match_count=5)
   rag_search_code_examples(query="[pattern]", match_count=3)
   ```

   **Web Research (if needed):**
   - Official documentation for technologies
   - Best practices and patterns
   - Common gotchas and pitfalls
   - Security considerations
   - Performance implications

   **For Safe Catch Flow, research:**
   - React + TypeScript patterns
   - React Hook Form + Zod validation
   - SQLite in browser (sql.js)
   - shadcn/ui components
   - Offline-first architecture
   - HACCP compliance requirements (if relevant)

### 4. **Technology Stack Exploration (New Projects)**

   Research and evaluate:
   - Framework options (React, Vue, Svelte, etc.)
   - State management approaches
   - Database solutions
   - UI libraries
   - Testing frameworks
   - Build tools
   - Deployment strategies

   Compare trade-offs:
   - Development speed vs. performance
   - Bundle size vs. features
   - Type safety vs. flexibility
   - Hosting options vs. cost

### 5. **Architecture Brainstorming**

   Explore possibilities:
   - Data flow patterns
   - Component hierarchy
   - State management strategy
   - API structure (if applicable)
   - Database schema design
   - Integration points
   - Sync strategies (for offline-first)

   **Document considerations:**
   - Scalability requirements
   - Performance constraints
   - Security requirements
   - Offline capability needs
   - User experience priorities

### 6. **Create Archon Project (if appropriate)**

   After exploration, create project in Archon:
   ```bash
   manage_project(
     "create",
     title="[Feature Name]",
     description="[Brief description from exploration]",
     github_repo="https://github.com/[org]/[repo]"
   )
   ```

### 7. **Generate Exploration Summary**

   Provide comprehensive summary including:

   **Findings:**
   - Relevant existing patterns in codebase
   - Similar implementations discovered
   - Technologies and libraries identified
   - Integration points mapped

   **Recommendations:**
   - Suggested approach based on research
   - Patterns to follow from existing code
   - Libraries/tools to use
   - Potential gotchas to avoid

   **Next Steps:**
   - Create INITIAL.md with requirements
   - Use `/create-plan` to generate structured plan
   - Key decisions that need user input

   **Open Questions:**
   - Any unclear requirements
   - Technology choices to finalize
   - Architecture decisions needed
   - Dependencies to clarify

## Safe Catch Flow Specific Primer Tasks

### Database Layer Exploration:
- Review `sqlite-service.ts` for CRUD patterns
- Check `sqlite-schema.sql` for table structures
- Understand staging/sync workflow
- Identify sync service patterns

### Form Layer Exploration:
- Analyze `HaccpEventForm.tsx` structure
- Review React Hook Form usage
- Check Zod validation patterns
- Understand error handling approach

### UI Layer Exploration:
- Review shadcn/ui component usage
- Check Tailwind CSS patterns
- Analyze responsive design approach
- Review toast notification patterns

### State Management Exploration:
- Review TanStack Query usage
- Check React context patterns
- Analyze data fetching strategies
- Review caching approaches

## Archon Integration Rules

**MANDATORY Archon workflow:**
1. ✅ **ALWAYS** check for existing projects first
2. ✅ **ALWAYS** check for existing tasks related to feature
3. ✅ **ALWAYS** search knowledge base before web research
4. ✅ **ALWAYS** create project in Archon after exploration
5. ✅ **NEVER** use TodoWrite - use Archon task management

## Output Format

Provide structured exploration summary:

```markdown
# Primer Summary: [Feature/Project Name]

## Codebase Analysis
[Key patterns, similar implementations, integration points]

## Research Findings
[Technology insights, best practices, gotchas]

## Architecture Exploration
[Potential approaches, data flow, component structure]

## Recommendations
[Suggested approach with rationale]

## Open Questions
[Decisions needed, unclear requirements]

## Next Steps
1. Create INITIAL.md with requirements
2. Run `/create-plan INITIAL.md`
3. [Any other preparation needed]

## Archon Project
Project ID: [project-id]
Tasks: [number] pending
```

## Tips for Effective Priming

✅ **DO:**
- Explore broadly before deciding
- Look for multiple solutions
- Document trade-offs
- Ask clarifying questions
- Reference existing code extensively
- Use Archon knowledge base first

❌ **DON'T:**
- Jump to implementation
- Skip codebase analysis
- Ignore existing patterns
- Make assumptions without research
- Skip Archon project creation
- Use TodoWrite instead of Archon

## Example Usage

```bash
# Analyze codebase for adding a new feature
/primer analyze

# Explore adding supplier management
/primer existing supplier-management

# Start planning a new project
/primer new-project inventory-tracking
```

## Success Criteria

After running `/primer`, you should have:
- ✅ Clear understanding of existing codebase patterns
- ✅ Research on relevant technologies and approaches
- ✅ Architectural exploration and options
- ✅ Recommendations with rationale
- ✅ Archon project created (if appropriate)
- ✅ Open questions documented
- ✅ Clear path to creating INITIAL.md

---

**Remember:** The primer phase is exploratory and unstructured. The goal is to gather context and ideas, not to make final decisions. Use this phase to understand the landscape before formal planning in INITIAL.md.
