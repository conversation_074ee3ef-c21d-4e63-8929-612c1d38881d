# Execute Plan - Systematic Task-by-Task Implementation

Execute a comprehensive implementation plan using task-driven development with Archon integration.

## Purpose

The `/execute-plan` command is Phase 2 of the structured workflow. It systematically implements the plan created by `/create-plan`, following a strict task-driven development cycle with Archon MCP server.

## Usage

```
/execute-plan [path-to-plan.md]
```

Example:
```
/execute-plan plans/supplier-management-plan.md
/execute-plan PRPs/temperature-dashboard.md
```

## Prerequisites

Before running `/execute-plan`:
1. ✅ Plan document exists (from `/create-plan`)
2. ✅ Archon project created with tasks
3. ✅ Requirements clearly defined
4. ✅ Success criteria established

## Archon-First Execution Cycle

**CRITICAL:** This workflow uses Archon MCP server for ALL task management. DO NOT use TodoWrite.

### The Task-Driven Development Cycle

```
1. GET TASK     → find_tasks()
2. START WORK   → manage_task("update", status="doing")
3. RESEARCH     → rag_search_knowledge_base()
4. IMPLEMENT    → Write/Edit code
5. REVIE<PERSON>       → manage_task("update", status="review")
6. <PERSON><PERSON><PERSON><PERSON><PERSON>     → Run tests/lint
7. COMPLETE     → manage_task("update", status="done")
8. NEXT TASK    → Go to step 1
```

## Detailed Process

### 1. **Load Plan & Project Context**

   ```bash
   # Read the plan document
   # Extract Archon project_id from plan

   # Get project details
   find_projects(project_id="[project-id]")

   # Get all tasks for this project
   find_tasks(filter_by="project", filter_value="[project-id]")
   ```

### 2. **Get Next Task**

   **MANDATORY Archon workflow:**
   ```bash
   # Get next todo task (highest priority)
   find_tasks(
     filter_by="status",
     filter_value="todo",
     project_id="[project-id]"
   )
   # Sort by task_order DESC to get highest priority

   # Or get specific task by ID
   find_tasks(task_id="[task-id]")
   ```

### 3. **Start Task**

   **Mark task as in progress:**
   ```bash
   manage_task(
     "update",
     task_id="[task-id]",
     status="doing",
     assignee="Coding Agent"  # Or keep as "User"
   )
   ```

   **IMPORTANT:** Only ONE task should be "doing" at a time!

### 4. **Research Phase (MANDATORY)**

   **Before writing ANY code, research:**

   ```bash
   # Search knowledge base (2-5 keywords!)
   rag_search_knowledge_base(
     query="[relevant-keywords]",
     source_id="[source-id]",  # If targeting specific docs
     match_count=5
   )

   # Search for code examples
   rag_search_code_examples(
     query="[pattern-keywords]",
     match_count=3
   )

   # Read full documentation pages if needed
   rag_read_full_page(page_id="[page-id]")
   ```

   **Research checklist:**
   - ✅ Searched knowledge base for relevant patterns
   - ✅ Found similar code examples in codebase
   - ✅ Reviewed referenced files from plan
   - ✅ Identified potential gotchas
   - ✅ Understood error handling approach

### 5. **Implementation**

   Follow plan blueprint for the current task:

   **For Database Tasks:**
   ```typescript
   // Update public/sqlite-schema.sql
   CREATE TABLE IF NOT EXISTS [table_name]_staged (
     id INTEGER PRIMARY KEY AUTOINCREMENT,
     [fields],
     created_by TEXT NOT NULL,
     created_at TEXT DEFAULT CURRENT_TIMESTAMP,
     synced_to_supabase INTEGER DEFAULT 0,
     sync_confirmed INTEGER DEFAULT 0
   );

   // Add methods to src/lib/sqlite-service.ts
   async insert[Entity](data: [Entity]Insert): Promise<void> {
     if (!this.db) throw new Error('Database not initialized');
     try {
       const stmt = this.db.prepare(`INSERT INTO ...`);
       stmt.run([...params]);
       stmt.free();
       console.log('[Entity] inserted successfully');
     } catch (error) {
       console.error('Error inserting [entity]:', error);
       throw error;
     }
   }
   ```

   **For Form Tasks:**
   ```typescript
   // Follow src/components/HaccpEventForm.tsx pattern
   import { useForm } from 'react-hook-form';
   import { zodResolver } from '@hookform/resolvers/zod';
   import { z } from 'zod';
   import { toast } from 'sonner';

   const formSchema = z.object({
     // Validation rules from plan
   });

   export function [Feature]Form() {
     const form = useForm({
       resolver: zodResolver(formSchema),
       defaultValues: { /* ... */ },
     });

     async function onSubmit(values: z.infer<typeof formSchema>) {
       try {
         await sqliteService.insert[Entity]({ ...values });
         toast.success('Success message');
         form.reset();
       } catch (error) {
         console.error('Error:', error);
         toast.error('Error message');
       }
     }

     return <Form {...form}>{ /* form fields */ }</Form>;
   }
   ```

   **For UI Component Tasks:**
   ```typescript
   // Use shadcn/ui from src/components/ui/
   import { Button } from "@/components/ui/button";
   import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

   // Follow existing styling patterns
   // Ensure responsive design
   // Add proper accessibility
   ```

   **For Integration Tasks:**
   ```typescript
   // Update src/App.tsx with new route
   <Route path="/[feature]" element={<[Feature]Page />} />

   // Add navigation (if applicable)
   // Import and use components
   ```

### 6. **Mark for Review**

   After implementation, mark task for review:
   ```bash
   manage_task(
     "update",
     task_id="[task-id]",
     status="review"
   )
   ```

### 7. **Validation Loop**

   **Level 1: Syntax & Types**
   ```bash
   # Run lint
   npm run lint

   # If errors, fix them before proceeding
   # If no errors, continue

   # Run build
   npm run build:dev

   # If TypeScript errors, fix them
   # If compilation succeeds, continue
   ```

   **Level 2: Manual Testing**
   ```bash
   # Start dev server
   npm run dev

   # Test in browser:
   # - Navigate to feature
   # - Test happy path
   # - Test edge cases
   # - Check browser console
   # - Verify data persistence
   # - Test error handling
   # - Verify user feedback (toasts)
   ```

   **Level 3: Data Integrity**
   - Verify SQLite operations
   - Check staging dashboard
   - Test sync to Supabase (if applicable)
   - Validate form submissions
   - Check error scenarios

### 8. **Fix Failures**

   If validation fails:
   ```bash
   # Keep task in "review" status
   # Analyze error messages
   # Check plan for gotchas
   # Review similar implementations
   # Fix the issues
   # Re-run validation from step 7
   ```

   **DO NOT mark task complete if:**
   - ❌ Lint errors exist
   - ❌ TypeScript compilation fails
   - ❌ Runtime errors in console
   - ❌ Feature doesn't work as specified
   - ❌ Tests fail
   - ❌ Data doesn't persist correctly

### 9. **Complete Task**

   **Only when ALL validation passes:**
   ```bash
   manage_task(
     "update",
     task_id="[task-id]",
     status="done"
   )
   ```

### 10. **Get Next Task**

   ```bash
   # Get next todo task
   find_tasks(
     filter_by="status",
     filter_value="todo",
     project_id="[project-id]"
   )

   # Return to step 3 with new task
   ```

### 11. **Verify Success Criteria**

   When all tasks complete, verify plan success criteria:
   - [ ] Feature works as specified
   - [ ] No TypeScript errors
   - [ ] No ESLint warnings
   - [ ] UI renders correctly
   - [ ] Form validation works
   - [ ] Data persists to SQLite
   - [ ] Staging dashboard shows records
   - [ ] Sync to Supabase works
   - [ ] Error handling implemented
   - [ ] User feedback working
   - [ ] No regressions
   - [ ] Browser console clean
   - [ ] Manual tests pass

### 12. **Mark Project Complete**

   ```bash
   # Update project status or add completion notes
   manage_project(
     "update",
     project_id="[project-id]",
     description="[original-description]\n\nCompleted: [date]"
   )
   ```

## Safe Catch Flow Implementation Patterns

### Database Implementation Pattern:
```typescript
// 1. Update public/sqlite-schema.sql
// 2. Add TypeScript interface in sqlite-service.ts
// 3. Add insert method
// 4. Add get/list methods
// 5. Add update method (if needed)
// 6. Add delete method (if needed)
// 7. Test in browser console
```

### Form Implementation Pattern:
```typescript
// 1. Define Zod schema with validation rules
// 2. Create form component with useForm hook
// 3. Add form fields using Form components
// 4. Implement onSubmit with error handling
// 5. Add success/error toasts
// 6. Test validation (empty, invalid, valid)
// 7. Verify data persistence
```

### Page Implementation Pattern:
```typescript
// 1. Create page component in src/pages/
// 2. Add necessary imports (hooks, components, services)
// 3. Implement data fetching (if needed)
// 4. Build UI using shadcn/ui components
// 5. Add loading and error states
// 6. Add route to App.tsx
// 7. Test navigation and functionality
```

## Archon Integration Rules (CRITICAL)

**MANDATORY workflow:**
1. ✅ **ALWAYS** use `find_tasks()` to get tasks
2. ✅ **ALWAYS** mark task "doing" before starting
3. ✅ **ALWAYS** research with knowledge base before coding
4. ✅ **ALWAYS** mark task "review" after implementation
5. ✅ **ALWAYS** run validation before marking "done"
6. ✅ **ALWAYS** mark task "done" only after ALL validation passes
7. ✅ **NEVER** use TodoWrite in this workflow
8. ✅ **NEVER** skip the research phase
9. ✅ **NEVER** have multiple tasks in "doing" status

**Task status flow:**
```
todo → doing → review → done
         ↑         ↓
         └─ fix ───┘
```

## Research Best Practices

**Keep queries SHORT (2-5 keywords):**

✅ **GOOD:**
- `rag_search_knowledge_base(query="React Hook Form validation")`
- `rag_search_code_examples(query="SQLite transaction")`
- `rag_search_knowledge_base(query="TypeScript Zod schema")`

❌ **BAD:**
- `rag_search_knowledge_base(query="how to implement form validation with React Hook Form and Zod in TypeScript")`
- `rag_search_code_examples(query="SQLite database transactions with error handling and rollback")`

## Common Validation Errors & Fixes

### TypeScript Errors:
- Missing imports: Add import statements
- Type mismatches: Check interface definitions
- Undefined properties: Verify object structure
- Implicit any: Add explicit types

### ESLint Warnings:
- Unused imports: Remove or use them
- Missing dependencies: Add to useEffect deps array
- Unused variables: Remove or use them
- Console statements: Remove or add eslint-disable

### Runtime Errors:
- Database not initialized: Check App.tsx initialization
- Method doesn't exist: Verify sqlite-service.ts exports
- Null/undefined: Add null checks and optional chaining
- Form validation: Check Zod schema matches fields

### Build Errors:
- Module not found: Check file paths and imports
- Syntax errors: Review TypeScript syntax
- Type errors: Fix type definitions
- Circular dependencies: Refactor imports

## Output Format

Throughout execution, provide updates:

```markdown
## Current Task: [task-title]
**Status:** doing
**Task ID:** [task-id]

### Research Findings:
[Key insights from knowledge base]

### Implementation:
[What was implemented]

### Validation:
✅ Lint: Passed
✅ Build: Passed
✅ Manual Test: Passed
✅ Data Integrity: Passed

### Marking Complete
Task marked as done ✓

---

## Next Task: [next-task-title]
**Status:** todo → doing
**Task ID:** [next-task-id]

[Continue cycle...]
```

## Final Summary

After all tasks complete:

```markdown
# Implementation Complete: [Feature Name]

## Archon Project Summary
- **Project ID:** [project-id]
- **Total Tasks:** [count]
- **Status:** All tasks completed ✓

## Validation Results
✅ All success criteria met
✅ No TypeScript errors
✅ No ESLint warnings
✅ Manual tests passed
✅ No regressions detected

## Files Modified/Created
- [list of files]

## Testing Performed
- [test scenarios executed]

## Known Issues/Limitations
- [any issues discovered]

## Next Steps
1. Final manual QA testing
2. Create commit (if ready)
3. Deploy to staging (if applicable)

## Verification Command
```bash
npm run dev
# Navigate to [feature URL]
# Test [key scenarios]
```
```

## Tips for Successful Execution

✅ **DO:**
- Follow the task cycle strictly
- Research BEFORE coding
- Test after EACH task
- Fix errors immediately
- Use Archon for ALL task management
- Keep task status updated
- Verify success criteria at end

❌ **DON'T:**
- Skip research phase
- Work on multiple tasks simultaneously
- Mark tasks complete with failing tests
- Use TodoWrite instead of Archon
- Skip validation steps
- Ignore error messages
- Batch task completions

## Example Execution Flow

```bash
# 1. Load plan
/execute-plan plans/supplier-management-plan.md

# 2. Get first task
find_tasks(project_id="proj-123", filter_by="status", filter_value="todo")

# 3. Start task
manage_task("update", task_id="task-1", status="doing")

# 4. Research
rag_search_knowledge_base(query="SQLite schema", match_count=5)

# 5. Implement
# [Write code following patterns]

# 6. Mark review
manage_task("update", task_id="task-1", status="review")

# 7. Validate
npm run lint && npm run build:dev && npm run dev

# 8. Complete
manage_task("update", task_id="task-1", status="done")

# 9. Next task
find_tasks(project_id="proj-123", filter_by="status", filter_value="todo")

# [Repeat 3-9 until all tasks complete]
```

## Success Criteria

After `/execute-plan`, you should have:
- ✅ All Archon tasks marked "done"
- ✅ Feature fully implemented per plan
- ✅ All validation levels passing
- ✅ Success criteria checklist complete
- ✅ No regressions introduced
- ✅ Documentation updated (if needed)
- ✅ Ready for final QA/deployment

---

**Remember:** Task-driven development with Archon ensures nothing is forgotten and everything is validated. Trust the process and follow the cycle religiously for best results.
