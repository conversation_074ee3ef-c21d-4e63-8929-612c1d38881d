{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__archon__find_projects", "mcp__archon__manage_project", "mcp__archon__find_tasks", "mcp__archon__manage_task", "mcp__archon__find_documents", "mcp__archon__manage_document", "mcp__archon__find_versions", "mcp__archon__manage_version", "mcp__archon__get_project_features", "mcp__archon__rag_get_available_sources", "mcp__archon__rag_search_knowledge_base", "mcp__archon__rag_search_code_examples", "mcp__archon__rag_list_pages_for_source", "mcp__archon__rag_read_full_page", "mcp__archon__health_check", "mcp__archon__session_info", "Bash(npm run lint:*)", "Bash(npm run build:dev:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(cat:*)", "Bash(supabase status:*)", "Bash(npm install:*)", "Bash(npm run test:run:*)", "Bash(wc:*)", "Bash(npx eslint:*)"], "deny": [], "ask": []}}