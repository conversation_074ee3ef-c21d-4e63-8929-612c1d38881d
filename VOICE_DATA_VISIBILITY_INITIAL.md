# Voice Agent Data Visibility Enhancement - Initial Requirements

## FEATURE

Add three capabilities to help users understand and verify what data the voice agent is creating in the SQLite staging tables:

1. **Data Viewing Tool** - Component to view staged inventory records created by voice
2. **Logging Enhancement** - Add console logs when voice agent creates records
3. **Recent Records List** - Function to fetch and display recent voice-created entries

## EXAMPLES

### Similar Patterns in Codebase

**1. Staging Dashboard Pattern:**
- File: `src/components/StagingDashboard.tsx`
- Shows staged data with confirmation status
- Uses `sqliteService` to fetch records
- Displays in tabular format with filtering

**2. Database Query Patterns:**
- File: `src/lib/sqlite-service.ts`
- Methods like `getAllInventory()`, `getInventoryByBatch()`
- Filtering by `created_by` field
- Async/await patterns with error handling

**3. Voice Tool Executor Pattern:**
- File: `src/lib/voice-tool-executor.ts`
- Already creates records with `created_by: 'voice-agent'`
- Returns success messages
- Has error handling and logging

**4. Component Display Patterns:**
- File: `src/components/VoiceToolFeedback.tsx`
- Shows real-time tool execution feedback
- Toast notifications for success/error
- Visual indicators with icons

## DOCUMENTATION

### React Patterns
- React Hooks documentation: https://react.dev/reference/react
- `useState`, `useEffect` for data fetching
- Component lifecycle and cleanup

### Database Access
- SQLite service methods (see `src/lib/sqlite-service.ts`)
- TypeScript interfaces for data types
- Async data fetching patterns

### UI Components
- shadcn/ui Table: https://ui.shadcn.com/docs/components/table
- shadcn/ui Card: https://ui.shadcn.com/docs/components/card
- shadcn/ui Badge: https://ui.shadcn.com/docs/components/badge

### Logging Best Practices
- Console methods: `console.log`, `console.info`, `console.table`
- Structured logging with context objects
- Performance considerations (avoid excessive logging)

## REQUIREMENTS

### 1. View Staged Data Component

**Functional Requirements:**
- Display inventory records where `created_by = 'voice-agent'`
- Show key fields: product_id, batch_number, quantity, unit, location, created_at
- Sort by most recent first (created_at DESC)
- Limit to last 20 records by default
- Include sync status indicators (synced_to_supabase, sync_confirmed)
- Refresh button to reload data
- Empty state when no voice records exist

**UI Requirements:**
- Use shadcn/ui Table component
- Responsive layout (works on mobile)
- Color-coded sync status badges
- Timestamp formatting (relative time: "2 minutes ago")
- Optional: Filter by date range
- Optional: Search by product_id or batch_number

**Location:**
- Create new component: `src/components/VoiceInventoryRecords.tsx`
- OR add tab to existing StagingDashboard

### 2. Enhanced Logging in Voice Tool Executor

**Functional Requirements:**
- Log to console when inventory record is created
- Include all key fields in log output
- Use `console.table()` for structured display
- Add success indicator (✅) for easy scanning
- Include timestamp in ISO format
- Show record ID returned from database

**Log Format:**
```javascript
console.log('✅ Voice Agent: Inventory record created');
console.table({
  record_id: inventoryId,
  product_id: 'salmon',
  batch_number: 'BATCH-001',
  quantity: 20,
  unit: 'kg',
  location: 'Freezer A',
  created_at: new Date().toISOString(),
  created_by: 'voice-agent'
});
```

**Location:**
- Update: `src/lib/voice-tool-executor.ts`
- Method: `addInventoryEvent()` - after successful creation
- Also add logging for CCP monitoring records

### 3. Recent Voice Records Function

**Functional Requirements:**
- Add method to sqlite-service: `getVoiceInventoryRecords(limit?: number)`
- Filter by `created_by = 'voice-agent'`
- Default limit: 20 records
- Sort by `created_at DESC`
- Return full record data with TypeScript types
- Handle errors gracefully (return empty array)

**Method Signature:**
```typescript
async getVoiceInventoryRecords(limit: number = 20): Promise<StagedInventory[]>
```

**Query Logic:**
```sql
SELECT * FROM staged_inventory
WHERE created_by = 'voice-agent'
ORDER BY created_at DESC
LIMIT ?
```

**Location:**
- Add to: `src/lib/sqlite-service.ts`
- Export method for use in components
- Add similar method for CCP records if needed

## OTHER CONSIDERATIONS

### Data Privacy
- Only show records created by current session (optional enhancement)
- Consider user attribution if multiple users use voice agent
- Don't expose sensitive data in console logs (production)

### Performance
- Limit query results to prevent memory issues
- Use pagination if showing >100 records
- Consider caching recent records (React Query)

### User Experience
- Toast notification when record is created
- Visual feedback in VoiceToolFeedback component
- Link to view full staged data from voice feedback
- Clear indication of sync status (staged vs synced)

### Error Handling
- Handle empty results gracefully
- Show error message if database query fails
- Fallback UI if component fails to load
- Console errors for debugging

### Testing
- Unit tests for `getVoiceInventoryRecords()` method
- Component tests for VoiceInventoryRecords display
- Integration test: create record via voice → verify in list
- Test edge cases: empty results, database errors

### Documentation
- Update VOICE_INTEGRATION.md with new capabilities
- Add JSDoc comments to new methods
- Include usage examples in component
- Update README if exposing to users

### Future Enhancements
- Export voice records to CSV
- Delete individual voice records
- Bulk sync all voice records
- Voice record analytics (count, trends)
- Filter by date range or product
- Real-time updates (WebSocket/polling)

## SUCCESS CRITERIA

1. ✅ User can view list of inventory records created by voice agent
2. ✅ Console logs show detailed info when voice creates records
3. ✅ New sqlite-service method returns voice-created records
4. ✅ Component displays data in readable table format
5. ✅ Sync status clearly indicated (staged vs synced)
6. ✅ All tests pass (unit + integration)
7. ✅ No TypeScript errors
8. ✅ Linting passes
9. ✅ Documentation updated

## IMPLEMENTATION PRIORITY

**Phase 1 (Immediate):**
1. Add `getVoiceInventoryRecords()` method to sqlite-service
2. Enhance logging in voice-tool-executor

**Phase 2 (Short-term):**
3. Create VoiceInventoryRecords component
4. Add tests for new functionality

**Phase 3 (Optional):**
5. Additional features (export, delete, analytics)
