# PRP Framework Quick Start Guide

Welcome to the **PRP (Problem, Requirements, Plan) Framework** for Safe Catch Flow! This guide will get you started in 5 minutes.

## 🎯 What is PRP?

PRP is a context engineering methodology that helps AI coding assistants (like Claude Code) implement features end-to-end by providing comprehensive context upfront. Instead of back-and-forth iterations, you define everything once, and the AI implements it completely.

## 🚀 Three-Step Workflow

```
1. Define → Create INITIAL.md with requirements
2. Generate → Run /generate-prp to create implementation plan
3. Execute → Run /execute-prp to build the feature
```

## 📋 Quick Start (5 Minutes)

### Step 1: Create Your Feature Request (2 minutes)

Copy `INITIAL.md` to a new file or edit it directly:

```markdown
## FEATURE
Add a temperature monitoring dashboard that displays recent readings

## EXAMPLES
- Form pattern: src/components/HaccpEventForm.tsx
- Database methods: src/lib/sqlite-service.ts (insertTemperatureReading)
- UI components: src/components/ui/card.tsx, button.tsx

## DOCUMENTATION
- React Query: https://tanstack.com/query/latest/docs/framework/react/overview
- shadcn/ui Card: https://ui.shadcn.com/docs/components/card

## OTHER CONSIDERATIONS
- Display last 10 readings
- Show temperature trends
- Highlight out-of-range values
- Refresh every 30 seconds
```

**Pro Tip:** Reference `INITIAL_EXAMPLE.md` for a complete, detailed example!

### Step 2: Generate PRP (1 minute)

In Claude Code, run:

```
/generate-prp INITIAL.md
```

Claude will:
- ✅ Analyze your codebase for similar patterns
- ✅ Research external documentation
- ✅ Create comprehensive implementation plan
- ✅ Save to `PRPs/temperature-dashboard.md`

**Review the PRP** before executing to ensure it captured everything correctly.

### Step 3: Execute PRP (2 minutes to start)

In Claude Code, run:

```
/execute-prp PRPs/temperature-dashboard.md
```

Claude will:
- ✅ Create detailed task plan with TodoWrite
- ✅ Implement each component following patterns
- ✅ Run validation loops (lint, build, test)
- ✅ Fix failures automatically
- ✅ Verify success criteria

## 📂 What Got Installed

Your repository now has:

```
safe-catch-flow/
├── .claude/
│   └── commands/
│       ├── generate-prp.md       # PRP generation logic
│       └── execute-prp.md        # PRP execution logic
├── PRPs/
│   └── templates/
│       └── prp_base.md          # Template for all PRPs
├── examples/
│   └── README.md                # Code pattern documentation
├── INITIAL.md                   # Feature request template
├── INITIAL_EXAMPLE.md           # Complete example
├── PRP_QUICK_START.md           # This guide
└── CLAUDE.md                    # Updated with PRP framework docs
```

## ✅ Best Practices

### DO:
- ✅ **Reference existing code** in INITIAL.md
- ✅ **Be specific** about requirements and validation rules
- ✅ **Include documentation links** to relevant sections
- ✅ **Define success criteria** clearly
- ✅ **Review PRPs** before executing

### DON'T:
- ❌ Write vague requirements ("make it better")
- ❌ Skip the examples section
- ❌ Forget validation rules
- ❌ Omit error handling requirements
- ❌ Skip the review step

## 🎓 Learning Resources

1. **Start Here:** Read `INITIAL_EXAMPLE.md` for a complete example
2. **Templates:** Use `INITIAL.md` as your starting template
3. **Patterns:** Check `examples/README.md` for code patterns
4. **Reference:** See `CLAUDE.md` section "PRP Framework"
5. **Base Template:** Review `PRPs/templates/prp_base.md`

## 💡 Real-World Example

Let's say you want to add a supplier delivery form:

**1. Create INITIAL.md:**
```markdown
## FEATURE
Add supplier delivery form with temperature and lot tracking

## EXAMPLES
- Form: src/components/HaccpEventForm.tsx
- Database: src/lib/sqlite-service.ts (insertProduct)
- Schema: public/sqlite-schema.sql

## DOCUMENTATION
- React Hook Form: https://react-hook-form.com/docs/useform
- Zod: https://zod.dev

## OTHER CONSIDERATIONS
- Temperature range: 32-212°F
- Required: supplier, product, quantity, temperature, lot_number
- Show success toast after submission
```

**2. Generate:**
```
/generate-prp INITIAL.md
```

**3. Review** `PRPs/supplier-delivery-form.md`

**4. Execute:**
```
/execute-prp PRPs/supplier-delivery-form.md
```

**5. Test:**
```bash
npm run dev
# Navigate to the new form and test
```

Done! 🎉

## 🔍 Debugging Tips

**If PRP generation seems incomplete:**
- Add more examples from your codebase
- Include more specific documentation URLs
- Clarify validation rules and edge cases

**If execution fails:**
- Check the validation loop errors (lint, build)
- Review the PRP for missing patterns
- Verify referenced files exist
- Check browser console for runtime errors

**If tests fail:**
- Run `npm run lint` to check for errors
- Run `npm run build:dev` to verify TypeScript
- Check browser DevTools console
- Verify SQLite operations in staging dashboard

## 📊 Validation Levels

Every PRP execution runs through validation:

**Level 1: Syntax & Types**
```bash
npm run lint        # No ESLint errors
npm run build:dev   # TypeScript compiles
```

**Level 2: Manual Testing**
```bash
npm run dev         # Test in browser
# Verify UI, forms, data persistence
```

**Level 3: Data Integrity**
- SQLite operations work correctly
- Staging dashboard shows records
- Supabase sync works (if applicable)
- Error handling functions properly

## 🎯 Success Criteria Template

Every PRP should define what "done" looks like:

- [ ] Feature works as specified
- [ ] No TypeScript errors
- [ ] No ESLint warnings
- [ ] UI renders correctly
- [ ] Form validation works
- [ ] Data persists to SQLite
- [ ] Sync to Supabase works (if applicable)
- [ ] Error handling implemented
- [ ] User feedback (toasts) working
- [ ] No regressions in existing features

## 🚦 Next Steps

1. **Try it now:** Create a simple feature request in INITIAL.md
2. **Generate PRP:** Run `/generate-prp INITIAL.md`
3. **Review:** Check the generated PRP in `PRPs/`
4. **Execute:** Run `/execute-prp PRPs/[your-feature].md`
5. **Test:** Verify everything works with `npm run dev`

## 📚 Advanced Topics

Once you're comfortable with basics:

- **Iterate on PRPs**: Update and refine based on learnings
- **Build a library**: Save successful PRPs as templates
- **Complex features**: Break into multiple smaller PRPs
- **Team workflow**: Share PRPs for consistency across team

## 💬 Common Questions

**Q: Can I edit a generated PRP?**
A: Yes! PRPs are markdown files. Edit before executing.

**Q: What if I need to change something mid-execution?**
A: Stop execution, edit the PRP, and re-run `/execute-prp`.

**Q: Can I reuse PRPs?**
A: Absolutely! Successful PRPs become templates for similar features.

**Q: Do I always need all sections in INITIAL.md?**
A: Yes, but they can be brief. More context = better results.

**Q: What if execution fails?**
A: Check validation errors, fix issues, and re-run. The PRP tracks progress with TodoWrite.

## 🎉 You're Ready!

You now have everything you need to use the PRP framework effectively. Start with a simple feature and work your way up to complex implementations.

Happy building! 🚀

---

**Need Help?**
- Check `INITIAL_EXAMPLE.md` for a complete example
- Review `CLAUDE.md` for project-specific patterns
- Reference `examples/README.md` for code patterns
- Look at `PRPs/templates/prp_base.md` for PRP structure
