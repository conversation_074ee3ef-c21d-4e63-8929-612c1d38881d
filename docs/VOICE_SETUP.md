# Voice Integration Setup Guide

Complete step-by-step guide to setting up the voice integration with OpenAI Realtime API.

## Prerequisites

- **Node.js** 18+ and npm/yarn
- **Modern browser** (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- **OpenAI account** with API access
- **Microphone** (built-in or external)
- **Stable internet connection** (recommended: 5+ Mbps)

## Part 1: Get Your OpenAI API Key

### Step 1: Create OpenAI Account

1. Visit [https://platform.openai.com/signup](https://platform.openai.com/signup)
2. Sign up with email or Google/Microsoft account
3. Verify your email address
4. Complete account setup

### Step 2: Set Up Billing

**Important:** The Realtime API requires a paid account.

1. Go to [https://platform.openai.com/account/billing](https://platform.openai.com/account/billing)
2. Click "Add payment method"
3. Enter credit card information
4. Add initial credits (minimum $5 recommended for testing)

### Step 3: Generate API Key

1. Navigate to [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
2. Click "Create new secret key"
3. Name your key (e.g., "SafeCatch Voice Integration")
4. Click "Create secret key"
5. **Copy the key immediately** - you won't be able to see it again
6. Store it securely (use a password manager)

**Security Note:** Never commit your API key to version control or share it publicly.

## Part 2: Configure Your Environment

### Step 1: Clone or Download Project

```bash
# If using git
git clone <repository-url>
cd safe-catch-flow

# Or download and extract ZIP
```

### Step 2: Install Dependencies

```bash
# Using npm
npm install

# Using yarn
yarn install
```

### Step 3: Create Environment File

```bash
# Copy the example environment file
cp .env.example .env
```

### Step 4: Add Your API Key

Open `.env` in your text editor and update:

```bash
# OpenAI Realtime API Configuration
VITE_OPENAI_API_KEY=sk-your-actual-api-key-here
VITE_OPENAI_REALTIME_ENDPOINT=wss://api.openai.com/v1/realtime

# Optional: Custom model settings (defaults shown)
# VITE_OPENAI_MODEL=gpt-4-realtime-preview
# VITE_OPENAI_VOICE=alloy
```

**Replace `sk-your-actual-api-key-here` with your actual OpenAI API key from Part 1, Step 3.**

### Step 5: Verify Environment Variables

```bash
# Quick check (should show your key)
cat .env | grep VITE_OPENAI_API_KEY

# Should output:
# VITE_OPENAI_API_KEY=sk-proj-...
```

## Part 3: Start the Development Server

### Step 1: Start Application

```bash
# Using npm
npm run dev

# Using yarn
yarn dev
```

You should see:

```
VITE v5.4.19  ready in 432 ms

  ➜  Local:   http://localhost:8080/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
```

### Step 2: Open in Browser

Navigate to [http://localhost:8080](http://localhost:8080)

## Part 4: Grant Microphone Permissions

### First Time Setup

When you click the microphone button for the first time, your browser will ask for permission.

#### Chrome/Edge

1. Click the microphone icon in the voice agent
2. Browser shows popup: "localhost wants to use your microphone"
3. Click **"Allow"**
4. Optionally check "Remember this decision"

#### Firefox

1. Click the microphone icon
2. Browser shows popup with dropdown: "Share Microphone"
3. Select your microphone from the dropdown
4. Check "Remember this decision"
5. Click **"Allow"**

#### Safari

1. Click the microphone icon
2. Safari shows dialog: "localhost would like to access your microphone"
3. Click **"Allow"**

### Troubleshooting Permissions

If microphone access is denied:

**Chrome/Edge:**
```
1. Click padlock icon (🔒) in address bar
2. Find "Microphone" setting
3. Change to "Allow"
4. Refresh page (Ctrl+R or Cmd+R)
```

**Firefox:**
```
1. Click padlock icon in address bar
2. Click ">" arrow next to "Connection Secure"
3. Click "More Information"
4. Go to "Permissions" tab
5. Find "Use the Microphone"
6. Uncheck "Use Default" and select "Allow"
7. Refresh page
```

**Safari:**
```
1. Safari menu → Settings → Websites
2. Click "Microphone" in left sidebar
3. Find localhost:8080
4. Change to "Allow"
5. Refresh page
```

## Part 5: Test Voice Integration

### Step 1: Start Voice Session

1. Look for the microphone button in your UI
2. Click the microphone button
3. Wait for connection confirmation (toast notification)
4. You should see: "Connected - Voice agent ready (excellent connection)"

### Step 2: Test Basic Commands

Try these commands to verify everything works:

```
"Hello, can you hear me?"
→ Should respond with greeting

"What products do we have?"
→ Should list available products

"Add 20 units of salmon to Freezer A as a receipt"
→ Should confirm inventory addition

"Check the status of salmon"
→ Should provide product information
```

### Step 3: Verify Tool Execution

When a command triggers a tool:

1. **Visual Feedback:** Tool execution UI appears
2. **Console Logs:** Check DevTools → Console for detailed logs
3. **Toast Notification:** Success or error message appears
4. **Voice Response:** AI confirms action verbally

## Part 6: Verify Installation

### Checklist

- [ ] OpenAI API key obtained and added to `.env`
- [ ] Dependencies installed successfully
- [ ] Development server starts without errors
- [ ] Browser opens at localhost:8080
- [ ] Microphone permission granted
- [ ] Voice button connects successfully
- [ ] Test command recognized and executed
- [ ] Tool execution feedback visible
- [ ] No console errors in DevTools

### Debug Checklist

If something doesn't work, check:

```bash
# 1. Verify API key is set
echo $VITE_OPENAI_API_KEY
# Should output your key (not empty)

# 2. Check server is running
curl http://localhost:8080
# Should return HTML

# 3. Check browser console
# Open DevTools (F12)
# Look for errors in Console tab

# 4. Check Network tab
# Look for WebSocket connection
# Should show wss://api.openai.com/v1/realtime

# 5. Verify microphone
# Go to https://www.onlinemictest.com/
# Test if your microphone works
```

## Part 7: Production Deployment

### Environment Variables

For production deployment, set environment variables in your hosting platform:

**Vercel:**
```bash
vercel env add VITE_OPENAI_API_KEY
# Paste your API key when prompted
```

**Netlify:**
```
1. Site Settings → Environment Variables
2. Add new variable:
   Key: VITE_OPENAI_API_KEY
   Value: <your-api-key>
```

**Docker:**
```dockerfile
ENV VITE_OPENAI_API_KEY=sk-your-key-here
```

### Security Best Practices

1. **Never expose API key in client code**
2. **Use environment variables** (never hardcode)
3. **Rotate keys regularly** (monthly recommended)
4. **Monitor usage** on OpenAI dashboard
5. **Set usage limits** to prevent unexpected charges
6. **Use key restrictions** if available

## Cost Estimation

### Realtime API Pricing

The OpenAI Realtime API charges for:
- **Audio input:** ~$0.06 per minute
- **Audio output:** ~$0.24 per minute
- **Text output:** $0.002 per 1K tokens

### Example Usage Costs

- **1 hour of voice testing:** ~$18 (input + output)
- **5-minute conversation:** ~$1.50
- **Daily production use (30 min):** ~$9

**Tip:** Set a monthly budget limit in OpenAI dashboard to avoid surprises.

## Optimization Tips

### Reduce Costs

1. **Use text mode for testing** (when possible)
2. **Keep conversations focused** (avoid long responses)
3. **Implement command timeouts** (auto-disconnect after inactivity)
4. **Cache common responses** (avoid repeated API calls)

### Improve Performance

1. **Use wired connection** (ethernet > WiFi)
2. **Close unnecessary browser tabs**
3. **Disable browser extensions** (during testing)
4. **Use modern browser version**
5. **Enable hardware acceleration** in browser settings

## Next Steps

- Read [VOICE_TROUBLESHOOTING.md](./VOICE_TROUBLESHOOTING.md) for common issues
- Review [VOICE_INTEGRATION.md](./VOICE_INTEGRATION.md) for architecture details
- Check [VOICE_API_REFERENCE.md](./VOICE_API_REFERENCE.md) for API documentation
- See [VOICE_TESTING_GUIDE.md](./VOICE_TESTING_GUIDE.md) for testing strategies

## Support

### Common Issues

See [VOICE_TROUBLESHOOTING.md](./VOICE_TROUBLESHOOTING.md) for detailed solutions to:

- Connection errors
- Authentication failures
- Microphone issues
- Network problems
- Tool execution errors

### Getting Help

If you encounter issues:

1. Check browser console for errors
2. Review troubleshooting guide
3. Search existing GitHub issues
4. Create new issue with:
   - Error message
   - Steps to reproduce
   - Browser and OS version
   - Console logs
   - Screenshots

---

**Last Updated:** October 24, 2025
**Version:** 1.0.0
**For:** Safe Catch Flow Voice Integration
