# Voice Integration API Reference

## Quick Start

### Basic Usage Pattern

```typescript
import VoiceToolExecutor, { Tool<PERSON><PERSON>, ToolResult } from '@/lib/voice-tool-executor';

// Create a tool call
const toolCall: ToolCall = {
  name: 'add_inventory_event',
  arguments: {
    product_id: 'salmon-001',
    quantity: 20,
    event_type: 'receipt',
    location: 'Freezer A'
  },
  call_id: 'cmd-001'
};

// Execute the tool
const result = await VoiceToolExecutor.executeTool(toolCall);

// Handle result
if (result.success) {
  console.log('Success:', result.data);
} else {
  console.error('Error:', result.error);
}
```

## Core Interfaces

### ToolCall

Represents a request to execute a tool.

```typescript
interface ToolCall {
  name: string;                    // Tool function name
  arguments: Record<string, unknown>; // Tool arguments
  call_id?: string;               // Optional unique identifier
}
```

**Tool Names:**
- `add_inventory_event` - Add inventory transaction
- `update_product_location` - Update product storage location
- `check_product_status` - Retrieve product inventory status
- `record_ccp_monitoring` - Record Critical Control Point measurement
- `get_haccp_events` - Retrieve compliance event history

### ToolResult

Represents the result of a tool execution.

```typescript
interface ToolResult {
  success: boolean;           // Whether operation succeeded
  message: string;            // Human-readable message
  data?: unknown;             // Result data (varies by tool)
  error?: string;             // Error message if failed
  errorType?: VoiceErrorType; // Classification of error
}
```

### VoiceErrorType

Enumeration of error types.

```typescript
enum VoiceErrorType {
  NETWORK = 'NETWORK',           // Network/connection error
  VALIDATION = 'VALIDATION',     // Input validation error
  DATABASE = 'DATABASE',         // Database operation error
  TIMEOUT = 'TIMEOUT',           // Operation timeout
  UNKNOWN = 'UNKNOWN'            // Unknown error
}
```

## Tool Specifications

### 1. add_inventory_event

Adds a new inventory event to the database.

**Signature:**
```typescript
function add_inventory_event(
  product_id: string,
  quantity: number,
  event_type: 'receipt' | 'usage' | 'adjustment',
  location: string,
  notes?: string
): Promise<ToolResult>
```

**Parameters:**

| Parameter | Type | Required | Description | Constraints |
|-----------|------|----------|-------------|-------------|
| product_id | string | Yes | Unique product identifier | Non-empty, max 255 chars |
| quantity | number | Yes | Quantity of units | Positive number, < 1,000,000 |
| event_type | enum | Yes | Type of event | One of: receipt, usage, adjustment |
| location | string | Yes | Storage location | Non-empty, max 255 chars |
| notes | string | No | Additional notes | Max 1000 chars |

**Response Data:**
```json
{
  "inventory_id": "inv-12345",      // Unique inventory ID
  "quantity_added": 20,              // Quantity processed
  "location": "Freezer A",           // Storage location
  "timestamp": "2025-10-24T10:30:00Z" // Operation timestamp
}
```

**Error Scenarios:**

| Error Type | Message | Solution |
|-----------|---------|----------|
| VALIDATION | Missing required field: product_id | Specify product name/ID |
| VALIDATION | Invalid quantity: must be positive | Use positive number |
| VALIDATION | Invalid event_type | Use: receipt, usage, adjustment |
| DATABASE | Database connection failed | Retry or check connection |
| TIMEOUT | Operation exceeded timeout | Retry command |

**Examples:**

```typescript
// Receipt of salmon
const result = await VoiceToolExecutor.executeTool({
  name: 'add_inventory_event',
  arguments: {
    product_id: 'salmon-001',
    quantity: 20,
    event_type: 'receipt',
    location: 'Freezer A',
    notes: 'Fresh Atlantic salmon delivery'
  }
});

// Usage adjustment
const result = await VoiceToolExecutor.executeTool({
  name: 'add_inventory_event',
  arguments: {
    product_id: 'cod-001',
    quantity: 5,
    event_type: 'usage',
    location: 'Cold Storage Room 1'
  }
});

// Inventory correction
const result = await VoiceToolExecutor.executeTool({
  name: 'add_inventory_event',
  arguments: {
    product_id: 'halibut-001',
    quantity: -2,
    event_type: 'adjustment',
    location: 'Freezer B'
  }
});
```

### 2. update_product_location

Updates the storage location of a product.

**Signature:**
```typescript
function update_product_location(
  product_id: string,
  new_location: string
): Promise<ToolResult>
```

**Parameters:**

| Parameter | Type | Required | Description | Constraints |
|-----------|------|----------|-------------|-------------|
| product_id | string | Yes | Product identifier | Non-empty, max 255 chars |
| new_location | string | Yes | New storage location | Non-empty, max 255 chars |

**Response Data:**
```json
{
  "product_id": "salmon-001",           // Product ID
  "previous_location": "Freezer A",     // Original location
  "new_location": "Cold Storage Room 2", // New location
  "updated_at": "2025-10-24T10:30:00Z"  // Update timestamp
}
```

**Error Scenarios:**

| Error Type | Message | Solution |
|-----------|---------|----------|
| VALIDATION | Missing required field: product_id | Specify product |
| VALIDATION | Missing required field: new_location | Specify destination |
| DATABASE | Product not found | Verify product ID |
| DATABASE | Location not available | Check location exists |

**Examples:**

```typescript
// Move to different freezer
const result = await VoiceToolExecutor.executeTool({
  name: 'update_product_location',
  arguments: {
    product_id: 'salmon-001',
    new_location: 'Freezer B'
  }
});

// Move to cold storage room
const result = await VoiceToolExecutor.executeTool({
  name: 'update_product_location',
  arguments: {
    product_id: 'cod-002',
    new_location: 'Cold Storage Room 2'
  }
});
```

### 3. check_product_status

Retrieves current inventory status for a product.

**Signature:**
```typescript
function check_product_status(
  product_id: string
): Promise<ToolResult>
```

**Parameters:**

| Parameter | Type | Required | Description | Constraints |
|-----------|------|----------|-------------|-------------|
| product_id | string | Yes | Product identifier | Non-empty, max 255 chars |

**Response Data:**
```json
{
  "product_id": "salmon-001",           // Product ID
  "quantity": 35,                       // Current quantity
  "location": "Cold Storage Room 2",    // Current location
  "min_stock": 10,                      // Minimum stock level
  "status": "above_minimum",            // Status: above_minimum, critical
  "last_updated": "2025-10-24T10:30:00Z" // Last update time
}
```

**Error Scenarios:**

| Error Type | Message | Solution |
|-----------|---------|----------|
| VALIDATION | Missing required field: product_id | Specify product |
| DATABASE | Product not found | Verify product exists |
| DATABASE | Database query failed | Retry operation |

**Examples:**

```typescript
// Check salmon status
const result = await VoiceToolExecutor.executeTool({
  name: 'check_product_status',
  arguments: {
    product_id: 'salmon-001'
  }
});

if (result.success) {
  const { quantity, location, status } = result.data;
  console.log(`${quantity} units in ${location} (${status})`);
}
```

### 4. record_ccp_monitoring

Records Critical Control Point monitoring measurements.

**Signature:**
```typescript
function record_ccp_monitoring(
  ccp_type: 'temperature' | 'humidity' | 'ph' | 'time',
  value: number,
  unit: string,
  location: string,
  status: 'within_limit' | 'out_of_range' | 'critical'
): Promise<ToolResult>
```

**Parameters:**

| Parameter | Type | Required | Description | Constraints |
|-----------|------|----------|-------------|-------------|
| ccp_type | enum | Yes | Type of CCP | temperature, humidity, ph, time |
| value | number | Yes | Measured value | Numeric, range depends on type |
| unit | string | Yes | Unit of measurement | celsius, fahrenheit, percent, etc |
| location | string | Yes | Measurement location | Non-empty, max 255 chars |
| status | enum | Yes | Status classification | within_limit, out_of_range, critical |

**Valid Value Ranges:**

| CCP Type | Min | Max | Unit | Default Range |
|----------|-----|-----|------|----------------|
| temperature | -50 | 80 | celsius | -30 to 5 (refrigeration) |
| temperature | -58 | 176 | fahrenheit | -22 to 41 |
| humidity | 0 | 100 | percent | 40-80 (typical storage) |
| ph | 0 | 14 | pH units | 6.0-8.0 (fish products) |

**Response Data:**
```json
{
  "ccp_id": "ccp-12345",                    // Unique CCP ID
  "ccp_type": "temperature",                // Type of CCP
  "value": 4.5,                             // Recorded value
  "unit": "celsius",                        // Unit
  "location": "Refrigerator 1",             // Location
  "status": "within_limit",                 // Status
  "recorded_at": "2025-10-24T10:30:00Z"    // Timestamp
}
```

**Error Scenarios:**

| Error Type | Message | Solution |
|-----------|---------|----------|
| VALIDATION | Invalid CCP type | Use: temperature, humidity, ph, time |
| VALIDATION | Invalid value for CCP type | Check temperature range (-50 to 80°C) |
| VALIDATION | Invalid status | Use: within_limit, out_of_range, critical |
| DATABASE | Database write failed | Retry operation |

**Examples:**

```typescript
// Record temperature (Celsius)
const result = await VoiceToolExecutor.executeTool({
  name: 'record_ccp_monitoring',
  arguments: {
    ccp_type: 'temperature',
    value: 4.5,
    unit: 'celsius',
    location: 'Refrigerator 1',
    status: 'within_limit'
  }
});

// Record humidity
const result = await VoiceToolExecutor.executeTool({
  name: 'record_ccp_monitoring',
  arguments: {
    ccp_type: 'humidity',
    value: 65,
    unit: 'percent',
    location: 'Freezer A',
    status: 'within_limit'
  }
});

// Out-of-range alarm
const result = await VoiceToolExecutor.executeTool({
  name: 'record_ccp_monitoring',
  arguments: {
    ccp_type: 'temperature',
    value: 15,
    unit: 'celsius',
    location: 'Freezer A',
    status: 'out_of_range'
  }
});
```

### 5. get_haccp_events

Retrieves HACCP event records from the database.

**Signature:**
```typescript
function get_haccp_events(
  limit?: number,
  status?: string
): Promise<ToolResult>
```

**Parameters:**

| Parameter | Type | Required | Description | Constraints |
|-----------|------|----------|-------------|-------------|
| limit | number | No | Max records to return | 1-100, default 10 |
| status | string | No | Filter by status | compliant, warning, critical |

**Response Data:**
```json
[
  {
    "id": "evt-001",
    "type": "temperature_check",
    "location": "Freezer A",
    "value": 3.2,
    "status": "compliant",
    "timestamp": "2025-10-24T10:30:00Z"
  },
  {
    "id": "evt-002",
    "type": "humidity_check",
    "location": "Freezer A",
    "value": 45,
    "status": "compliant",
    "timestamp": "2025-10-24T10:15:00Z"
  }
]
```

**Error Scenarios:**

| Error Type | Message | Solution |
|-----------|---------|----------|
| VALIDATION | Invalid limit | Use number between 1-100 |
| DATABASE | Database query failed | Retry operation |

**Examples:**

```typescript
// Get last 10 events (default)
const result = await VoiceToolExecutor.executeTool({
  name: 'get_haccp_events',
  arguments: { limit: 10 }
});

// Get last 20 compliant events
const result = await VoiceToolExecutor.executeTool({
  name: 'get_haccp_events',
  arguments: {
    limit: 20,
    status: 'compliant'
  }
});

// Use result
if (result.success && Array.isArray(result.data)) {
  const events = result.data;
  console.log(`Retrieved ${events.length} HACCP events`);
}
```

## Error Handling

### Error Classification

Errors are classified into categories for better handling:

```typescript
switch (result.errorType) {
  case VoiceErrorType.VALIDATION:
    // User input was invalid - ask for clarification
    showUserMessage('Please check your input: ' + result.error);
    break;

  case VoiceErrorType.DATABASE:
    // Database issue - offer retry
    showUserMessage('Database error - please retry');
    offerRetryButton();
    break;

  case VoiceErrorType.NETWORK:
    // Network issue - check connection
    showUserMessage('Network error - checking connection');
    retryWithExponentialBackoff();
    break;

  case VoiceErrorType.TIMEOUT:
    // Operation took too long - retry
    showUserMessage('Operation timed out - retrying');
    await retryToolExecution(toolCall);
    break;

  case VoiceErrorType.UNKNOWN:
    // Unknown error - log and inform user
    logError(result);
    showUserMessage('An unexpected error occurred');
    break;
}
```

### Retry Logic

```typescript
async function retryToolWithBackoff(
  toolCall: ToolCall,
  maxRetries: number = 3
): Promise<ToolResult> {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      const result = await VoiceToolExecutor.executeTool(toolCall);
      if (result.success) {
        return result;
      }

      // Exponential backoff before retry
      const delay = Math.pow(2, attempt) * 1000; // 1s, 2s, 4s
      await new Promise(resolve => setTimeout(resolve, delay));
    } catch (error) {
      if (attempt === maxRetries - 1) throw error;
    }
  }

  throw new Error('Max retries exceeded');
}
```

## Component Integration

### VoiceAgent Component

```typescript
import VoiceAgent from '@/components/VoiceAgent';

export default function MyPage() {
  return (
    <VoiceAgent
      onSpeakingChange={(speaking) => {
        console.log('User is speaking:', speaking);
      }}
    />
  );
}
```

### VoiceToolFeedback Component

```typescript
import VoiceToolFeedback from '@/components/VoiceToolFeedback';
import { ToolCall, ToolResult } from '@/lib/voice-tool-executor';

interface Props {
  toolCall: ToolCall;
  result?: ToolResult;
  isExecuting?: boolean;
  error?: string;
  onDismiss?: () => void;
}

export default function MyComponent({ toolCall, result, isExecuting }: Props) {
  return (
    <VoiceToolFeedback
      toolCall={toolCall}
      result={result}
      isExecuting={isExecuting}
      onDismiss={() => console.log('Dismissed')}
    />
  );
}
```

## Advanced Usage

### Custom Tool Wrapper

```typescript
async function executeInventoryCommand(
  productId: string,
  quantity: number,
  location: string
): Promise<void> {
  const toolCall: ToolCall = {
    name: 'add_inventory_event',
    arguments: {
      product_id: productId,
      quantity,
      event_type: 'receipt',
      location
    },
    call_id: generateCallId()
  };

  try {
    const result = await VoiceToolExecutor.executeTool(toolCall);

    if (result.success) {
      console.log('Inventory added:', result.data);
    } else {
      handleError(result.errorType, result.error);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}
```

### Batch Operations

```typescript
async function executeMultipleTools(
  toolCalls: ToolCall[]
): Promise<ToolResult[]> {
  const results = await Promise.all(
    toolCalls.map(call => VoiceToolExecutor.executeTool(call))
  );

  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);

  console.log(`${successful.length} succeeded, ${failed.length} failed`);
  return results;
}
```

### Audit Logging Integration

```typescript
import { auditLogger, AuditEventType, AuditSeverity } from '@/lib/voice-audit-logger';

const result = await VoiceToolExecutor.executeTool(toolCall);

auditLogger.log(
  result.success ? AuditEventType.TOOL_EXECUTED : AuditEventType.TOOL_FAILED,
  AuditSeverity.INFO,
  `Tool ${toolCall.name} ${result.success ? 'succeeded' : 'failed'}`,
  {
    toolName: toolCall.name,
    success: result.success,
    errorType: result.errorType,
    duration: Date.now() - startTime
  }
);
```

## Type Definitions

For TypeScript users, import these types:

```typescript
// Main types
import {
  ToolCall,
  ToolResult,
  VoiceToolExecutor
} from '@/lib/voice-tool-executor';

// Error types
import {
  VoiceErrorType,
  VoiceAgentError,
  classifyError,
  logError
} from '@/lib/voice-error-handler';

// Audit logging
import {
  auditLogger,
  AuditEventType,
  AuditSeverity,
  AuditLogEntry
} from '@/lib/voice-audit-logger';

// Validation
import {
  validateToolArguments,
  formatValidationError
} from '@/lib/voice-validation';
```

## Performance Considerations

### Timeouts

```typescript
// Tool execution has a 30-second timeout
// If exceeded, returns TIMEOUT error

// Recommended timeouts by operation:
- add_inventory_event: 5-10 seconds
- update_product_location: 3-5 seconds
- check_product_status: 2-3 seconds
- record_ccp_monitoring: 3-5 seconds
- get_haccp_events: 5-10 seconds
```

### Caching Strategy

```typescript
// Consider caching product status
const productStatusCache = new Map<string, CachedStatus>();

async function getProductStatusCached(productId: string) {
  const cached = productStatusCache.get(productId);
  if (cached && Date.now() - cached.timestamp < 60000) {
    return cached.data;
  }

  const result = await VoiceToolExecutor.executeTool({
    name: 'check_product_status',
    arguments: { product_id: productId }
  });

  if (result.success) {
    productStatusCache.set(productId, {
      data: result.data,
      timestamp: Date.now()
    });
  }

  return result;
}
```

---

**Last Updated:** October 24, 2025
**Version:** 1.0.0
