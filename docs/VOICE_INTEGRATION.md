# Voice Integration Guide

## Overview

Safe Catch Flow includes a comprehensive voice integration system that allows users to control the application using natural speech. The voice system supports:

- **Real-time audio streaming** via WebRTC for low-latency interaction
- **Tool calling** for executing inventory operations, compliance checks, and monitoring tasks
- **Error handling & validation** with comprehensive error recovery
- **Audit logging** for compliance and troubleshooting
- **Natural language understanding** through OpenAI's Realtime API

## Features

### 1. Voice-Controlled Operations

The voice system supports the following operations:

- **Inventory Management**: Add receipts, adjustments, and usage events
- **Product Location Updates**: Move products between storage locations
- **Product Status Queries**: Check inventory levels and locations
- **CCP Monitoring**: Record temperature, humidity, and other critical control points
- **HACCP Event Retrieval**: Access compliance event history

### 2. Error Handling

The system automatically handles:

- Network failures with graceful degradation
- Database connection issues with retry logic
- Validation errors with clear user feedback
- Timeout scenarios with configurable limits
- Partial failures in command sequences

### 3. Audit Logging

All voice commands are logged with:

- Command execution timestamps
- User attribution (created_by field)
- Tool execution details
- Success/failure status
- Error classification and recovery attempts
### 5. Voice Data Visibility

The system provides multiple ways to track and monitor voice agent activity:

- **Console Logging**: Real-time structured output when voice agent creates records
- **UI Component**: Dedicated component for viewing voice-created inventory records
- **Programmatic Access**: Query methods for filtering voice agent data
- **Audit Trail**: All voice-created records tagged with `created_by: 'voice-agent'`


### 4. User Feedback

Rich visual feedback includes:

- Loading indicators during tool execution
- Success confirmations with result data
- Error messages with troubleshooting hints
- Tool execution summaries

## Setup Guide

### Prerequisites

- Modern browser with WebRTC support (Chrome, Firefox, Safari, Edge)
- Microphone access enabled
- OpenAI API key configured in Supabase environment

### Installation

Voice integration is built into Safe Catch Flow. No additional installation is required.

To access voice features:

1. Navigate to any form or data entry page
2. Look for the microphone icon in the interface
3. Click to initiate voice input
4. Speak your command naturally

### Configuration

Voice integration is configured through environment variables:

```env
# OpenAI Realtime API configuration
VITE_OPENAI_API_KEY=your_api_key_here
VITE_OPENAI_BASE_URL=https://api.openai.com/v1

# Optional: Custom endpoint configuration
VITE_VOICE_TIMEOUT=30000  # 30 seconds
VITE_VOICE_LANGUAGE=en-US
```

## API Reference

### Tool Definitions

#### add_inventory_event

Adds a new inventory event (receipt, usage, or adjustment).

**Parameters:**
- `product_id` (string, required): Unique product identifier
- `quantity` (number, required): Quantity of units
- `event_type` (string, required): One of `receipt`, `usage`, `adjustment`
- `location` (string, required): Storage location
- `notes` (string, optional): Additional notes about the event

**Response:**
```json
{
  "success": true,
  "message": "Inventory event added successfully",
  "data": {
    "inventory_id": "inv-12345",
    "quantity_added": 20,
    "location": "Freezer A",
    "timestamp": "2025-10-24T10:30:00Z"
  }
}
```

**Example Usage:**
```
"Add 20 units of salmon to Freezer A"
"Record a receipt of cod at Cold Storage Room 2"
"Adjust inventory for halibut by minus 5"
```

#### update_product_location

Updates the storage location of a product.

**Parameters:**
- `product_id` (string, required): Unique product identifier
- `new_location` (string, required): Destination storage location

**Response:**
```json
{
  "success": true,
  "message": "Product location updated",
  "data": {
    "product_id": "salmon-001",
    "previous_location": "Freezer A",
    "new_location": "Cold Storage Room 2",
    "updated_at": "2025-10-24T10:30:00Z"
  }
}
```

**Example Usage:**
```
"Move salmon to Cold Storage Room 2"
"Relocate halibut from Freezer A to Freezer B"
```

#### check_product_status

Retrieves current status and inventory level for a product.

**Parameters:**
- `product_id` (string, required): Unique product identifier

**Response:**
```json
{
  "success": true,
  "message": "Product status retrieved",
  "data": {
    "product_id": "salmon-001",
    "quantity": 35,
    "location": "Cold Storage Room 2",
    "min_stock": 10,
    "status": "above_minimum",
    "last_updated": "2025-10-24T10:30:00Z"
  }
}
```

**Example Usage:**
```
"What's the status of salmon?"
"Check inventory level for cod"
"Where is the halibut stored?"
```

#### record_ccp_monitoring

Records Critical Control Point (CCP) monitoring data.

**Parameters:**
- `ccp_type` (string, required): Type of CCP (`temperature`, `humidity`, `ph`, `time`)
- `value` (number, required): Measured value
- `unit` (string, required): Unit of measurement (e.g., `celsius`, `fahrenheit`, `percent`)
- `location` (string, required): Location where measurement was taken
- `status` (string, required): One of `within_limit`, `out_of_range`, `critical`

**Response:**
```json
{
  "success": true,
  "message": "CCP monitoring data recorded",
  "data": {
    "ccp_id": "ccp-12345",
    "ccp_type": "temperature",
    "value": 4.5,
    "unit": "celsius",
    "location": "Refrigerator 1",
    "status": "within_limit",
    "recorded_at": "2025-10-24T10:30:00Z"
  }
}
```

**Valid Temperature Ranges:**
- Freezer: -30 to -15°C
- Refrigerator: 0 to 5°C
- Cool storage: 5 to 15°C
- Room temperature: 15 to 25°C

**Example Usage:**
```
"Temperature in Freezer A is 2 degrees Celsius"
"Record humidity at 45 percent in Freezer B"
"Set refrigerator temperature to 4 degrees"
```

#### get_haccp_events

Retrieves HACCP (Hazard Analysis and Critical Control Points) event records.

**Parameters:**
- `limit` (number, optional): Maximum number of events to return (default: 10)
- `status` (string, optional): Filter by event status

**Response:**
```json
{
  "success": true,
  "message": "HACCP events retrieved",
  "data": [
    {
      "id": "evt-001",
      "type": "temperature_check",
      "location": "Freezer A",
      "value": 3.2,
      "status": "compliant",
      "timestamp": "2025-10-24T10:30:00Z"
    },
    {
      "id": "evt-002",
      "type": "humidity_check",
      "location": "Freezer A",
      "value": 45,
      "status": "compliant",
      "timestamp": "2025-10-24T10:15:00Z"
    }
  ]
}
```

**Example Usage:**
```
"Show me recent HACCP events"

## Voice Data Visibility

The voice agent automatically tracks all records it creates for easy monitoring and debugging. This feature provides multiple ways to view and query voice-created data.

### Viewing Voice-Created Records

#### Option 1: UI Component

Access the Voice Inventory Records component to see all voice-created entries in a table format:

```typescript
import { VoiceInventoryRecords } from '@/components/VoiceInventoryRecords';

<VoiceInventoryRecords />
```

**Component Features:**
- Displays last 20 voice-created inventory records
- Shows product ID, batch number, quantity, location, and sync status
- Automatic refresh capability
- Empty state message for when no records exist
- Responsive table design with Tailwind CSS

**Table Columns:**
- **Product ID**: Unique identifier for the product
- **Batch Number**: Batch tracking number
- **Quantity**: Amount with unit (e.g., "100 kg")
- **Location**: Storage location or "—" if not specified
- **Created**: Relative time (e.g., "5 minutes ago")
- **Status**: Sync status badge (Synced or Pending)

#### Option 2: Console Logs

Check browser DevTools console for real-time logging when voice agent creates records:

```
✅ Voice Agent: Inventory Record Created
┌─────────────┬────────────────────────────────┐
│  (index)    │            Values              │
├─────────────┼────────────────────────────────┤
│ Record ID   │ '550e8400-e29b-41d4-a716...'  │
│ Product ID  │ 'salmon-001'                   │
│ Batch Number│ 'B2024-001'                    │
│ Quantity    │ '100 kg'                       │
│ Location    │ 'Cooler-A'                     │
│ Created By  │ 'voice-agent'                  │
│ Timestamp   │ '2024-01-15T10:30:00.000Z'    │
└─────────────┴────────────────────────────────┘
```

**CCP Monitoring Logs:**
```
✅ Voice Agent: CCP Monitoring Record Created
┌─────────────┬────────────────────────────────┐
│  (index)    │            Values              │
├─────────────┼────────────────────────────────┤
│ Record ID   │ '660e8400-e29b-41d4-a716...'  │
│ CCP Name    │ 'Temperature Check'            │
│ Measurement │ '4.5 °C'                       │
│ Within Limits│ 'Yes'                         │
│ Monitored By│ 'voice-system'                 │
│ Created By  │ 'voice-agent'                  │
│ Timestamp   │ '2024-01-15T10:30:00.000Z'    │
└─────────────┴────────────────────────────────┘
```

#### Option 3: Programmatic Access

Query voice records directly using the SQLite service:

```typescript
import { sqliteService } from '@/lib/sqlite-service';

// Get last 20 voice-created inventory records
const records = await sqliteService.getVoiceInventoryRecords(20);

// Get voice-created CCP monitoring records
const ccpRecords = await sqliteService.getVoiceCCPRecords(20);

// Filter and process records
const pendingRecords = records.filter(r => !r.synced_to_supabase);
const recentRecords = records.slice(0, 10);
```

**Method Parameters:**
- `limit` (number, optional): Maximum records to return (default: 20)

**Return Type:**
```typescript
interface StagedInventory {
  id: string;
  product_id: string;
  batch_number: string;
  quantity: number;
  unit?: string;
  location?: string;
  received_date: string;
  created_at?: string;
  synced_to_supabase?: boolean;
  created_by?: string; // Always 'voice-agent' for these methods
}
```

### Data Structure

All voice-created records include:

- **`created_by: 'voice-agent'`** - Identifier tag for filtering
- **`created_at`** - ISO 8601 timestamp
- **`synced_to_supabase`** - Boolean sync status
- **Standard table fields** - product_id, batch_number, quantity, etc.

### Use Cases

**Debugging:**
Check console logs immediately after voice commands to verify correct data capture.

**Monitoring:**
Use the UI component to review recent voice agent activity at a glance.

**Reporting:**
Query voice records programmatically to generate activity reports or analytics.

**Verification:**
Confirm voice commands were processed correctly before data sync to production.

"List the last 20 compliance checks"
"What HACCP records do we have?"
```

## Voice Command Examples

### Inventory Receipt Workflow

```
User: "Add 20 units of Atlantic salmon to Freezer A"
System: Adds inventory event
Response: "Inventory added successfully"

User: "Record the temperature at 2 degrees Celsius"
System: Records CCP monitoring
Response: "CCP monitoring data recorded"

User: "Check salmon status"
System: Retrieves product status
Response: "Salmon: 35 units in Cold Storage Room 2"
```

### Product Relocation Workflow

```
User: "Move the cod to Cold Storage Room 2"
System: Updates product location
Response: "Location updated successfully"

User: "Record temperature at 3 degrees"
System: Records monitoring data
Response: "CCP monitoring data recorded"

User: "Where is the cod now?"
System: Checks product status
Response: "Cod: 50 units in Cold Storage Room 2"
```

### Compliance Checking Workflow

```
User: "Show me recent HACCP events"
System: Retrieves compliance records
Response: "Retrieved 5 HACCP events"

User: "Check temperature in Freezer A"
System: Records monitoring
Response: "Temperature recorded as 2.5°C - within limits"
```

## Error Handling

### Common Errors and Solutions

#### Validation Error
**Message:** "Missing required field: product_id"
**Solution:** Ensure you specify which product you're referring to
```
Incorrect: "Add 20 units to Freezer A"
Correct: "Add 20 units of salmon to Freezer A"
```

#### Database Connection Error
**Message:** "Database connection failed"
**Solution:** Check internet connection and wait a few moments before retrying
```
Action: Click "Retry" or speak the command again
```

#### Network Error
**Message:** "Failed to connect to server"
**Solution:** Verify internet connectivity
```
Action: Check network status and reconnect
```

#### Timeout Error
**Message:** "Tool execution exceeded timeout limit"
**Solution:** The server took too long to respond
```
Action: Retry the command or check server status
```

#### Invalid Parameter
**Message:** "Invalid event_type: must be one of [receipt, usage, adjustment]"
**Solution:** Use correct operation type
```
Incorrect: "Add a sample of salmon"
Correct: "Add 20 units of salmon as a receipt"
```

## Validation Rules

### Quantity Validation
- Must be a positive number
- Must be less than 1,000,000 units
- Cannot be zero

### Temperature Validation
- Must be between -50°C and +80°C
- Refrigeration (0-5°C): Most recommended
- Freezing (-30 to -15°C): For frozen products

### Location Validation
- Must be a non-empty string
- Should match existing storage locations
- Examples: "Freezer A", "Cold Storage Room 1", "Chiller Unit 2"

### Event Type Validation
- Valid types: `receipt`, `usage`, `adjustment`
- `receipt`: Incoming inventory
- `usage`: Items used or consumed
- `adjustment`: Inventory correction

## Advanced Features

### Session Persistence

Voice sessions are automatically tracked with:
- Unique session IDs
- Message counts
- Tool call tracking
- Error tracking

Sessions help with:
- Debugging communication issues
- Analyzing usage patterns
- Ensuring data consistency

### Connection Quality Monitoring

The system monitors connection quality:
- **Excellent**: < 50ms latency
- **Good**: 50-100ms latency
- **Fair**: 100-200ms latency
- **Poor**: > 200ms latency

### Automatic Reconnection

If connection is lost:
1. Exponential backoff retry (1s, 2s, 4s, 8s... max 30s)
2. Automatic reconnection attempts
3. User notification of connection status
4. Graceful degradation if reconnection fails

## Testing

### Running Tests

```bash
# Run all tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests once and exit
npm run test:run
```

### Test Coverage

The test suite includes:
- **74 comprehensive tests**
- **26 integration scenario tests**
- **23 tool definition tests**
- **25 component interaction tests**

Test categories:
- Tool execution flows
- Voice command scenarios
- Error handling & recovery
- Input validation
- Type safety
- Component rendering
- User interactions
- Accessibility

## Troubleshooting

### Microphone Not Detected

**Issue:** Voice input button appears disabled
**Solutions:**
1. Check browser permissions for microphone access
2. Ensure your device has a working microphone
3. Restart the browser
4. Try a different browser

### Commands Not Recognized

**Issue:** Voice input not translating to action
**Solutions:**
1. Speak clearly and naturally
2. Avoid background noise
3. Use specific product names
4. Wait for system response before speaking again

### Connection Drops

**Issue:** "Network error" messages appearing frequently
**Solutions:**
1. Check internet connection speed
2. Move closer to WiFi router
3. Clear browser cache
4. Disable VPN if using one
5. Try a different browser or device

### Slow Response Times

**Issue:** Commands take a long time to process
**Solutions:**
1. Check network bandwidth
2. Verify server status
3. Reduce background processes
4. Try with fewer simultaneous operations
5. Check browser console for errors

### Database Errors

**Issue:** "Database error" messages
**Solutions:**
1. Verify SQLite database is initialized
2. Check browser storage permissions
3. Clear IndexedDB data if corrupted
4. Refresh the page to reinitialize
5. Check browser DevTools for detailed errors

## Debugging

### Enable Debug Logging

The voice system includes audit logging for debugging:

```typescript
import { auditLogger, AuditEventType } from '@/lib/voice-audit-logger';

// View audit logs in browser console
auditLogger.log(
  AuditEventType.TOOL_REQUESTED,
  AuditSeverity.INFO,
  'Custom message',
  { customData: true }
);
```

### Check Browser Console

Open DevTools (F12 or Right-click → Inspect) and check:
1. Console tab for error messages
2. Network tab for API requests
3. Application tab for IndexedDB/localStorage
4. Performance tab for latency issues

### Common Error Types

- **NETWORK**: Connection or server communication failure
- **VALIDATION**: Input validation failure
- **DATABASE**: Database operation failure
- **TIMEOUT**: Operation exceeded time limit
- **UNKNOWN**: Unexpected error

## Performance Optimization

### Network Optimization

```typescript
// Audio codec optimization (configured automatically)
- Opus codec for speech
- 16kHz sample rate
- Adaptive bitrate (12-20 kbps)
```

### Browser Optimization

```typescript
// Recommended settings
- Enable hardware acceleration
- Use latest browser version
- Close unnecessary tabs
- Disable browser extensions
```

### Device Optimization

```typescript
// For best performance
- Use wired internet (when possible)
- Minimize background applications
- Use quality microphone
- Ensure sufficient RAM (2GB+)
```

## Security & Privacy

### Data Handling

- All voice data is processed in real-time
- Audio is not stored on client
- Commands are logged for audit trail only
- Database operations include user attribution

### Microphone Permissions

- Microphone access requires explicit user consent
- Permissions are managed by browser
- Users can revoke access anytime
- No data is collected without permission

### Network Security

- All communications use HTTPS/WSS (secure protocols)
- API keys are kept in environment variables
- No sensitive data is logged
- Audit trails include appropriate filtering

## Future Enhancements

Planned improvements:

1. **Multi-language Support**: Spanish, French, German, etc.
2. **Custom Voice Commands**: Define personalized commands
3. **Voice Analytics**: Usage patterns and optimization
4. **Offline Mode**: Limited functionality without internet
5. **Voice Profiles**: Different user voice recognition
6. **Natural Language Generation**: More conversational responses
7. **Command Macros**: Chain multiple commands
8. **Voice Shortcuts**: Frequently used command shortcuts

## Support & Resources

### Getting Help

1. Check this documentation first
2. Review error messages and troubleshooting section
3. Check browser console for detailed error logs
4. Contact support with:
   - Error message
   - Command spoken
   - Browser/device info
   - Recent activity logs

### Documentation Links

- Main README: [README.md](../README.md)
- Voice Tool Executor: [src/lib/voice-tool-executor.ts](../src/lib/voice-tool-executor.ts)
- Voice Error Handler: [src/lib/voice-error-handler.ts](../src/lib/voice-error-handler.ts)
- Voice Audit Logger: [src/lib/voice-audit-logger.ts](../src/lib/voice-audit-logger.ts)

### Quick Links

- Voice Agent Component: [src/components/VoiceAgent.tsx](../src/components/VoiceAgent.tsx)
- Voice Tool Feedback: [src/components/VoiceToolFeedback.tsx](../src/components/VoiceToolFeedback.tsx)
- SQLite Service: [src/lib/sqlite-service.ts](../src/lib/sqlite-service.ts)

## Changelog

### Version 1.0.0 (Current)

**Features:**
- Real-time voice input via OpenAI Realtime API
- Tool calling for inventory and compliance operations
- Comprehensive error handling and validation
- Audit logging for compliance tracking
- Rich UI feedback with VoiceToolFeedback component
- 74 comprehensive integration tests

**Supported Tools:**
- add_inventory_event
- update_product_location
- check_product_status
- get_haccp_events
- record_ccp_monitoring

**Improvements:**
- WebRTC for low-latency audio
- Automatic error recovery
- Session tracking
- Connection quality monitoring
- Type-safe interfaces

---

**Last Updated:** October 24, 2025
**Version:** 1.0.0
**Status:** Production Ready
