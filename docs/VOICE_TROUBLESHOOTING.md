# Voice Integration Troubleshooting Guide

## Connection Error Quick Reference

The voice agent implements intelligent error classification with specific solutions for each error type. When a connection error occurs, you'll see a detailed error message with suggestions.

### Error Type: API_KEY_MISSING

**Error Message:**
```
Configuration Error
OpenAI API key not configured
```

**Cause:** No API key found in environment variables, or placeholder key detected.

**Solutions:**
1. Add `VITE_OPENAI_API_KEY` to your `.env` file
2. Get your API key from [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
3. Restart the development server after adding the key
4. Verify `.env` file is in project root (not `.env.example`)

**Quick Fix:**
```bash
# Create .env file
cp .env.example .env

# Edit .env and replace placeholder
VITE_OPENAI_API_KEY=sk-your-actual-key-here

# Restart server
npm run dev
```

### Error Type: API_KEY_INVALID

**Error Message:**
```
Authentication Failed
Invalid OpenAI API key
```

**Cause:** API key is incorrect, expired, or revoked.

**Solutions:**
1. Verify your API key is correct in `.env` file
2. Check that the key hasn't been revoked or expired in OpenAI dashboard
3. Ensure you have access to GPT-4 Realtime API (requires paid account)
4. Regenerate API key if necessary

**Quick Fix:**
```bash
# Check your key (should start with sk-proj- or sk-)
cat .env | grep VITE_OPENAI_API_KEY

# If incorrect, update .env with new key
# Then restart server
```

### Error Type: NETWORK_OFFLINE

**Error Message:**
```
Network Offline
No internet connection detected
```

**Cause:** Device has no internet connection.

**Solutions:**
1. Check your internet connection
2. Verify WiFi or ethernet is connected
3. Connection will retry automatically when network is available
4. Check network icon in system tray

**Quick Fix:**
```bash
# Test internet connectivity
ping *******

# Test OpenAI API endpoint
ping api.openai.com

# If both fail, check your network connection
```

### Error Type: NETWORK_ERROR

**Error Message:**
```
Connection Error
Retrying in Xs (attempt Y/5)
```

**Cause:** Temporary network interruption or unstable connection.

**Features:**
- **Automatic retry** with exponential backoff
- **Retry schedule:** 1s, 2s, 4s, 8s, 16s
- **Max retries:** 5 attempts
- **Progress indicator:** Shows countdown to next retry

**Solutions:**
1. Check your internet connection stability
2. Verify firewall isn't blocking WebSocket connections
3. Try disabling VPN if active
4. Wait for automatic retry (no action needed)

**Manual Retry:**
- Click the microphone button after all retries exhausted
- Connection will restart from attempt 1

### Error Type: WEBSOCKET_FAILED

**Error Message:**
```
WebSocket Connection Failed
Unable to establish real-time connection
```

**Cause:** WebSocket connection to OpenAI cannot be established.

**Solutions:**
1. Check if `api.openai.com` is accessible
2. Verify network allows WebSocket connections
3. Check browser console for detailed error logs
4. Disable firewall/antivirus temporarily to test

**Debug Steps:**
```javascript
// Open browser console and test WebSocket
const ws = new WebSocket('wss://api.openai.com/v1/realtime?api_key=YOUR_KEY');
ws.onopen = () => console.log('✓ WebSocket works');
ws.onerror = (e) => console.error('✗ WebSocket failed:', e);
```

### Error Type: MICROPHONE_DENIED

**Error Message:**
```
Microphone Access Denied
Cannot access microphone
```

**Cause:** Browser doesn't have permission to access microphone.

**Solutions:**
1. Allow microphone access in browser settings
2. Check system microphone permissions
3. Reload the page and accept microphone prompt

**Browser-Specific:**

**Chrome/Edge:**
```
1. Click padlock icon (🔒) in address bar
2. Find "Microphone" → Change to "Allow"
3. Refresh page (Ctrl+R)
```

**Firefox:**
```
1. Click padlock → More Information
2. Permissions tab → Microphone
3. Select "Allow" → Refresh page
```

**Safari:**
```
1. Safari → Settings → Websites
2. Microphone → Find localhost
3. Change to "Allow" → Refresh page
```

### Error Type: MAX_RETRIES_REACHED

**Error Message:**
```
Connection Failed
Maximum reconnection attempts reached
```

**Cause:** Failed to connect after 5 automatic retry attempts.

**Solutions:**
1. Click the microphone button to try again
2. Check your API key and internet connection
3. View browser console for detailed error logs
4. Contact support if issue persists

**What Happened:**
- Attempted 5 reconnections with exponential backoff
- Total retry time: ~31 seconds (1s + 2s + 4s + 8s + 16s)
- All attempts failed

**Next Steps:**
1. Verify internet is stable
2. Check API key is valid
3. Try from different browser
4. Wait 1-2 minutes then retry

## Connection Quality Indicators

The voice agent monitors connection quality in real-time:

| Quality | Latency | Indicator Color | Action |
|---------|---------|----------------|--------|
| Excellent | <50ms | 🟢 Green | Optimal performance |
| Good | 50-100ms | 🟡 Yellow | Slight delay, acceptable |
| Fair | 100-200ms | 🟠 Orange | Noticeable delay |
| Poor | >200ms | 🔴 Red | Consider reconnecting |

**Quality displayed in:**
- Connection toast notification: "Connected (excellent connection)"
- Console logs for monitoring

## Retry Behavior

### Exponential Backoff Schedule

| Attempt | Delay | Cumulative Time |
|---------|-------|-----------------|
| 1 | 1 second | 1s |
| 2 | 2 seconds | 3s |
| 3 | 4 seconds | 7s |
| 4 | 8 seconds | 15s |
| 5 | 16 seconds | 31s |
| Max | 30 seconds | Capped |

**Features:**
- Automatic retry on connection failure
- Smart retry: skips retry if network is offline
- Error count tracking: stops retry if too many errors (>10)
- User feedback: toast shows countdown to next attempt

### When Retry Stops

Automatic retry stops when:
1. **Max retries reached** (5 attempts)
2. **Network offline** (waits for network to return)
3. **Too many errors** (>10 errors in session)
4. **Manual stop** (user closes connection)

## Quick Diagnostics

### Step 1: Check Browser Console

Open Developer Tools (F12 or Right-click → Inspect) and check the Console tab:

```javascript
// Check if voice service is loaded
console.log(VoiceAgent);  // Should be defined

// Check browser capabilities
console.log(navigator.mediaDevices);  // Should have getUserMedia
console.log(window.WebSocket);        // Should be defined
console.log(window.AudioContext || window.webkitAudioContext);
```

### Step 2: Verify Permissions

Check if microphone access is allowed:

1. Look for microphone icon in address bar
2. Click it to see permissions
3. Ensure microphone is not blocked
4. Reset permissions if needed

### Step 3: Check Network Status

```bash
# Test connectivity
ping api.openai.com

# Check network in DevTools
# Open Network tab and try voice command
# Look for WebSocket or HTTP errors
```

## Common Issues & Solutions

### Issue 1: Microphone Not Detected

**Symptoms:**
- Voice input button is disabled or grayed out
- Error: "No microphone detected"
- Audio input slider shows no activity

**Possible Causes:**
1. Browser doesn't have microphone permission
2. No microphone connected/enabled on device
3. Microphone in use by another application
4. Browser version too old

**Solutions:**

```bash
# Solution 1: Grant microphone permission
1. Click address bar microphone icon
2. Select "Always allow"
3. Refresh page

# Solution 2: Check device settings
macOS:
  System Settings → Privacy & Security → Microphone
  Make sure browser is in the list

Windows:
  Settings → Privacy & Security → Microphone
  Toggle on and allow browser access

Linux:
  Check with: pulseaudio-utils / pactl

# Solution 3: Test microphone in another app
  Record audio with system recorder
  If it fails, microphone is broken

# Solution 4: Update browser
  Use Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
```

### Issue 2: Voice Input Recognized But No Action

**Symptoms:**
- Microphone working
- Waveform shows audio being recorded
- No response or error appears

**Possible Causes:**
1. WebRTC connection not established
2. OpenAI API key not configured
3. Network latency too high
4. Speech not clear enough

**Solutions:**

```typescript
// Check WebSocket connection
const ws = new WebSocket('wss://api.openai.com/...');
ws.addEventListener('open', () => console.log('Connected'));
ws.addEventListener('error', (e) => console.error('Connection error:', e));

// Check API key
console.log(import.meta.env.VITE_OPENAI_API_KEY ? 'Set' : 'Not set');

// Test with clearer speech
// Speak slowly and clearly
// Avoid background noise
// Use standard English pronunciation

// Check network latency
console.time('API Response');
// ... API call ...
console.timeEnd('API Response');
// Should be < 5000ms
```

### Issue 3: Commands Partially Recognized

**Symptoms:**
- "Add 20 units of salmon" → Recognized as "Add 20 units"
- Product names not recognized
- Numbers misheard

**Possible Causes:**
1. Background noise interfering
2. Speaking too fast or unclear
3. Accent or dialect differences
4. Microphone quality issues

**Solutions:**

```bash
# Solution 1: Reduce background noise
- Move to quieter location
- Close windows/doors
- Stop other audio playback
- Use headset with noise cancellation

# Solution 2: Speak more clearly
- Speak slowly and deliberately
- Separate words more distinctly
- Use standard English pronunciation
- Spell out unusual product names

# Solution 3: Use full command structure
Unclear: "Add salmon"
Clear: "Add 20 units of salmon to Freezer A as a receipt"

# Solution 4: Use phonetic alphabet for names
Instead: "Smith"
Try: "S-M-I-T-H" or "Sierra-Mike-India-Tango-Hotel"

# Solution 5: Upgrade microphone
- Use headset microphone
- USB condenser microphone
- External microphone for better clarity
```

### Issue 4: Validation Errors

**Symptoms:**
- Error: "Missing required field: product_id"
- Error: "Invalid quantity: must be positive"
- Error: "Invalid event_type"

**Possible Causes:**
1. Command doesn't include all required information
2. Wrong data type used
3. Enum value not recognized

**Solutions:**

```bash
# Validation Error: Missing product_id
Incorrect: "Add 20 units to Freezer A"
Correct:   "Add 20 units of salmon to Freezer A"

# Validation Error: Negative quantity
Incorrect: "Remove 5 units" → May be interpreted as negative
Correct:   "Adjust inventory by minus 5" or "Record 5 units usage"

# Validation Error: Invalid event_type
Incorrect: "Add a sample of fish"
Correct:   "Add 20 units of fish as a receipt"
          "Record 5 units of fish as usage"
          "Adjust inventory for fish by 10"

# Valid event types:
- receipt   → Items received/delivered
- usage     → Items used/consumed
- adjustment → Inventory correction
```

### Issue 5: Database Connection Error

**Symptoms:**
- Error: "Database connection failed"
- Error: "Database operation timed out"
- Data not saving

**Possible Causes:**
1. SQLite database not initialized
2. Browser storage quota exceeded
3. IndexedDB corrupted
4. Network issue affecting sync

**Solutions:**

```typescript
// Solution 1: Reinitialize database
// Hard refresh page
Ctrl+Shift+R (Windows/Linux)
Cmd+Shift+R (macOS)

// Solution 2: Clear browser storage
DevTools → Application → Storage
1. IndexedDB → Delete all
2. Local Storage → Clear all
3. Session Storage → Clear all
4. Refresh page to reinitialize

// Solution 3: Check storage quota
console.log(navigator.storage.estimate());
// Should show plenty of available space

// Solution 4: Check database logs
// Open Console in DevTools
// Look for SQLite initialization logs
```

### Issue 6: Network Connection Error

**Symptoms:**
- Error: "Failed to connect to server"
- Error: "Network unreachable"
- WebSocket connection fails

**Possible Causes:**
1. Internet connection down
2. VPN or proxy blocking connection
3. Firewall blocking WebSocket
4. Server temporarily unavailable

**Solutions:**

```bash
# Solution 1: Check internet connection
ping *******
curl https://api.openai.com/v1/models

# Solution 2: Disable VPN/Proxy
- Turn off VPN temporarily
- Check proxy settings
- Try from different network

# Solution 3: Check firewall rules
Windows: Check Windows Defender
macOS: System Settings → Security & Privacy
Linux: sudo ufw status

# Solution 4: Wait for server recovery
# If OpenAI API is down, wait 5-10 minutes
# Check status at status.openai.com

# Solution 5: Try different browser
# Some browsers may have connection issues
# Try Chrome, Firefox, Safari, Edge
```

### Issue 7: Slow Response Times

**Symptoms:**
- 5+ second delay before response
- Commands seem to hang
- UI feels unresponsive

**Possible Causes:**
1. Network latency
2. Server overload
3. Browser performance issues
4. System resource constraints

**Solutions:**

```bash
# Solution 1: Check network speed
# Open speedtest.net in browser
# Look for:
- Download > 10 Mbps
- Upload > 5 Mbps
- Ping < 50ms

# Solution 2: Reduce browser load
- Close unnecessary tabs
- Disable extensions
- Clear browser cache

# Solution 3: Check system resources
Windows:
  Task Manager → Performance
  Look for high CPU/Memory usage

macOS:
  Activity Monitor
  Check CPU/Memory tabs

# Solution 4: Optimize network
- Connect via ethernet (if possible)
- Closer to WiFi router
- Reduce connected devices
- Close bandwidth-heavy apps

# Solution 5: Use different server region
# If available, select nearest region
# Or contact support for regional info
```

### Issue 8: Permission Denied Errors

**Symptoms:**
- Error: "Permission denied"
- Error: "Not authorized"
- API key validation fails

**Possible Causes:**
1. API key not set or incorrect
2. API key doesn't have required permissions
3. CORS policy blocking request
4. API key quota exceeded

**Solutions:**

```bash
# Solution 1: Verify API key
# Check .env file has correct key
VITE_OPENAI_API_KEY=sk-...

# Solution 2: Check API key permissions
# Log into OpenAI account
# Verify API key has:
- read permissions for models
- write permissions for completions

# Solution 3: Check CORS headers
# Open Network tab in DevTools
# Click on API request
# Check Response Headers for CORS headers

# Solution 4: Check API quota
# OpenAI dashboard → Usage
# Verify usage is under quota
# Check for overage charges

# Solution 5: Regenerate API key
# If key seems compromised
1. OpenAI dashboard → API keys
2. Delete old key
3. Create new key
4. Update .env file
```

### Issue 9: Tool Execution Errors

**Symptoms:**
- Tool appears to run but returns error
- Error: "Tool execution timeout"
- Error: "Tool not found"

**Possible Causes:**
1. Tool doesn't exist or name misspelled
2. Arguments in wrong format
3. Database operation failed
4. Timeout exceeded

**Solutions:**

```typescript
// Check valid tool names
const validTools = [
  'add_inventory_event',
  'update_product_location',
  'check_product_status',
  'record_ccp_monitoring',
  'get_haccp_events'
];

// Verify tool arguments
// add_inventory_event requires:
// - product_id (string)
// - quantity (number)
// - event_type (receipt|usage|adjustment)
// - location (string)

// If timeout: try simpler command
Incorrect: "Add 100 units with detailed notes"
Correct:   "Add 100 units to Freezer A"

// Check tool logs
console.log('Tool execution logs');
// Look for detailed error messages
```

### Issue 10: UI Feedback Not Appearing

**Symptoms:**
- VoiceToolFeedback component doesn't show
- Loading indicator missing
- Success/error messages not displayed

**Possible Causes:**
1. Component not mounted
2. CSS not loading
3. State not updating
4. Events not firing

**Solutions:**

```typescript
// Solution 1: Check component is rendered
// Open DevTools → Elements tab
// Search for "VoiceToolFeedback"

// Solution 2: Check CSS is loaded
// Open DevTools → Styles tab
// Look for feedback component styles

// Solution 3: Check component state
// Use React DevTools Extension
// Verify toolFeedback state is updating

// Solution 4: Check event listeners
console.log('Listening for tool execution');
// Verify events are being emitted

// Solution 5: Check browser console for errors
// Look for red error messages
// Check for warnings
```

## Debug Mode

### Enable Verbose Logging

```typescript
// In src/lib/voice-audit-logger.ts
const DEBUG = true;

if (DEBUG) {
  console.log('[Voice Debug]', message, data);
}
```

### Check Audit Logs

```typescript
import { auditLogger } from '@/lib/voice-audit-logger';

// View recent logs
console.log(auditLogger.getLogs());

// Filter by event type
console.log(auditLogger.getLogs().filter(
  log => log.eventType === 'TOOL_EXECUTED'
));
```

### Network Debug

```typescript
// Monitor WebSocket messages
const ws = new WebSocket('...');

ws.addEventListener('message', (event) => {
  console.log('Received:', event.data);
});

ws.addEventListener('open', () => {
  console.log('Connected');
});

ws.addEventListener('error', (error) => {
  console.error('WebSocket error:', error);
});
```

## Browser DevTools Tips

### Console Debugging

```javascript
// Check service status
console.log('Voice service:', typeof VoiceAgent);
console.log('Database:', typeof sqliteService);
console.log('Tool executor:', typeof VoiceToolExecutor);

// Monitor API calls
window.__apiCalls = [];
// Then check window.__apiCalls

// Performance monitoring
performance.mark('command-start');
// ... execute command ...
performance.mark('command-end');
performance.measure('command', 'command-start', 'command-end');
```

### Network Tab

1. Open DevTools → Network tab
2. Filter by: WebSocket, XHR
3. Look for:
   - WebSocket connection to OpenAI
   - HTTP requests to API
   - Response codes (200, 400, 500)
   - Response times

### Storage Tab

```
IndexedDB → Safe-Catch-Flow
├── Products
├── Inventory
├── CCPMonitoring
├── HACCPEvents
└── TemperatureReadings
```

## Performance Profiling

```bash
# Chrome/Edge
1. Open DevTools
2. Go to Performance tab
3. Click record
4. Execute voice command
5. Stop recording
6. Analyze timeline

# Firefox
1. Open DevTools
2. Go to Performance tab
3. Click "Start profiling"
4. Execute voice command
5. Click "Capture recording"
6. Analyze results
```

## Contact Support

If you've tried all solutions, gather information:

1. **Error Message** - Exact error text
2. **Steps to Reproduce** - How to trigger issue
3. **Environment** - Browser, OS, version
4. **Logs** - Console output and audit logs
5. **Screenshots** - UI state when error occurs
6. **Network Info** - Connection type, speed
7. **Recent Activity** - What you were doing

Include this information when contacting support.

---

**Last Updated:** October 24, 2025
**Version:** 1.0.0
**Issues Covered:** 10 common issues + solutions
