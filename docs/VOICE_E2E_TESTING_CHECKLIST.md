# Voice Integration End-to-End Testing Checklist

Complete checklist for testing the voice integration with real OpenAI Realtime API.

## Pre-Testing Setup

### 1. Environment Verification

- [ ] `.env` file exists in project root
- [ ] `VITE_OPENAI_API_KEY` is set to valid API key (starts with `sk-`)
- [ ] `VITE_OPENAI_REALTIME_ENDPOINT` is set (or using default)
- [ ] OpenAI account has billing configured
- [ ] API key has not exceeded usage limits

**Verify:**
```bash
# Check environment variables
cat .env | grep VITE_OPENAI

# Should output:
# VITE_OPENAI_API_KEY=sk-proj-...
# VITE_OPENAI_REALTIME_ENDPOINT=wss://api.openai.com/v1/realtime
```

### 2. System Requirements

- [ ] Microphone connected and working
- [ ] Internet connection stable (5+ Mbps recommended)
- [ ] Modern browser (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- [ ] B<PERSON>er has microphone permission granted
- [ ] No VPN or proxy blocking WebSocket connections

**Test Microphone:**
- Visit [https://www.onlinemictest.com/](https://www.onlinemictest.com/)
- Verify microphone input works

**Test Connection:**
```bash
# Test internet speed
speedtest-cli  # or visit speedtest.net

# Test API endpoint accessibility
ping api.openai.com
```

### 3. Start Development Server

```bash
# Install dependencies
npm install

# Start server
npm run dev

# Should show:
# Local: http://localhost:8080/
```

## Part 1: Connection Tests

### 1.1 Initial Connection

- [ ] Navigate to http://localhost:8080
- [ ] Click microphone button in VoiceAgent
- [ ] Browser requests microphone permission
- [ ] Grant microphone permission
- [ ] Toast notification shows "Connected"
- [ ] Connection quality indicator appears (excellent/good/fair/poor)
- [ ] No errors in browser console

**Expected Console Logs:**
```
WebRTC Audio Constraints Applied: {
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true,
  sampleRate: 24000,
  channelCount: 1,
  latency: 0.01
}
AudioRecorder started successfully with optimization config: {...}
WebSocket connected to OpenAI Realtime API {latency: 150, quality: 'good', sessionId: '...'}
Network monitoring started (3s interval)
```

### 1.2 Connection Quality

Test different network conditions:

**Excellent Connection (<50ms latency):**
- [ ] Connect via ethernet cable
- [ ] Click microphone button
- [ ] Verify toast shows "Connected (excellent connection)"
- [ ] Check console shows latency <50ms

**Good Connection (50-100ms):**
- [ ] Connect via WiFi (close to router)
- [ ] Click microphone button
- [ ] Verify toast shows "Connected (good connection)"
- [ ] Check console shows latency 50-100ms

**Fair Connection (100-200ms):**
- [ ] Connect via WiFi (far from router)
- [ ] Click microphone button
- [ ] Verify toast shows "Connected (fair connection)"
- [ ] Check console shows latency 100-200ms

**Poor Connection (>200ms):**
- [ ] Use mobile hotspot or slow WiFi
- [ ] Click microphone button
- [ ] Verify toast shows "Connected (poor connection)"
- [ ] Check console shows latency >200ms

### 1.3 Error Handling

**Missing API Key:**
- [ ] Remove API key from `.env` (set to empty)
- [ ] Restart dev server
- [ ] Click microphone button
- [ ] Verify error: "Configuration Error - OpenAI API key not configured"
- [ ] Verify suggestions displayed in toast
- [ ] Restore API key and restart

**Invalid API Key:**
- [ ] Set API key to invalid value (e.g., `sk-invalid-key`)
- [ ] Restart dev server
- [ ] Click microphone button
- [ ] Verify error: "Authentication Failed - Invalid OpenAI API key"
- [ ] Verify suggestions displayed
- [ ] Restore valid API key

**Network Offline:**
- [ ] Disconnect from internet (turn off WiFi)
- [ ] Click microphone button
- [ ] Verify error: "Network Offline - No internet connection detected"
- [ ] Reconnect to internet
- [ ] Verify connection retries automatically

**Connection Interruption:**
- [ ] Connect successfully
- [ ] Disconnect internet briefly (5-10 seconds)
- [ ] Reconnect internet
- [ ] Verify automatic reconnection with exponential backoff
- [ ] Check retry attempts in console (1s, 2s, 4s, 8s, 16s)

### 1.4 Retry Logic

- [ ] Start with invalid API key
- [ ] Click microphone button
- [ ] Observe 5 retry attempts with exponential backoff
- [ ] Verify retry delays: 1s → 2s → 4s → 8s → 16s
- [ ] After 5 attempts, verify "Maximum reconnection attempts reached"
- [ ] Fix API key
- [ ] Click microphone button again
- [ ] Verify connection succeeds

## Part 2: Voice Command Tests

### 2.1 Basic Voice Recognition

**Test 1: Simple Greeting**
- [ ] Say: "Hello, can you hear me?"
- [ ] Verify AI responds with greeting
- [ ] Verify response audio plays
- [ ] Verify transcript appears in UI (if visible)
- [ ] No errors in console

**Test 2: Information Query**
- [ ] Say: "What can you help me with?"
- [ ] Verify AI describes capabilities
- [ ] Verify response audio quality is clear
- [ ] Verify reasonable response time (<5 seconds)

**Test 3: Unclear Speech**
- [ ] Mumble or speak very quietly
- [ ] Verify AI asks for clarification
- [ ] Speak clearly and repeat
- [ ] Verify AI understands

### 2.2 Tool Execution - Inventory Operations

**Test 1: Add Inventory (Receipt)**
- [ ] Say: "Add 20 units of salmon to Freezer A as a receipt"
- [ ] Verify VoiceToolFeedback component appears
- [ ] Verify loading indicator shows while executing
- [ ] Verify tool call displays: `add_inventory_event`
- [ ] Verify arguments shown correctly:
  ```json
  {
    "product_id": "salmon",
    "quantity": 20,
    "location": "Freezer A",
    "event_type": "receipt"
  }
  ```
- [ ] Verify success message appears
- [ ] Verify AI confirms action verbally
- [ ] Check database: record added to staged inventory

**Test 2: Check Product Status**
- [ ] Say: "What is the status of salmon?"
- [ ] Verify tool execution: `check_product_status`
- [ ] Verify product information returned
- [ ] Verify AI reads status verbally
- [ ] Verify feedback UI shows result

**Test 3: Update Location**
- [ ] Say: "Move salmon from Freezer A to Cooler B"
- [ ] Verify tool execution: `update_product_location`
- [ ] Verify location change confirmed
- [ ] Verify AI confirms move verbally
- [ ] Check database: location updated

**Test 4: Usage Event**
- [ ] Say: "Record 5 units of salmon as usage"
- [ ] Verify tool execution: `add_inventory_event`
- [ ] Verify event_type is "usage"
- [ ] Verify quantity is 5
- [ ] Verify AI confirms usage recorded
- [ ] Check database: usage event added

**Test 5: Adjustment Event**
- [ ] Say: "Adjust inventory for salmon by minus 3"
- [ ] Verify tool execution: `add_inventory_event`
- [ ] Verify event_type is "adjustment"
- [ ] Verify quantity is -3
- [ ] Verify AI confirms adjustment
- [ ] Check database: adjustment recorded

### 2.3 Tool Execution - CCP Monitoring

**Test 1: Record Temperature**
- [ ] Say: "Record CCP temperature check for Freezer A at 38 degrees"
- [ ] Verify tool execution: `record_ccp_monitoring`
- [ ] Verify parameters:
  ```json
  {
    "location": "Freezer A",
    "temperature": 38,
    "control_measure": "temperature_check"
  }
  ```
- [ ] Verify AI confirms recording
- [ ] Check database: CCP monitoring record added

**Test 2: Temperature Out of Range**
- [ ] Say: "Record temperature for Cooler B at 50 degrees"
- [ ] Verify tool execution succeeds
- [ ] Verify AI mentions temperature is high (if programmed)
- [ ] Verify record saved with out-of-range value

### 2.4 Tool Execution - HACCP Events

**Test 1: Get Recent Events**
- [ ] Say: "Show me recent HACCP events"
- [ ] Verify tool execution: `get_haccp_events`
- [ ] Verify events retrieved from database
- [ ] Verify AI summarizes events verbally
- [ ] Verify feedback shows event list

**Test 2: Query Specific Event Type**
- [ ] Say: "Show me all receipt events"
- [ ] Verify tool filters by event_type
- [ ] Verify only receipt events returned
- [ ] Verify AI reads filtered results

## Part 3: Audio Quality Tests

### 3.1 Input Audio Quality

**Test with Different Environments:**
- [ ] **Quiet room**: Say command, verify clear recognition
- [ ] **Background noise**: Turn on music, say command
  - Verify noise suppression works (somewhat)
  - Speech should still be recognized if clear
- [ ] **Multiple speakers**: Have someone talk nearby
  - Verify primary speaker is focused on
- [ ] **Distant microphone**: Step back from mic
  - Verify auto-gain control compensates
  - Speech should still be audible

**Test with Different Speech Patterns:**
- [ ] **Normal pace**: Standard speaking speed
- [ ] **Slow speech**: Speak very slowly
- [ ] **Fast speech**: Speak quickly
- [ ] **Accented speech**: Test with different accents
- [ ] **Complex product names**: "Add quinoa to storage"

### 3.2 Output Audio Quality

- [ ] Verify AI voice is clear and natural
- [ ] Verify no audio glitches or stuttering
- [ ] Verify appropriate volume level
- [ ] Verify no echo or feedback
- [ ] Verify audio playback completes fully (no cutoff)

### 3.3 Adaptive Bitrate

**Test Network Adaptation:**
- [ ] Start with good connection
- [ ] Check console for audio optimization logs
- [ ] Verify bitrate is at target (48kbps)
- [ ] Throttle connection (browser DevTools → Network → Slow 3G)
- [ ] Verify bitrate reduces automatically
- [ ] Check console: `Adaptive bitrate: <lower-value> bps`
- [ ] Restore connection
- [ ] Verify bitrate increases back to target

## Part 4: Performance Tests

### 4.1 Latency Measurement

- [ ] Say command: "Add 10 units of tuna"
- [ ] Measure time from end of speech to tool execution
- [ ] **Target:** <2 seconds for simple commands
- [ ] **Acceptable:** <5 seconds for complex commands
- [ ] **Warning:** >5 seconds may indicate network issues

**Check Console Logs:**
```
Network Monitoring Update: {
  avgLatency: '150.00ms',
  packetLoss: '0.0%',
  bandwidth: '2000kbps',
  quality: 'good'
}
```

### 4.2 Jitter Buffer Performance

- [ ] Start voice session
- [ ] Speak continuously for 30 seconds
- [ ] Check console for jitter buffer logs (10% sample rate)
```
Audio Optimization - Jitter Buffer: {
  bufferTimeMs: '40.00',
  targetBufferMs: 40,
  sendIntervalMs: 40,
  audioDataSize: 4096,
  networkLatency: 150,
  networkPacketLoss: 0
}
```
- [ ] Verify buffer time stays around 40ms target
- [ ] Verify send interval adapts to network conditions

### 4.3 Memory and CPU Usage

- [ ] Open browser Task Manager (Shift+Esc in Chrome)
- [ ] Start voice session
- [ ] Speak for 5 minutes continuously
- [ ] Monitor memory usage (should be stable, not growing)
- [ ] Monitor CPU usage (should be <50% for voice tab)
- [ ] Disconnect voice session
- [ ] Verify memory is released

## Part 5: Error Recovery Tests

### 5.1 Network Interruption Recovery

- [ ] Connect and say a command successfully
- [ ] Turn off WiFi for 5 seconds
- [ ] Turn WiFi back on
- [ ] Verify automatic reconnection
- [ ] Say another command
- [ ] Verify command executes successfully

### 5.2 Tool Execution Failure

- [ ] Say: "Add 999999 units of invalid-product"
- [ ] Verify tool execution fails gracefully
- [ ] Verify error message displayed in feedback UI
- [ ] Verify error toast appears
- [ ] Verify AI acknowledges error verbally
- [ ] Session should still be active
- [ ] Say another valid command
- [ ] Verify new command works

### 5.3 Concurrent Tool Execution

- [ ] Say: "Add 20 units of salmon AND check the status of tuna"
- [ ] Verify multiple tools may be called
- [ ] Verify both execute successfully
- [ ] Verify feedback shows both results
- [ ] Verify no race conditions or conflicts

## Part 6: Long-Running Session Tests

### 6.1 Extended Session

- [ ] Start voice session
- [ ] Execute 20 different commands over 15 minutes
- [ ] Verify connection remains stable
- [ ] Verify no memory leaks (check Task Manager)
- [ ] Verify audio quality doesn't degrade
- [ ] Verify response time stays consistent

### 6.2 Idle Behavior

- [ ] Connect voice session
- [ ] Don't speak for 5 minutes
- [ ] Say a command after idle period
- [ ] Verify command still works
- [ ] Verify no timeout or disconnection (unless configured)

## Part 7: Cross-Browser Testing

Test in each major browser:

### Chrome
- [ ] All connection tests pass
- [ ] All voice commands work
- [ ] Audio quality is good
- [ ] Performance is acceptable

### Firefox
- [ ] All connection tests pass
- [ ] All voice commands work
- [ ] Audio quality is good
- [ ] Performance is acceptable

### Safari (macOS)
- [ ] All connection tests pass
- [ ] All voice commands work
- [ ] Audio quality is good
- [ ] Performance is acceptable

### Edge
- [ ] All connection tests pass
- [ ] All voice commands work
- [ ] Audio quality is good
- [ ] Performance is acceptable

## Part 8: Regression Testing

### 8.1 Verify Previous Fixes

From VOICE_FIX_PLAN.md, verify:

- [ ] **Fix 1**: Audio constraints properly applied (echoCancellation, etc.)
- [ ] **Fix 2**: Connection lifecycle managed correctly
- [ ] **Fix 3**: Error handling with user-friendly messages
- [ ] **Fix 4**: Network monitoring active and updating
- [ ] **Fix 5**: Exponential backoff retry logic working
- [ ] **Fix 6**: Session state tracked accurately
- [ ] **Fix 7**: Tool execution with proper feedback
- [ ] **Fix 8**: Audit logging for all events

### 8.2 Check Integration Tests

Run automated tests:

```bash
# Run all tests
npm run test:run

# Run specific test suites
npm run test:run src/test/audio-optimization.test.ts
npm run test:run src/test/voice-connection.test.ts

# Check coverage
npm run test:coverage
```

Expected results:
- [ ] All 53+ connection tests pass
- [ ] All audio optimization tests pass
- [ ] No test failures
- [ ] Coverage >90% for connection code

## Part 9: Production Readiness

### 9.1 Build and Deploy

```bash
# Build for production
npm run build

# Verify build succeeds
# Check dist/ directory exists

# Preview production build
npm run preview

# Test in preview mode
# Verify voice integration works identically
```

### 9.2 Environment Variables

- [ ] Production `.env` configured
- [ ] API key is production key (not test key)
- [ ] No sensitive data in client code
- [ ] Environment variables properly injected
- [ ] Build includes correct API endpoint

### 9.3 Monitoring Setup

- [ ] OpenAI usage dashboard monitored
- [ ] Cost alerts configured (e.g., >$100/month)
- [ ] Error logging configured
- [ ] Performance monitoring active
- [ ] User feedback collection enabled

## Testing Summary

**Total Tests:** 100+
**Required Pass Rate:** 95%+

**Critical Paths:**
1. Connection establishment
2. Error handling and recovery
3. Tool execution (inventory, CCP, HACCP)
4. Audio quality (input and output)
5. Network adaptation

**Sign-Off Checklist:**
- [ ] All critical path tests pass
- [ ] No blocking bugs found
- [ ] Performance meets requirements
- [ ] Documentation is complete
- [ ] Ready for user acceptance testing

---

**Test Date:** _______________
**Tester:** _______________
**Browser:** _______________
**OS:** _______________
**Pass Rate:** _____%
**Notes:**

---

**Last Updated:** October 24, 2025
**Version:** 1.0.0
