# Slash Commands Reference

Complete reference for all available slash commands in Safe Catch Flow.

## 🎯 Three-Phase Workflow Commands

### Phase 1: Exploration

#### `/primer [mode] [area]`

**Purpose:** Start vibe planning and exploration phase.

**Modes:**
- `analyze` - Analyze current codebase
- `existing [feature-area]` - Explore existing feature area
- `new-project [name]` - Research for new project

**Examples:**
```bash
/primer analyze
/primer existing supplier-management
/primer new-project inventory-tracking
```

**What it does:**
1. Checks Archon for existing projects/tasks
2. Analyzes codebase patterns
3. Searches knowledge base
4. Researches technologies
5. Provides exploration summary with recommendations

**Output:**
- Codebase analysis findings
- Research insights
- Recommended approach
- Open questions
- Next steps

**When to use:**
- Understanding new codebases
- Exploring complex features
- Researching technologies
- Making architecture decisions

---

### Phase 2A: Planning

#### `/create-plan [INITIAL.md]`

**Purpose:** Generate comprehensive implementation plan from requirements.

**Usage:**
```bash
/create-plan INITIAL.md
/create-plan features/auth/INITIAL.md
```

**Prerequisites:**
- ✅ INITIAL.md exists with requirements
- ✅ Archon MCP server available

**What it does:**
1. Parses INITIAL.md requirements
2. Creates/checks Archon project
3. Researches using knowledge base
4. Analyzes referenced examples
5. Generates implementation blueprint
6. Creates Archon tasks
7. Defines success criteria
8. Saves plan document

**Output:**
- `plans/[feature-name]-plan.md` with complete blueprint
- Archon project with tasks (todo status)
- Success criteria checklist
- Implementation guidance

**INITIAL.md Structure:**
```markdown
## FEATURE
[What to build]

## EXAMPLES
[Similar code files to reference]

## DOCUMENTATION
[External docs and APIs]

## OTHER CONSIDERATIONS
[Validation rules, constraints, requirements]
```

---

### Phase 2B: Execution

#### `/execute-plan [plan.md]`

**Purpose:** Systematically implement plan using task-driven development.

**Usage:**
```bash
/execute-plan plans/supplier-management-plan.md
/execute-plan PRPs/temperature-dashboard.md
```

**Prerequisites:**
- ✅ Plan document exists
- ✅ Archon project created with tasks

**What it does:**
Follows strict task-driven cycle:
1. **Get Task** → Retrieve next todo task from Archon
2. **Start Work** → Mark task as "doing"
3. **Research** → Search knowledge base (MANDATORY)
4. **Implement** → Write code following patterns
5. **Review** → Mark task as "review"
6. **Validate** → Run lint, build, manual tests
7. **Complete** → Mark task as "done" (only if validation passes)
8. **Next Task** → Repeat from step 1

**Validation Levels:**
- **Level 1:** `npm run lint` + `npm run build:dev`
- **Level 2:** Manual browser testing
- **Level 3:** Data integrity verification

**Critical Rules:**
- ✅ Research BEFORE coding (every task)
- ✅ Only ONE task "doing" at a time
- ✅ Validate BEFORE marking "done"
- ✅ Use Archon (NOT TodoWrite)
- ❌ NEVER skip research
- ❌ NEVER mark done with failing tests

**Output:**
- Fully implemented feature
- All tasks marked "done"
- All validation passing
- Success criteria met

---

## 📚 Legacy PRP Commands

### `/generate-prp [INITIAL.md]`

**Purpose:** Generate Product Requirements Prompt (legacy workflow).

**Usage:**
```bash
/generate-prp INITIAL.md
```

**What it does:**
- Analyzes codebase patterns
- Researches documentation
- Generates comprehensive PRP
- Saves to `PRPs/[feature-name].md`

**Note:** Maintained for backward compatibility. New projects should use `/create-plan`.

---

### `/execute-prp [PRP.md]`

**Purpose:** Execute PRP implementation (legacy workflow).

**Usage:**
```bash
/execute-prp PRPs/temperature-dashboard.md
```

**What it does:**
- Loads PRP context
- Creates TodoWrite task list
- Implements each component
- Runs validation loops
- Verifies success criteria

**Note:** Maintained for backward compatibility. New projects should use `/execute-plan` with Archon.

---

## 🔄 Workflow Comparison

### Recommended: Three-Phase Workflow

```bash
# 1. Explore (optional but recommended)
/primer existing [feature-area]

# 2. Create requirements
# [Edit INITIAL.md]

# 3. Generate plan
/create-plan INITIAL.md

# 4. Execute plan
/execute-plan plans/[feature-name]-plan.md
```

**Benefits:**
- ✅ Structured exploration phase
- ✅ Archon task management
- ✅ Integrated knowledge base search
- ✅ Three-level validation
- ✅ Sub-agent support

### Legacy: PRP Workflow

```bash
# 1. Create requirements
# [Edit INITIAL.md]

# 2. Generate PRP
/generate-prp INITIAL.md

# 3. Execute PRP
/execute-prp PRPs/[feature-name].md
```

**Benefits:**
- ✅ Simpler two-step process
- ✅ TodoWrite integration
- ✅ Still generates comprehensive plans

**Recommendation:** Use three-phase workflow for all new work.

---

## 🎓 Quick Start Examples

### Example 1: Simple Form

```bash
# Skip primer, go straight to planning
cat > INITIAL.md <<EOF
## FEATURE
Add temperature reading form

## EXAMPLES
- src/components/HaccpEventForm.tsx

## DOCUMENTATION
- https://react-hook-form.com/docs

## OTHER CONSIDERATIONS
- Temperature: 32-212°F
- Required: location, temperature, timestamp
EOF

/create-plan INITIAL.md
/execute-plan plans/temperature-reading-form-plan.md
```

### Example 2: Complex Feature with Exploration

```bash
# 1. Explore first
/primer existing supplier-management

# 2. Review findings, create INITIAL.md
cat > INITIAL.md <<EOF
## FEATURE
Complete supplier management system with deliveries

## EXAMPLES
- src/components/HaccpEventForm.tsx
- src/lib/sqlite-service.ts
- src/lib/sync-service.ts

## DOCUMENTATION
- React Hook Form: https://react-hook-form.com/docs
- SQLite: https://sql.js.org/documentation/

## OTHER CONSIDERATIONS
- Multiple forms (supplier profile, deliveries, contacts)
- Sync to Supabase
- Temperature tracking for deliveries
- Lot number tracking
- Quality checks
EOF

# 3. Create plan
/create-plan INITIAL.md

# 4. Execute
/execute-plan plans/supplier-management-plan.md
```

### Example 3: Bug Fix (Quick)

```bash
# Minimal INITIAL.md
cat > INITIAL.md <<EOF
## FEATURE
Fix temperature validation allowing negative values

## EXAMPLES
- src/components/HaccpEventForm.tsx (line 45)

## DOCUMENTATION
- Zod validation: https://zod.dev

## OTHER CONSIDERATIONS
- Add min(32) to Zod schema
- Update error message
- Test edge cases
EOF

/create-plan INITIAL.md
/execute-plan plans/fix-temperature-validation-plan.md
```

---

## 🛠️ Command Options & Flags

### `/primer`

**Modes:**
- `analyze` - Full codebase analysis
- `existing [area]` - Focused feature area
- `new-project [name]` - New project research

### `/create-plan`

**Arguments:**
- `[path]` - Path to INITIAL.md (required)

**Example Paths:**
- `INITIAL.md` (root)
- `features/auth/INITIAL.md`
- `bugs/temp-fix/INITIAL.md`

### `/execute-plan`

**Arguments:**
- `[path]` - Path to plan.md (required)

**Supported Paths:**
- `plans/[name]-plan.md` (from `/create-plan`)
- `PRPs/[name].md` (from `/generate-prp`)

---

## 📊 Success Criteria

### After `/primer`
- ✅ Codebase patterns identified
- ✅ Technologies researched
- ✅ Approach recommended
- ✅ Open questions documented

### After `/create-plan`
- ✅ Archon project created
- ✅ Tasks generated
- ✅ Plan document saved
- ✅ Data models defined
- ✅ Success criteria established

### After `/execute-plan`
- ✅ All tasks "done"
- ✅ Feature fully implemented
- ✅ All validation passing
- ✅ No TypeScript errors
- ✅ No ESLint warnings
- ✅ Manual tests passed
- ✅ Success criteria met

---

## 🔍 Troubleshooting

### Command Not Found

**Problem:** `/primer` or other commands not recognized

**Solution:**
1. Check `.claude/commands/` directory exists
2. Verify command files are present
3. Restart Claude Code session

### Archon Not Available

**Problem:** Commands fail with Archon errors

**Solution:**
1. Verify Archon MCP server is configured
2. Check `claude_desktop_config.json`
3. Restart Claude Code/Desktop

### Plan Execution Fails

**Problem:** `/execute-plan` fails validation

**Solution:**
1. Review error messages carefully
2. Check plan gotchas section
3. Fix issues and re-run validation
4. Don't mark task done until passing

### Tasks Not Created

**Problem:** `/create-plan` doesn't create Archon tasks

**Solution:**
1. Verify Archon MCP is available
2. Check for error messages
3. Manually verify with `find_projects()`

---

## 📚 Related Documentation

- **WORKFLOW.md** - Complete workflow guide
- **CLAUDE.md** - Project-specific patterns
- **INITIAL.md** - Requirements template
- **INITIAL_EXAMPLE.md** - Complete example
- **PRP_QUICK_START.md** - Legacy quick start
- **plans/README.md** - Plans directory guide

---

## 💡 Best Practices

### Planning
✅ DO:
- Use `/primer` for complex features
- Reference existing code extensively
- Include specific documentation URLs
- Define clear success criteria

❌ DON'T:
- Skip exploration for complex work
- Write vague requirements
- Omit validation rules
- Forget error handling

### Execution
✅ DO:
- Research before every task
- Follow task cycle strictly
- Validate after each task
- Fix errors immediately
- Use short RAG queries (2-5 keywords)

❌ DON'T:
- Skip research phase
- Work on multiple tasks simultaneously
- Mark done with failing tests
- Use TodoWrite (use Archon)
- Batch task completions

### General
✅ DO:
- Trust the process
- Follow the workflow
- Validate continuously
- Review generated plans

❌ DON'T:
- Rush through phases
- Skip validation
- Ignore gotchas
- Deploy with errors

---

## 🎉 Summary

The slash command system provides a structured, repeatable development process:

| Command | Phase | Purpose |
|---------|-------|---------|
| `/primer` | 1: Planning | Explore & research |
| `/create-plan` | 2A: Planning | Generate blueprint |
| `/execute-plan` | 2B: Implementation | Build feature |
| Validation | 3: Validation | Ensure quality |

**Key to success:** Follow the process, trust the system, validate continuously.

Happy building! 🚀
