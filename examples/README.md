# Code Pattern Examples

This directory contains example code patterns and implementations that can be referenced when creating PRPs (Product Requirements Prompts).

## Purpose

When defining a new feature in `INITIAL.md`, reference files in this directory to show:
- Existing patterns to follow
- Similar implementations
- Coding conventions
- Best practices specific to this project

## Current Project Patterns

### 📝 Forms & Validation

**Pattern File:** `src/components/HaccpEventForm.tsx`

Key patterns demonstrated:
- React Hook Form setup with Zod validation
- Form field structure with FormField components
- Select dropdowns for entity selection
- Number input handling
- Date/time inputs
- Toast notifications (success/error)
- Form reset after submission
- Async form submission with error handling

**Use when:**
- Creating any new form component
- Implementing form validation
- Adding user input functionality

### 🗄️ Database Operations

**Pattern File:** `src/lib/sqlite-service.ts`

Key patterns demonstrated:
- SQLite service class structure
- Database initialization
- Parameterized queries (SQL injection prevention)
- Insert/update/delete/select operations
- Error handling and logging
- Type-safe interfaces
- Staging workflow fields (synced_to_supabase, sync_confirmed)

**Use when:**
- Adding new database tables
- Creating CRUD operations
- Implementing data persistence

### 🔄 Data Synchronization

**Pattern File:** `src/lib/sync-service.ts`

Key patterns demonstrated:
- Syncing SQLite data to Supabase
- Filtering unsynced records
- Batch operations
- Error handling during sync
- Update sync status flags

**Use when:**
- Implementing Supabase synchronization
- Adding new entities to sync workflow
- Managing staging data

### 🎨 UI Components

**Pattern Directory:** `src/components/ui/`

Key patterns demonstrated:
- shadcn/ui component usage
- Consistent styling with Tailwind
- Accessible form components
- Button states and variants
- Dialog/modal patterns
- Card layouts

**Use when:**
- Building user interfaces
- Creating layouts
- Implementing interactive elements

### 📊 SQLite Schema

**Pattern File:** `public/sqlite-schema.sql`

Key patterns demonstrated:
- Table creation with constraints
- Primary keys (INTEGER AUTOINCREMENT)
- Foreign key relationships
- Default values (CURRENT_TIMESTAMP)
- Staging table pattern with sync fields
- Data type conventions (TEXT, INTEGER, REAL)

**Use when:**
- Creating new database tables
- Modifying existing schema
- Understanding data relationships

## Example Workflows

### Creating a New Form

**Reference these files:**
1. `src/components/HaccpEventForm.tsx` - Complete form pattern
2. `src/lib/sqlite-service.ts` - Database insert method
3. `src/components/ui/form.tsx` - Form components
4. `public/sqlite-schema.sql` - Table schema

**Pattern to follow:**
```typescript
// 1. Define Zod schema for validation
const formSchema = z.object({
  field1: z.string().min(1, 'Required'),
  field2: z.number().positive(),
});

// 2. Setup form with React Hook Form
const form = useForm<z.infer<typeof formSchema>>({
  resolver: zodResolver(formSchema),
  defaultValues: { /* ... */ },
});

// 3. Handle submission
async function onSubmit(values: z.infer<typeof formSchema>) {
  try {
    await sqliteService.insertEntity(values);
    toast.success('Success message');
    form.reset();
  } catch (error) {
    toast.error('Error message');
  }
}

// 4. Render form with FormFields
```

### Adding a New Database Entity

**Reference these files:**
1. `public/sqlite-schema.sql` - Table structure
2. `src/lib/sqlite-service.ts` - Service methods
3. `src/lib/sync-service.ts` - Sync implementation

**Pattern to follow:**
```sql
-- 1. Create table in sqlite-schema.sql
CREATE TABLE IF NOT EXISTS your_entity_staged (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  field1 TEXT NOT NULL,
  created_by TEXT NOT NULL,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  synced_to_supabase INTEGER DEFAULT 0,
  sync_confirmed INTEGER DEFAULT 0
);
```

```typescript
// 2. Add interface in sqlite-service.ts
interface YourEntity {
  id: number;
  field1: string;
  created_by: string;
  created_at: string;
  synced_to_supabase: number;
  sync_confirmed: number;
}

// 3. Add insert method
async insertYourEntity(data: YourEntityInsert): Promise<void> {
  if (!this.db) throw new Error('Database not initialized');

  try {
    const stmt = this.db.prepare(`
      INSERT INTO your_entity_staged (field1, created_by)
      VALUES (?, ?)
    `);
    stmt.run([data.field1, data.created_by]);
    stmt.free();
  } catch (error) {
    console.error('Error inserting entity:', error);
    throw error;
  }
}
```

## Best Practices

### When Referencing Examples in INITIAL.md

✅ **Good:**
```markdown
## EXAMPLES
- Form pattern: src/components/HaccpEventForm.tsx (Zod validation, React Hook Form setup)
- Database insert: src/lib/sqlite-service.ts (insertProduct method, parameterized queries)
- Select dropdown: HaccpEventForm.tsx (product selection with data loading)
```

❌ **Avoid:**
```markdown
## EXAMPLES
- Look at the forms
- Check database stuff
```

### Keeping Examples Current

- As you build new features, they become examples for future work
- Update this README when new patterns emerge
- Reference the most recent, best implementation of each pattern
- Document any gotchas or lessons learned

## Common Patterns Quick Reference

| Pattern | File | Key Concept |
|---------|------|-------------|
| Form with validation | `HaccpEventForm.tsx` | React Hook Form + Zod |
| Database insert | `sqlite-service.ts` | Parameterized queries, error handling |
| Data sync | `sync-service.ts` | Batch sync, status tracking |
| UI components | `components/ui/*` | shadcn/ui patterns |
| Schema design | `sqlite-schema.sql` | Staging tables, constraints |
| Select dropdown | `HaccpEventForm.tsx` | Dynamic options from database |
| Toast notifications | `HaccpEventForm.tsx` | Success/error feedback |
| Form reset | `HaccpEventForm.tsx` | Clear form after submit |

## Adding New Examples

As the codebase grows:

1. **Identify reusable patterns** in new features
2. **Document the pattern** in this README
3. **Reference the implementation** file path
4. **Describe key concepts** demonstrated
5. **Update the Quick Reference** table

This makes future PRPs more effective by building a library of proven patterns.
