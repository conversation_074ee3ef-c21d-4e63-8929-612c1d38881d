# Voice Agent Connection Timeout Fix

**Date:** October 25, 2025
**Task:** Investigate realtime agent not responding
**Status:** Fixed - Ready for Testing

---

## 🔍 Issue Identified

**Problem:** OpenAI Realtime API connection was timing out after 30 seconds during the WebRTC handshake.

**Root Cause:** The OpenAI Agents SDK requires microphone access for WebRTC audio input, but the code was attempting to connect WITHOUT requesting microphone permissions first. This caused the SDK to hang waiting for microphone access, leading to a timeout.

---

## ✅ Changes Made

### 1. **Pre-Connection Microphone Permission Request**
```typescript
// BEFORE: Attempted to connect without requesting mic access
const ephemeralKey = await getEphemeralKey();
sessionRef.current = new RealtimeSession(agentRef.current, {
  model: 'gpt-realtime',
});
await sessionRef.current.connect({ apiKey: ephemeralKey });

// AFTER: Request mic permissions BEFORE connecting
// Request microphone permissions BEFORE connecting
console.log('🎤 Requesting microphone permissions...');
try {
  const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
  console.log('✅ Microphone access granted');
  // Stop the test stream - <PERSON><PERSON> will request its own
  stream.getTracks().forEach(track => track.stop());
} catch (micError) {
  console.error('❌ Microphone permission denied:', micError);
  throw new Error('Microphone access is required for voice chat. Please allow microphone access in your browser settings.');
}

// Then get ephemeral key and connect
const ephemeralKey = await getEphemeralKey();
sessionRef.current = new RealtimeSession(agentRef.current, {
  model: 'gpt-4o-realtime-preview',
  transport: 'webrtc',
});
await sessionRef.current.connect({ apiKey: ephemeralKey });
```

### 2. **Updated Model Name**
- Changed from `'gpt-realtime'` to `'gpt-4o-realtime-preview'`
- This is a more stable model identifier

### 3. **Explicit Transport Specification**
```typescript
sessionRef.current = new RealtimeSession(agentRef.current, {
  model: 'gpt-4o-realtime-preview',
  transport: 'webrtc', // Explicitly specify WebRTC transport
});
```

### 4. **Enhanced Error Handling**
- Added specific error detection for microphone permission denials
- Improved error messages with actionable suggestions
- Extended toast duration for error messages (10 seconds)

### 5. **Better Console Logging**
```typescript
console.log('🎤 Requesting microphone permissions...');
console.log('🔑 Getting ephemeral key...');
console.log('🤖 Creating agent and session...');
console.log('🔌 Connecting to OpenAI Realtime API via WebRTC...');
console.log('⏱️ Connection timeout set to 30 seconds');
```

---

## 🧪 Testing Instructions

### Prerequisites
1. Backend server must be running:
   ```bash
   npm run dev:server
   # Should see: Server running on http://localhost:3001
   # Should see: OpenAI API Key: ✅ Set
   ```

2. Frontend dev server must be running:
   ```bash
   npm run dev
   # Navigate to http://localhost:8080
   ```

### Test Cases

#### ✅ Test 1: Successful Connection (Happy Path)
1. Click "Start Voice Chat" button
2. **Expect:** Browser prompts for microphone permission
3. **Action:** Click "Allow"
4. **Expect:** Console logs show:
   - 🎤 Requesting microphone permissions...
   - ✅ Microphone access granted
   - 🔑 Getting ephemeral key...
   - ✅ Ephemeral key obtained
   - 🤖 Creating agent and session...
   - 🔌 Connecting to OpenAI Realtime API via WebRTC...
   - ✅ Connected to OpenAI Realtime API via WebRTC
5. **Expect:** Toast notification "Voice agent ready. Start speaking!"
6. **Expect:** UI shows "Listening" indicator
7. **Action:** Speak into microphone (e.g., "Hello, can you hear me?")
8. **Expect:** AI responds with audio and transcript appears in chat

#### ❌ Test 2: Microphone Permission Denied
1. Click "Start Voice Chat" button
2. **Expect:** Browser prompts for microphone permission
3. **Action:** Click "Block" or "Deny"
4. **Expect:** Console logs show:
   - 🎤 Requesting microphone permissions...
   - ❌ Microphone permission denied
5. **Expect:** Toast error with title "Microphone Access Denied"
6. **Expect:** Error message includes suggestions to allow microphone access

#### ⏱️ Test 3: Network Connection Timeout
1. Disable network or set up network delay (e.g., throttle to Offline in DevTools)
2. Click "Start Voice Chat" button
3. **Expect:** After 30 seconds, connection times out
4. **Expect:** Toast error with title "Connection Error"
5. **Expect:** Error message includes network troubleshooting suggestions

#### 🔧 Test 4: Tool Execution
1. Successfully connect to voice agent (Test 1)
2. **Action:** Say a tool command, e.g.:
   - "Add 50 pounds of salmon from supplier Acme to freezer"
   - "Record a CCP monitoring for cooling with temperature 38 degrees"
3. **Expect:** Tool execution feedback appears
4. **Expect:** Tool result is spoken back by AI
5. **Expect:** Database record is created (check SQLite)

#### 🔊 Test 5: Audio Settings
1. Successfully connect to voice agent (Test 1)
2. Test audio controls at bottom of panel:
   - Toggle audio on/off
   - Adjust volume slider
3. **Expect:** Audio output responds to settings

---

## 🔍 Debugging

### Check Backend Health
```bash
curl http://localhost:3001/health | jq .
```

**Expected output:**
```json
{
  "status": "ok",
  "timestamp": "2025-10-25T...",
  "hasOpenAiKey": true
}
```

### Check Ephemeral Key Generation
```bash
curl -X POST http://localhost:3001/api/voice/ephemeral-key | jq .
```

**Expected output:**
```json
{
  "value": "ek_...",
  "expires_at": **********,
  "session": {
    "type": "realtime",
    "model": "gpt-realtime",
    ...
  }
}
```

### Browser Console Debugging
Open DevTools Console and look for:
- 🎤 Microphone permission logs
- 🔑 Ephemeral key logs
- 🔌 Connection logs
- 📝/🔊 AI response logs (text/audio deltas)

### Network Debugging
1. Open DevTools → Network tab
2. Filter by "WS" (WebSocket) or check for WebRTC connections
3. Look for:
   - POST to `/api/voice/ephemeral-key` (should return 200)
   - WebRTC peer connection establishment

---

## 📊 Success Criteria

- [ ] Connection establishes within 5-10 seconds (not 30 seconds)
- [ ] Microphone permission prompt appears immediately
- [ ] Console logs show all connection steps
- [ ] AI responds to voice input with audio
- [ ] Tool calls execute successfully
- [ ] Audio settings (volume, on/off) work correctly
- [ ] Error messages provide actionable guidance

---

## 🚀 Next Steps

### If Connection Works:
1. ✅ Mark Archon task "Investigate realtime agent not responding" as **done**
2. Test all voice command patterns (inventory, CCP monitoring, product status)
3. Test error handling scenarios (network loss, timeouts, invalid commands)
4. Verify tool execution end-to-end
5. Test audio quality and latency
6. Document any remaining issues

### If Connection Still Fails:
1. Check browser console for specific error messages
2. Verify network connectivity to api.openai.com
3. Test with different browsers (Chrome, Safari, Firefox)
4. Check firewall/VPN settings
5. Verify OpenAI API key has Realtime API access
6. Check OpenAI API status page for outages

---

## 📝 Technical Notes

### WebRTC Transport
The OpenAI Agents SDK uses WebRTC for browser-to-API connections:
- **Requires:** Microphone permissions (getUserMedia)
- **Protocol:** RTP over UDP (peer-to-peer)
- **Latency:** Lower than WebSocket (~50-100ms improvement)
- **Audio:** Automatic handling by SDK (no manual audio streaming needed)

### SDK Behavior
- `RealtimeSession.connect()` automatically:
  - Negotiates WebRTC connection (SDP/ICE)
  - Sets up audio input stream from microphone
  - Configures audio output for AI responses
  - Handles all protocol-level messages

### Microphone Permission Flow
1. Browser requires explicit user permission for microphone access
2. Permission prompt blocks getUserMedia() until user responds
3. If denied, all subsequent WebRTC connections will fail
4. Permission persists per-origin (localhost:8080)
5. Can be reset in browser settings

---

## 🎯 Related Files

- **src/components/VoiceAgent.tsx** - Main voice agent component (modified)
- **src/lib/openai-ephemeral-keys.ts** - Ephemeral key service
- **server.js** - Backend Express server for ephemeral keys
- **.env** - Environment variables (OPENAI_API_KEY)

---

## 🔗 References

- OpenAI Realtime API: https://platform.openai.com/docs/guides/realtime
- OpenAI Agents SDK: https://www.npmjs.com/package/@openai/agents
- WebRTC getUserMedia: https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia
